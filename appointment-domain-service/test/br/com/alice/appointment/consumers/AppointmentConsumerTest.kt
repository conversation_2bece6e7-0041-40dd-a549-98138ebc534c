package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentEvolutionService
import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.appointment.converters.toTimeline
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.ChannelId
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineAppendage
import br.com.alice.data.layer.models.TimelineAppendagesCategory
import br.com.alice.data.layer.models.TimelineAppendagesType
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineStatus
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.healthcondition.client.PersonCaseService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AppointmentConsumerTest : ConsumerTest() {

    private val timelineService: TimelineService = mockk()
    private val appointmentEvolutionService: AppointmentEvolutionService = mockk()
    private val personCaseService: PersonCaseService = mockk()
    private val consumer: AppointmentConsumer = AppointmentConsumer(timelineService, appointmentEvolutionService, personCaseService)

    @Test
    fun `#consumerAppointmentDeleted should create timeline by appointment`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(
            discardedType = AppointmentDiscardedType.NO_SHOW,
            status = AppointmentStatus.DISCARDED,
            type = AppointmentType.ANNOTATION,
        )

        val expectResult = Timeline(
            personId = appointment.personId,
            status = TimelineStatus.DISCARDED_NO_SHOW,
            staffId = appointment.staffId,
            title = appointment.type.description,
            type = TimelineType.APPOINTMENT_ANNOTATION,
            referencedModelId = appointment.id,
            referencedModelClass = TimelineReferenceModel.APPOINTMENT,
            referencedModelDate = appointment.createdAt,
            description = appointment.content,
        )

        val event = DraftAppointmentDeletedEvent(
            appointmentId = appointment.id,
            discardedType = appointment.discardedType!!,
            description = appointment.content,
            personId = appointment.personId,
            type = appointment.type,
            staffId = appointment.staffId,
            createdAt = appointment.createdAt,
            appointmentEvent = appointment.event
        )

        coEvery {
            timelineService.findOneBy(
                TimelineFilter(
                    referencedModelIds = listOf(appointment.id),
                    referencedModelClasses = listOf(TimelineReferenceModel.APPOINTMENT)
                )
            )
        } returns NotFoundException().failure()

        coEvery {
            timelineService.addAndNotify(match {
                it == Timeline(
                    personId = expectResult.personId,
                    staffId = expectResult.staffId,
                    description = expectResult.description,
                    type = expectResult.type,
                    title = expectResult.title,
                    status = expectResult.status,
                    referencedModelId = expectResult.referencedModelId,
                    referencedModelDate = expectResult.referencedModelDate,
                    referencedModelClass = expectResult.referencedModelClass,
                    providerUnitId = expectResult.providerUnitId,
                    id = it.id,
                    version = it.version,
                    updatedAt = it.updatedAt,
                    createdAt = it.createdAt,
                )
            })
        } returns expectResult.success()

        val result = consumer.consumerAppointmentDeleted(event)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(expectResult, "id", "createdAt", "updatedAt")

        coVerifyOnce {
            timelineService.addAndNotify(any())
            timelineService.findOneBy(any())
        }
    }

    @Test
    fun `#consumerAppointmentDeleted should update timeline by appointment`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(
            discardedType = AppointmentDiscardedType.NO_SHOW,
            status = AppointmentStatus.DISCARDED,
            type = AppointmentType.ANNOTATION,
            description = "description"
        )

        val expectResult = Timeline(
            personId = appointment.personId,
            status = TimelineStatus.DISCARDED_NO_SHOW,
            staffId = appointment.staffId,
            type = TimelineType.APPOINTMENT_ANNOTATION,
            referencedModelId = appointment.id,
            referencedModelClass = TimelineReferenceModel.APPOINTMENT,
            referencedModelDate = appointment.createdAt,
            description = appointment.content,
        )

        val event = DraftAppointmentDeletedEvent(
            appointmentId = appointment.id,
            discardedType = appointment.discardedType!!,
            description = appointment.content,
            personId = appointment.personId,
            type = appointment.type,
            staffId = appointment.staffId,
            createdAt = appointment.createdAt
        )

        coEvery {
            timelineService.findOneBy(
                TimelineFilter(
                    referencedModelIds = listOf(appointment.id),
                    referencedModelClasses = listOf(TimelineReferenceModel.APPOINTMENT)
                )
            )
        } returns expectResult.copy(status = TimelineStatus.DISCARDED_NO_SHOW, description = "era essa description")
            .success()

        coEvery {
            timelineService.updatedAndNotify(match {
                it == Timeline(
                    personId = expectResult.personId,
                    staffId = expectResult.staffId,
                    description = expectResult.description,
                    type = expectResult.type,
                    status = expectResult.status,
                    referencedModelId = expectResult.referencedModelId,
                    referencedModelDate = expectResult.referencedModelDate,
                    referencedModelClass = expectResult.referencedModelClass,
                    providerUnitId = expectResult.providerUnitId,
                    id = it.id,
                    version = it.version,
                    updatedAt = it.updatedAt,
                    createdAt = it.createdAt,
                )
            })
        } returns expectResult.success()

        val result = consumer.consumerAppointmentDeleted(event)

        assertThat(result).isSuccessWithDataIgnoringGivenFields(expectResult, "id", "createdAt", "updatedAt")

        coVerifyOnce {
            timelineService.updatedAndNotify(any())
            timelineService.findOneBy(any())
        }
    }

    @Test
    fun `#consumerAppointmentDeleted should not add timeline by appointment when reason isn't no show`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(
            discardedType = AppointmentDiscardedType.OTHERS,
            status = AppointmentStatus.DISCARDED,
            type = AppointmentType.ANNOTATION,
            description = "description"
        )

        val event = DraftAppointmentDeletedEvent(
            appointmentId = appointment.id,
            discardedType = appointment.discardedType!!,
            description = appointment.content,
            personId = appointment.personId,
            type = appointment.type,
            staffId = appointment.staffId,
            createdAt = appointment.createdAt
        )

        val result = consumer.consumerAppointmentDeleted(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyNone {
            timelineService.updatedAndNotify(any())
            timelineService.findOneBy(any())
        }
    }

    @Test
    fun `#consumerAppointmentCompleted should create timeline by appointment`() = runBlocking {
        val caseRecordDetails = CaseRecordDetails(
            description = DiseaseDetails(
                type = Disease.Type.CID_10,
                value = "10",
                description = "Unha Encravada",
            ),
            severity = CaseSeverity.ONGOING,
            caseId = RangeUUID.generate(),
            channel = ChannelId("ChannelIdCaseRecord", "test")
        )

        val personCase = TestModelFactory.buildPersonCase(
            id = caseRecordDetails.caseId!!
        )

        val appointment = TestModelFactory.buildAppointment(
            status = AppointmentStatus.FINISHED,
            type = AppointmentType.ANNOTATION,
            channelId = "ChannelIdField",
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val evolutions = TestModelFactory.buildAppointmentEvolution(
            appointmentId = appointment.id,
            staffId = appointment.staffId,
        )

        val appendage = TimelineAppendage(
            title = personCase.codeValue + " - " + personCase.codeDescription,
            id = personCase.id,
            category = TimelineAppendagesCategory.valueOf(personCase.codeType.name),
            type = TimelineAppendagesType.PERSON_CASE
        )

        val expectResult = Timeline(
            personId = appointment.personId,
            status = TimelineStatus.FINISHED,
            staffId = appointment.staffId,
            title = appointment.type.description,
            type = TimelineType.APPOINTMENT_ANNOTATION,
            referencedModelId = appointment.id,
            referencedModelClass = TimelineReferenceModel.APPOINTMENT,
            referencedModelDate = appointment.createdAt,
            description = appointment.content,
            channelIds = listOf("ChannelIdField", "ChannelIdCaseRecord"),
            evolutions = listOf(evolutions.toTimeline()),
            appendages = listOf(appendage)
        )

        val event = AppointmentCompletedEvent(appointment = appointment)

        coEvery {
            timelineService.findOneBy(
                TimelineFilter(
                    referencedModelIds = listOf(appointment.id),
                    referencedModelClasses = listOf(TimelineReferenceModel.APPOINTMENT)
                )
            )
        } returns NotFoundException().failure()

        coEvery {
            personCaseService.findByIds(listOf(personCase.id))
        } returns listOf(personCase).success()

        coEvery {
            appointmentEvolutionService.getByAppointment(appointment.id)
        } returns listOf(evolutions).success()

        coEvery {
            timelineService.addAndNotify(match {
                it == Timeline(
                    personId = expectResult.personId,
                    staffId = expectResult.staffId,
                    description = expectResult.description,
                    type = expectResult.type,
                    title = expectResult.title,
                    status = expectResult.status,
                    referencedModelId = expectResult.referencedModelId,
                    referencedModelDate = expectResult.referencedModelDate,
                    referencedModelClass = expectResult.referencedModelClass,
                    providerUnitId = expectResult.providerUnitId,
                    evolutions = expectResult.evolutions,
                    channelIds = expectResult.channelIds,
                    id = it.id,
                    version = it.version,
                    updatedAt = it.updatedAt,
                    createdAt = it.createdAt,
                    appendages = listOf(appendage),
                )
            })
        } returns expectResult.success()

        val result = consumer.consumerAppointmentCompleted(event)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(expectResult, "id", "createdAt", "updatedAt")

        coVerifyOnce {
            timelineService.addAndNotify(any())
            timelineService.findOneBy(any())
            personCaseService.findByIds(any())
        }
    }

    @Test
    fun `#consumerAppointmentCompleted should update timeline by appointment`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(
            status = AppointmentStatus.FINISHED,
            type = AppointmentType.ANNOTATION,
            description = "description",
            caseRecordDetails = null
        )

        val evolutions = TestModelFactory.buildAppointmentEvolution(
            appointmentId = appointment.id,
            staffId = appointment.staffId,
        )

        val expectResult = Timeline(
            personId = appointment.personId,
            status = TimelineStatus.FINISHED,
            staffId = appointment.staffId,
            type = TimelineType.APPOINTMENT_ANNOTATION,
            referencedModelId = appointment.id,
            referencedModelClass = TimelineReferenceModel.APPOINTMENT,
            referencedModelDate = appointment.createdAt,
            evolutions = listOf(evolutions.toTimeline()),
            description = appointment.content,
        )

        val event = AppointmentCompletedEvent(appointment = appointment)

        coEvery {
            timelineService.findOneBy(
                TimelineFilter(
                    referencedModelIds = listOf(appointment.id),
                    referencedModelClasses = listOf(TimelineReferenceModel.APPOINTMENT)
                )
            )
        } returns expectResult.copy(status = TimelineStatus.DISCARDED_NO_SHOW, description = "era essa description")
            .success()
        coEvery {
            appointmentEvolutionService.getByAppointment(appointment.id)
        } returns listOf(evolutions).success()

        coEvery {
            timelineService.updatedAndNotify(match {
                it == Timeline(
                    personId = expectResult.personId,
                    staffId = expectResult.staffId,
                    description = expectResult.description,
                    type = expectResult.type,
                    status = expectResult.status,
                    referencedModelId = expectResult.referencedModelId,
                    evolutions = expectResult.evolutions,
                    referencedModelDate = expectResult.referencedModelDate,
                    referencedModelClass = expectResult.referencedModelClass,
                    providerUnitId = expectResult.providerUnitId,
                    id = it.id,
                    version = it.version,
                    updatedAt = it.updatedAt,
                    createdAt = it.createdAt,
                )
            })
        } returns expectResult.success()

        val result = consumer.consumerAppointmentCompleted(event)

        assertThat(result).isSuccessWithDataIgnoringGivenFields(expectResult, "id", "createdAt", "updatedAt")

        coVerifyOnce {
            timelineService.updatedAndNotify(any())
        }
    }

}
