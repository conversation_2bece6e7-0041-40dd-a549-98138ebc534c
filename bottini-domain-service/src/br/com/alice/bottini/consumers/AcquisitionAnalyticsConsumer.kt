package br.com.alice.bottini.consumers

import br.com.alice.bottini.analytics.Segment
import br.com.alice.bottini.client.LeadQualificationService
import br.com.alice.bottini.client.LeadService
import br.com.alice.bottini.events.LeadCreatedEvent
import br.com.alice.bottini.events.SimulationFinishedEvent
import br.com.alice.common.logging.logger
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.LeadSource
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.membership.model.events.SignedContractEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestInProgressEvent
import br.com.alice.person.br.com.alice.person.model.events.FirstAccessEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.PersonCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class AcquisitionAnalyticsConsumer(
    private val leadQualifierService: LeadQualificationService,
    private val personService: PersonService,
    private val leadService: LeadService,
    private val memberService: MemberService,
) : Consumer() {

    private fun sendAliceOrDuquesaFromSource(source: LeadSource?) =
        if (source != LeadSource.DUQUESA) Brand.ALICE.name else source.name

    suspend fun leadCreated(event: LeadCreatedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val lead = event.payload
        logger.info(
            "Sending lead_created segment event in consumer",
            "lead_id" to lead.id
        )
        val targetInfo = leadQualifierService.qualify(lead).get()
        Segment.analytics.track(
            "lead_created",
            buildJsonObject {
                put("description", "Lead criado no back-end")
                put("brand", sendAliceOrDuquesaFromSource(lead.source))
                put("user_id", lead.id.toString())
                put("lead_id", lead.id.toString())
                put("banco_lead_created_at", lead.createdAt.toString())
                put("is_target", targetInfo.isTarget)
                put("is_imp", targetInfo.isIMP)
                put("is_anti_imp", targetInfo.isAntiIMP)
                put("is_super_target", targetInfo.isSuperTarget)
                if (lead.trackingInfo?.utmMedium != null) put("lead_utm_medium", lead.trackingInfo?.utmMedium)
                if (lead.trackingInfo?.utmSource != null) put("lead_utm_source", lead.trackingInfo?.utmSource)
                if (lead.trackingInfo?.utmCampaign != null) put("lead_utm_campaign", lead.trackingInfo?.utmCampaign)
                if (lead.trackingInfo?.utmContent != null) put("lead_utm_content", lead.trackingInfo?.utmContent)
                if (lead.trackingInfo?.utmCampaignId != null) put(
                    "lead_utm_campaign_id",
                    lead.trackingInfo?.utmCampaignId
                )
                if (lead.trackingInfo?.utmAdSetId != null) put("lead_utm_ad_set_id", lead.trackingInfo?.utmAdSetId)
                if (lead.trackingInfo?.utmAdId != null) put("lead_utm_ad_id", lead.trackingInfo?.utmAdId)
                if (lead.trackingInfo?.utmTerm != null) put("lead_utm_term", lead.trackingInfo?.utmTerm)
                if (lead.trackingInfo?.origin != null) put("lead_origin", lead.trackingInfo?.origin.toString())
                if (lead.trackingInfo?.anonymousId != null) put("anonymous_id", lead.trackingInfo?.anonymousId)
            }
        )
        true.success()
    }

    suspend fun simulationFinished(event: SimulationFinishedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val lead = event.payload.lead
        val simulation = event.payload.simulation

        logger.info(
            "Sending simulation_finished segment event in consumer",
            "lead_id" to lead.id,
            "simulation_id" to simulation.id
        )
        Segment.analytics.track(
            "simulation_finished",
            buildJsonObject {
                put("description", "Simulação finalizada no back-end")
                put("user_id", lead.id.toString())
                put("lead_id", lead.id.toString())
                put("simulation_id", simulation.id.toString())
                put("banco_simulação_created_at", simulation.createdAt.toString())
                put("type", simulation.type.toString())
                if (lead.trackingInfo?.anonymousId != null) put("anonymous_id", lead.trackingInfo?.anonymousId)
            }
        )
        true.success()
    }


    suspend fun personCreated(event: PersonCreatedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val person = event.payload

        if (person.leadId != null) {
            val lead = leadService.get(person.leadId!!).get()
            logger.info(
                "Sending person_created segment event in consumer",
                "person_id" to person.id,
                "lead_id" to person.leadId
            )
            Segment.analytics.track(
                "person_created",
                buildJsonObject {
                    put("description", "Person criada no back-end")
                    put("user_id", person.leadId.toString())
                    put("lead_id", person.leadId.toString())
                    put("person_id", person.id.toString())
                    put("brand", sendAliceOrDuquesaFromSource(lead.source))
                }
            )
        } else {
            logger.info(
                "person_created segment event was not send, person without leadId",
                "person_id" to person.id,
            )
        }

        true.success()
    }

    suspend fun memberActivated(event: MemberActivatedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val member = event.payload
        val person = personService.get(member.personId).get()

        if (person.leadId != null) {
            val membership = memberService.getCurrent(person.id).get()
            logger.info(
                "Sending member_activated segment event in consumer",
                "member_id" to member.id,
                "person_id" to person.id,
                "lead_id" to person.leadId
            )
            Segment.analytics.track(
                "member_activated",
                buildJsonObject {
                    put("description", "Member ativado no back-end")
                    put("user_id", person.leadId.toString())
                    put("lead_id", person.leadId.toString())
                    put("person_id", person.id.toString())
                    put("member_id", member.id.toString())
                    put("brand", membership.brand?.name)
                }
            )
        } else {
            logger.info(
                "member_activated segment event was not send, member without leadId",
                "member_id" to member.id,
                "person_id" to person.id,
            )
        }
        true.success()
    }

    suspend fun firstAccess(event: FirstAccessEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val login = event.payload
        val person = personService.get(login.personId).get()
        if (person.leadId != null) {
            val lead = leadService.get(person.leadId!!).get()
            logger.info(
                "Sending first_access segment event in consumer",
                "person_id" to person.id,
                "lead_id" to person.leadId
            )
            Segment.analytics.track(
                "first_access",
                buildJsonObject {
                    put("description", "Primeiro login no app")
                    put("person_id", person.id.toString())
                    put("lead_id", person.leadId.toString())
                    put("user_id", person.leadId.toString())
                    put("brand", sendAliceOrDuquesaFromSource(lead.source))
                }
            )
        } else {
            logger.info(
                "member_activated segment event was not send, member without leadId",
                "person_id" to person.id
            )
        }

        true.success()
    }

    suspend fun shoppingFinished(event: ShoppingFinishedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val payload = event.payload
        val person = personService.get(payload.productOrder.personId).get()
        if (person.leadId != null) {
            val lead = leadService.get(person.leadId!!).get()
            logger.info(
                "Sending shopping_finished segment event in consumer",
                "person_id" to person.id,
                "lead_id" to person.leadId
            )
            Segment.analytics.track(
                "shopping_finished",
                buildJsonObject {
                    put("description", "Shopping finalizado")
                    put("person_id", person.id.toString())
                    put("lead_id", person.leadId.toString())
                    put("user_id", person.leadId.toString())
                    put("brand", sendAliceOrDuquesaFromSource(lead.source))
                }
            )
        } else {
            logger.info(
                "shopping_finished segment event was not send, member without leadId",
                "person_id" to person.id
            )
        }

        true.success()
    }

    suspend fun registrationFinished(event: PersonRegistrationFinishedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val person = event.payload.person
            if (person.leadId != null) {
                val lead = leadService.get(person.leadId!!).get()
                logger.info(
                    "Sending registration_finished segment event in consumer",
                    "person_id" to person.id,
                    "lead_id" to person.leadId
                )
                Segment.analytics.track(
                    "registration_finished",
                    buildJsonObject {
                        put("description", "Registration finalizado")
                        put("person_id", person.id.toString())
                        put("lead_id", person.leadId.toString())
                        put("user_id", person.leadId.toString())
                        put("brand", sendAliceOrDuquesaFromSource(lead.source))
                    }
                )
            } else {
                logger.info(
                    "registration_finished segment event was not send, member without leadId",
                    "person_id" to person.id
                )
            }

            true.success()
        }

    suspend fun invoiceSent(event: InvoicePaymentCreatedEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        if (event.payload.paymentReason != PaymentReason.FIRST_PAYMENT) return@withSubscribersEnvironment true.success()
        if (event.payload.member == null) {
            logger.info(
                "invoice_sent not sent, event without member",
                "member_invoice_id" to event.payload.invoicePayment.memberInvoiceId
            )
            return@withSubscribersEnvironment true.success()
        }

        val personId = event.payload.member!!.personId
        val person = personService.get(personId).get()

        if (person.leadId != null) {
            val membership = memberService.getCurrent(person.id).get()
            logger.info(
                "Sending invoice_sent segment event in consumer",
                "person_id" to person.id,
                "lead_id" to person.leadId
            )
            Segment.analytics.track(
                "invoice_sent",
                buildJsonObject {
                    put("description", "Fatura enviada")
                    put("person_id", person.id.toString())
                    put("lead_id", person.leadId.toString())
                    put("user_id", person.leadId.toString())
                    put("brand", membership.brand?.name)
                }
            )
        } else {
            logger.info(
                "invoice_sent segment event was not send, member without leadId",
                "person_id" to person.id
            )
        }

        true.success()
    }

    suspend fun portabilityRequested(event: InsurancePortabilityRequestInProgressEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val payload = event.payload
            val person = personService.get(payload.request.personId).get()
            if (person.leadId != null) {
                val lead = leadService.get(person.leadId!!).get()
                logger.info(
                    "Sending portability_requested segment event in consumer",
                    "person_id" to person.id,
                    "lead_id" to person.leadId
                )
                Segment.analytics.track(
                    "portability_requested",
                    buildJsonObject {
                        put("description", "Portabilidade solicitada")
                        put("person_id", person.id.toString())
                        put("lead_id", person.leadId.toString())
                        put("user_id", person.leadId.toString())
                        put("brand", sendAliceOrDuquesaFromSource(lead.source))
                    }
                )
            } else {
                logger.info(
                    "portability_requested segment event was not send, member without leadId",
                    "person_id" to person.id
                )
            }

            true.success()
        }

    suspend fun contractSigned(event: SignedContractEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val contract = event.payload
        val person = personService.get(contract.personId).get()

        logger.info(
            "Sending contract_sent segment event in consumer",
            "contract_id" to contract.id,
            "person_id" to contract.personId,
            "lead_id" to person.leadId,
        )
        val json = buildJsonObject {
            put("description", "Contrato foi assinado")
            put("contract_id", contract.id.toString())
            put("person_id", contract.personId.toString())
            put("lead_id", person.leadId.toString())
            put("user_id", person.leadId.toString())
        }

        Segment.analytics.track(
            "signed_contract",
            json,
        )

        logger.info(
            "Event contract_sent segment was send",
            "person_id" to person.id,
            "lead_id" to person.leadId,
            "json" to json,
        )
        true.success()
    }
}
