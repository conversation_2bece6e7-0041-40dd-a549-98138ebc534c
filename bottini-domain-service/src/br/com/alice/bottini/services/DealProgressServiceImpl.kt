package br.com.alice.bottini.services

import br.com.alice.bottini.client.DealProgressService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.sales.b2c.Deal
import br.com.alice.communication.crm.sales.b2c.DealPipeline
import br.com.alice.communication.crm.sales.b2c.DealStage
import br.com.alice.communication.crm.sales.b2c.SalesCrmPipeline
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.DealSalesInfo
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InsurancePortabilityRequest
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonLogin
import br.com.alice.data.layer.models.ProductOrder
import br.com.alice.data.layer.models.ProductType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.PersonPayload
import br.com.alice.product.client.ProductService
import br.com.alice.schedule.model.events.AppointmentSchedulePayload
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class DealProgressServiceImpl(
    private val salesCrmPipeline: SalesCrmPipeline,
    private val personSalesService: PersonSalesService,
    private val onboardingService: OnboardingService,
    private val dealSalesInfoService: DealSalesInfoService,
    private val productService: ProductService,
    private val promoCodeService: PromoCodeService,
    private val personService: PersonService,
): DealProgressService {

    override suspend fun registerFirstAccessOnDeal(login: PersonLogin): Result<Any, Throwable> {
        logger.info(
            "DealProgressServiceImpl.registerFirstAccessOnDeal: first access event received, updating deal",
            "person_id" to login.personId
        )

        val person = personService.get(login.personId).get()
        if (person.isDuquesa) return true.success()

        val deal = Deal(
            email = login.email,
            firstAccess = LocalDateTime.now(),
        )

        return updateDeal(person.id, deal, person)
    }

    override suspend fun moveDealStageOnShoppingFinished(
        productOrder: ProductOrder,
        promoCodeId: UUID?
    ): Result<Any, Throwable> {
        val promoCode = promoCodeId?.let { promoCodeService.get(it).getOrNullIfNotFound() }
        val product = productService.getProduct(productOrder.productId).getOrNullIfNotFound()

        val deal = Deal(
            stage = DealStage.SHOPPING_FINISHED,
            amount = productOrder.price,
            productName = product?.title.orEmpty(),
            promoCodeType = promoCode?.type?.toString(),
            promoCode = promoCode?.code
        )

        return updateDeal(productOrder.personId, deal)
    }

    override suspend fun moveDealOnPortabilitySubmitted(request: InsurancePortabilityRequest): Result<Any, Throwable> {
        val deal = Deal(
            stage = DealStage.WAITING_PORTABILITY,
            portabilityType = request.type?.toString(),
            portabilityStep = request.step?.toString(),
            portabilityStatus = request.status.toString(),
            portabilityMinPeriod = request.getAnswer(InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD),
            portabilityHospitals = request.getAnswer(InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE_HOSPITALS),
            portabilityInsuranceName = request.getAnswer(InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE)
        )

        return updateDeal(request.personId, deal)
    }

    override suspend fun moveDealOnPortabilityDecline(request: InsurancePortabilityRequest): Result<Any, Throwable> {
        val deal = Deal(
            stage = DealStage.SHOPPING_FINISHED,
            portabilityStep = request.step?.toString(),
            portabilityStatus = request.status.toString(),
            portabilityDeclineReason = request.declineReason?.toString(),
            portabilityDeclineReasons = request.declinedReasons?.joinToString()
        )

        return updateDeal(request.personId, deal)
    }

    override suspend fun moveDealOnPortabilityInProgress(request: InsurancePortabilityRequest): Result<Any, Throwable> {
        val deal = Deal(
            stage = DealStage.WAITING_PORTABILITY_INFO,
            portabilityStep = request.step?.toString(),
            portabilityStatus = request.status.toString(),
        )
        return updateDeal(request.personId, deal)
    }

    override suspend fun moveDealStageOnRegistrationFinished(personId: PersonId): Result<Any, Throwable> {
        val deal = Deal(stage = DealStage.HEALTH_DECLARATION_APPOINTMENT)
        return updateDeal(personId, deal)
    }

    override suspend fun moveDealStageOnContractStarted(personId: PersonId): Result<Any, Throwable> {
        val deal = Deal(stage = DealStage.CONTRACT_RECEIVED)

        return updateDeal(personId, deal)
    }

    override suspend fun moveDealOnInvoiceSent(personId: PersonId): Result<Any, Throwable> {
        val deal = Deal(stage = DealStage.INVOICE_SENT)
        return updateDeal(personId, deal)
    }

    override suspend fun moveDealOnMemberActivated(member: Member): Result<Any, Throwable> {
        val b2bProduct = member.selectedProduct.type == ProductType.B2B

        val deal = if (b2bProduct) Deal(
            stage = DealStage.LOST_LEAD_B2C,
            isEnterpriseSale = true
        ) else Deal(stage = DealStage.MEMBER_ACTIVATED)

        return updateDeal(member.personId, deal)
    }

    override suspend fun addCancellationDataToDeal(member: Member): Result<Any, Throwable> {
        val deal = Deal(
            stage = DealStage.MEMBER_CANCELED,
            pipeline = DealPipeline.DEFAULT,
            dataDoCancelamento = LocalDateTime.now().toLocalDate()?.atBeginningOfTheDay(),
            motivoDeCancelamentoDoMembro = if (personService.get(member.personId).get()
                    .containsTag("cancelamento_inadimplencia")
            ) {
                "inadimplencia"
            } else {
                "outros"
            }
        )
        return updateDeal(member.personId, deal)
    }

    override suspend fun moveDealOnAppointmentCreated(
        appointmentSchedule: AppointmentSchedule,
        personId: PersonId
    ): Result<Any, Throwable> =
        if (appointmentSchedule.type == AppointmentScheduleType.HEALTH_DECLARATION) {
            val deal = Deal(
                stage = DealStage.SCHEDULED_HEALTH_DECLARATION_APPOINTMENT,
                healthDeclarationAppointmentStartDatetime = appointmentSchedule.startTime,
                healthDeclarationAppointmentEndDatetime = appointmentSchedule.endTime,
                healthDeclarationAppointmentCreatedAt = appointmentSchedule.createdAt,
                healthDeclarationAppointmentStatus = appointmentSchedule.status.toString(),
            )
            updateDeal(personId, deal)
        } else {
            logger.info("DealProgressServiceImpl.moveDealOnAppointmentCreated: ignoring non health declaration appointment schedule")
            true.success()
        }

    override suspend fun updateDealOnPersonEvent(person: PersonPayload): Result<Any, Throwable> {
        val deal = Deal(
            email = person.email,
            dateOfBirth = person.dateOfBirth,
            nationalId = person.nationalId,
            name = "${person.firstName} ${person.lastName}",
            firstName = person.firstName,
            lastName = person.lastName,
            phoneNumber = person.phoneNumber,
            isInternal = person.tags?.contains("internal")
        )

        return updateDeal(person.id, deal)
    }

    override suspend fun moveDealToNoShow(schedule: AppointmentSchedulePayload): Result<Any, Throwable> {
        return if (schedule.type == AppointmentScheduleType.HEALTH_DECLARATION) {
            val deal = Deal(
                stage = DealStage.HEALTH_DECLARATION_APPOINTMENT_NO_SHOW
            )
            updateDeal(schedule.personId, deal)
        } else {
            logger.info("DealProgressServiceImpl.moveDealToNoShow: ignoring non health declaration appointment schedule")
            true.success()
        }
    }

    private suspend fun updateDeal(
        personId: PersonId,
        deal: Deal,
        person: Person? = null
    ): Result<Any, Throwable> {
        val hasFinishedGasProcess = onboardingService.findByPerson(personId).getOrNullIfNotFound()?.hasFinished ?: false
        val finalStages = listOf(
            DealStage.MEMBER_ACTIVATED,
            DealStage.MEMBER_CANCELED,
            DealStage.LOST_LEAD_B2C
        )
        val ongoingDeal = deal.stage !in finalStages

        if (hasFinishedGasProcess && ongoingDeal) {
            logger.info(
                "DealProgressServiceImpl.moveDealToNoShow: ignoring Hubspot event, member already active.",
                "deal" to deal,
                "personId" to personId
            )
            return true.success()
        }

        val person = person ?: personService.get(personId).get()
        person.opportunityId?.let { dealSalesInfoService.findByLastOpportunityId(it)?.hubspotDealId }
            ?: person.leadId?.let {
                val personSalesInfo = personSalesService.findByLeadId(it)
                val hubspotDealId = personSalesInfo?.hubspotDealId
                hubspotDealId?.let {
                    dealSalesInfoService.add(
                        DealSalesInfo(
                            personId = personId,
                            lastOpportunityId = person.opportunityId,
                            hubspotDealId = hubspotDealId,
                        )
                    )
                    personSalesService.update(personSalesInfo.copy(hubspotDealId = null))
                }
                hubspotDealId
            }

        logger.info(
            "DealProgressServiceImpl.updateDeal: old hubspot integration is disabled",
            "deal" to deal
        )

        return true.success()
    }
}
