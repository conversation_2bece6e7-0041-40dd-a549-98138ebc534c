package br.com.alice.bottini.consumers

import br.com.alice.bottini.client.DealProgressService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.moneyin.event.InvoiceSentEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestDeclinedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestInProgressEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestSubmittedEvent
import br.com.alice.person.br.com.alice.person.model.events.FirstAccessEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.PersonCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class DealProgressConsumerTest : ConsumerTest() {

    private val dealProgressService: DealProgressService = mockk()

    private val consumer = DealProgressConsumer(
        dealProgressService
    )

    private val leadId = RangeUUID.generate()
    private val person = TestModelFactory.buildPerson(opportunityId = RangeUUID.generate(), leadId = leadId)
    private val product = TestModelFactory.buildProduct()
    private val productOrder = TestModelFactory.buildProductOrder(product = product, personId = person.id)
    private val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(person.id)

    @BeforeTest
    override fun setup() {
        super.setup()
    }

    @Test
    fun `#firstAccess should return true`() = runBlocking<Unit> {
        val personLogin = TestModelFactory.buildPersonLogin(personId = person.id)
        val event = FirstAccessEvent(personLogin)

        coEvery { dealProgressService.registerFirstAccessOnDeal(personLogin) } returns true.success()

        val response = consumer.firstAccess(event)

        ResultAssert.assertThat(response).isSuccess()
    }

    @Test
    fun `#updateDealOnPersonCreated should return true`() = runBlocking {
        val event = PersonCreatedEvent(person)
        coEvery {
            dealProgressService.updateDealOnPersonEvent(event.payload)
        } returns true.success()

        val response = consumer.updateDealOnPersonCreated(event)

        ResultAssert.assertThat(response).isSuccess()

        coVerifyOnce { dealProgressService.updateDealOnPersonEvent(any()) }
    }

    @Test
    fun `#updateDealOnPersonUpdated should return true`() = runBlocking {
        val event = PersonUpdatedEvent(person)
        coEvery {
            dealProgressService.updateDealOnPersonEvent(event.payload)
        } returns true.success()

        val response = consumer.updateDealOnPersonUpdated(event)
        ResultAssert.assertThat(response).isSuccess()
        coVerifyOnce { dealProgressService.updateDealOnPersonEvent(any()) }
    }

    @Test
    fun `#moveDealStageOnShoppingFinished should return true`() = runBlocking<Unit> {
        coEvery {
            dealProgressService.moveDealStageOnShoppingFinished(productOrder, null)
        } returns true.success()

        val response = consumer.moveDealStageOnShoppingFinished(ShoppingFinishedEvent(productOrder))
        ResultAssert.assertThat(response).isSuccess()
    }


    @Test
    fun `#moveDealStageOnContractStarted should return true`() = runBlocking<Unit> {
        val event = ContractPhaseStartedEvent(person.id)

        coEvery {
            dealProgressService.moveDealStageOnContractStarted(person.id)
        } returns true.success()

        val result = consumer.moveDealStageOnContractStarted(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnPortabilitySubmitted should return true`() = runBlocking<Unit> {
        val event = InsurancePortabilityRequestSubmittedEvent(insurancePortabilityRequest)

        coEvery {
            dealProgressService.moveDealOnPortabilitySubmitted(insurancePortabilityRequest)
        } returns true.success()

        val result = consumer.moveDealOnPortabilitySubmitted(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnPortabilityDecline should return true`() = runBlocking<Unit> {
        val event = InsurancePortabilityRequestDeclinedEvent(insurancePortabilityRequest)

        coEvery {
            dealProgressService.moveDealOnPortabilityDecline(insurancePortabilityRequest)
        } returns true.success()

        val result = consumer.moveDealOnPortabilityDecline(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnPortabilityInProgress should return true`() = runBlocking<Unit> {
        val event = InsurancePortabilityRequestInProgressEvent(insurancePortabilityRequest)

        coEvery {
            dealProgressService.moveDealOnPortabilityInProgress(insurancePortabilityRequest)
        } returns true.success()

        val result = consumer.moveDealOnPortabilityInProgress(event = event)

        ResultAssert.assertThat(result).isSuccess()

    }

    @Test
    fun `#moveDealStageOnRegistrationFinished should return true`() = runBlocking<Unit> {
        val event = PersonRegistrationFinishedEvent(person)

        coEvery {
            dealProgressService.moveDealStageOnRegistrationFinished(person.id)
        } returns true.success()

        val result = consumer.moveDealStageOnRegistrationFinished(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnAppointmentCreated should return true`() = runBlocking<Unit> {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id)
        val event = AppointmentScheduleCreatedEvent(person, appointmentSchedule)

        coEvery {
            dealProgressService.moveDealOnAppointmentCreated(appointmentSchedule, person.id)
        } returns true.success()

        val result = consumer.moveDealOnAppointmentCreated(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnAppointmentCancellation should return true`() = runBlocking<Unit> {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id)
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule)

        coEvery {
            dealProgressService.moveDealToNoShow(event.payload)
        } returns true.success()

        val result = consumer.moveDealOnAppointmentCancellation(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnAppointmentNoShow should return true`() = runBlocking<Unit> {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id)
        val event = AppointmentScheduleNoShowEvent(appointmentSchedule)
        coEvery {
            dealProgressService.moveDealToNoShow(event.payload)
        } returns true.success()

        val result = consumer.moveDealOnAppointmentNoShow(event = event)

        ResultAssert.assertThat(result).isSuccess()

    }

    @Test
    fun `#moveDealOnInvoiceSent should return true`() = runBlocking<Unit> {
        val memberInvoice = TestModelFactory.buildMemberInvoice()
        val event = InvoiceSentEvent(person, memberInvoice)

        coEvery {
            dealProgressService.moveDealOnInvoiceSent(person.id)
        } returns true.success()

        val result = consumer.moveDealOnInvoiceSent(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `#moveDealOnMemberActivated should return true`() = runBlocking<Unit> {
        val member = TestModelFactory.buildMember(person.id)
        val event = MemberActivatedEvent(member)

        coEvery {
            dealProgressService.moveDealOnMemberActivated(member)
        } returns true.success()

        val result = consumer.moveDealOnMemberActivated(event = event)

        ResultAssert.assertThat(result).isSuccess()
    }

}
