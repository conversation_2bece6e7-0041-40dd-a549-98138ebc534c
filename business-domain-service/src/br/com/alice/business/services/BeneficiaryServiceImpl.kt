package br.com.alice.business.services

import br.com.alice.authentication.currentUserId
import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.converters.BeneficiaryConverter.toNewPerson
import br.com.alice.business.converters.BeneficiaryConverter.toNewPristineBeneficiary
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.BeneficiaryArchivedEvent
import br.com.alice.business.events.BeneficiaryCanceledEvent
import br.com.alice.business.events.BeneficiaryChangedEvent
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.business.events.BeneficiaryCreatedForActiveMemberEvent
import br.com.alice.business.events.BeneficiaryUpdatedEvent
import br.com.alice.business.events.NewBeneficiaryRelationshipAssignedEvent
import br.com.alice.business.events.RequestCassiMemberUpdateEvent
import br.com.alice.business.exceptions.AlreadyAliceMemberException
import br.com.alice.business.exceptions.BeneficiaryActivationValidationException
import br.com.alice.business.exceptions.BeneficiaryAlreadyCanceledException
import br.com.alice.business.exceptions.BeneficiaryAlreadyExistsException
import br.com.alice.business.exceptions.BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany
import br.com.alice.business.exceptions.BeneficiaryMustHaveSubContractIdException
import br.com.alice.business.exceptions.CompanySubcontractRequiredException
import br.com.alice.business.exceptions.EmptyAvailableProductListException
import br.com.alice.business.exceptions.FailedToInferSubcontractException
import br.com.alice.business.exceptions.HolderIsDependentException
import br.com.alice.business.exceptions.InvalidActivationCancellationStateException
import br.com.alice.business.exceptions.InvalidBeneficiaryCanceledRequest
import br.com.alice.business.exceptions.MismatchCompanySubcontractException
import br.com.alice.business.exceptions.NationalIdAlreadyUsedByHolderException
import br.com.alice.business.exceptions.SubcontractIsDifferentFromHolderException
import br.com.alice.business.logics.BeneficiaryLogic
import br.com.alice.business.logics.BeneficiaryLogic.archive
import br.com.alice.business.logics.BeneficiaryLogic.buildGetBeneficiaryQueryFilter
import br.com.alice.business.logics.BeneficiaryLogic.extractHolderFromDependents
import br.com.alice.business.logics.BeneficiaryLogic.filterBeforeActivatedAt
import br.com.alice.business.logics.BeneficiaryLogic.mountBeneficiary
import br.com.alice.business.logics.BeneficiaryLogic.validateAndAbortCancel
import br.com.alice.business.logics.BeneficiaryLogic.validateAndUpdateCancelFields
import br.com.alice.business.logics.BeneficiaryLogic.validateBeneficiary
import br.com.alice.business.logics.BeneficiaryLogic.withDependents
import br.com.alice.business.logics.BeneficiaryLogic.withOnboarding
import br.com.alice.business.logics.CompanyLogic.getInitialProductId
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.business.metrics.BeneficiaryMetric.Status.FAILURE
import br.com.alice.business.metrics.BeneficiaryMetric.Status.SUCCESS
import br.com.alice.business.metrics.BeneficiaryMetric.metrifyBeneficiaryCanceled
import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.business.model.toBeneficiary
import br.com.alice.business.model.toPerson
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.CompanyContractModel
import br.com.alice.data.layer.models.CompanyModel
import br.com.alice.data.layer.models.CompanySubContractModel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberLifeCycleEvents
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.services.BeneficiaryModelDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.onboarding.GasProcessService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class BeneficiaryServiceImpl(
    private val beneficiaryDataService: BeneficiaryModelDataService,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
    private val kafkaProducerService: KafkaProducerService,
    private val companyService: CompanyService,
    private val memberService: MemberService,
    private val productService: ProductService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val personService: PersonService,
    private val gasProcessService: GasProcessService,
    private val cassiMemberService: CassiMemberService,
    private val onboardingService: OnboardingService,
    private val contractService: CompanyContractService,
    private val subcontractService: CompanySubContractService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val preActivationPaymentService: PreActivationPaymentService
) : BeneficiaryService {

    companion object {
        const val CHUNK_GROUP_SIZE = 500
        const val DISABLE_CREATED_AT_CHECKING_FROM_CASSI_NUMBER_ACCOUNT_TRIGGER =
            "disable_created_at_checking_from_cassi_number_account_trigger"
    }

    override suspend fun addBeneficiaryForExistentMember(
        beneficiary: Beneficiary,
        initialProductId: UUID,
        flowType: BeneficiaryOnboardingFlowType,
        phase: BeneficiaryOnboardingPhaseType,
        ignoreHiredAtValidation: Boolean
    ) = beneficiary.toModel().validateAndAdd(initialProductId, ignoreHiredAtValidation)
        .then { logger.info("Beneficiary Created", "beneficiary_id" to it.id) }
        .map { it.toTransport() }
        .then {
            kafkaProducerService.produce(
                BeneficiaryCreatedEvent(it, initialProductId, flowType, phase),
                it.memberId.toString()
            )
        }

    override suspend fun updateDependentWithNewParent(
        dependentWithOnboarding: Beneficiary,
        newParent: Beneficiary,
    ) = coResultOf<Beneficiary, Throwable> {
        if (dependentWithOnboarding.parentBeneficiary == newParent.id) {
            logger.info(
                "Returning the beneficiary register when new parent is equal dependent parent beneficiary",
                "dependent_beneficiary_id" to dependentWithOnboarding.id,
                "dependent_beneficiary_parent_beneficiary" to dependentWithOnboarding.parentBeneficiary,
                "new_parent_id" to newParent.id,
            )

            dependentWithOnboarding
        } else {
            memberService.get(dependentWithOnboarding.memberId)
                .map {
                    if (it.active) createNewMembership(
                        dependentWithOnboarding.toModel(), newParent.toModel()
                    ).toTransport()
                    else if (it.isPending) updateParentRelationship(
                        dependentWithOnboarding.toModel(), newParent.toModel()
                    ).toTransport()
                    else dependentWithOnboarding
                }.get()
        }
    }

    private suspend fun updateParentRelationship(
        dependentWithOnboarding: BeneficiaryModel,
        newParent: BeneficiaryModel
    ) = beneficiaryDataService.update(dependentWithOnboarding.copy(parentBeneficiary = newParent.id)).then {
        kafkaProducerService.produce(
            BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.UPDATED),
            it.id.toString()
        )
    }.get()

    private suspend fun createNewMembership(
        dependentWithOnboarding: BeneficiaryModel,
        newParent: BeneficiaryModel,
    ) =
        (if (dependentWithOnboarding.companySubContractId != newParent.companySubContractId) memberService.get(newParent.memberId)
            .get().activationDate else null)
            .let { activationDate ->
                memberService.cancelAndCreateNewMember(
                    dependentWithOnboarding.memberId,
                    activationDate
                )
            }
            .map { dependentWithOnboarding.toNewMembershipBeneficiary(it.id, it.status).withNewParent(newParent) }
            .flatMap {
                logger.info("Beneficiary::createNewMembership", "b" to it)

                it.addAndProduceEvent(
                    it.onboarding!!.initialProductId,
                    it.onboarding!!.flowType,
                    it.onboarding!!.currentPhase!!.phase
                )
            }
            .then {
                kafkaProducerService.produce(
                    NewBeneficiaryRelationshipAssignedEvent(dependentWithOnboarding.toTransport(), it.toTransport())
                )
            }
            .then {
                logger.info(
                    "New Beneficiary Relationship Assigned",
                    "dependent_beneficiary_id" to it.id,
                    "parent_beneficiary_id" to newParent.id
                )
            }.get()

    override suspend fun archiveBeneficiaryById(id: UUID) = beneficiaryDataService.get(id)
        .map { it.archive() }
        .flatMap { beneficiaryDataService.update(it) }
        .map { it.toTransport() }
        .then {
            kafkaProducerService.produce(BeneficiaryArchivedEvent(it))
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(it, NotificationEventAction.UPDATED),
                it.id.toString()
            )
        }

    override suspend fun canPersonHaveThisProduct(personId: PersonId, product: Product): Result<Product, Throwable> =
        when (product.isB2BOrADESAO) {
            true -> memberService.getCurrent(personId)
                .flatMap { findByMemberId(it.id) }
                .flatMapPair { beneficiary ->
                    companyProductPriceListingService.findCpplProductIdsForSubContract(
                        beneficiary.companySubContractId
                            ?: throw BeneficiaryMustHaveSubContractIdException(
                                beneficiary.id,
                                null,
                                beneficiary.companyId
                            )
                    )
                }
                .flatMap { (cpplProducts, beneficiary) ->
                    when (cpplProducts.contains(product.id)) {
                        true -> product.success()
                        false -> BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany(
                            personId,
                            beneficiary.companySubContractId!!,
                            product.id
                        ).failure()
                    }
                }

            else -> product.success()
        }

    override suspend fun reactivateMembership(beneficiaryId: UUID) =
        get(beneficiaryId, FindOptions(withOnboarding = true))
            .flatMap { reactivateMemberByBeneficiary(it.toModel()) }
            .map { it.toTransport() }

    override suspend fun reactivateMembershipByMember(memberId: UUID, params: MemberLifeCycleEvents?) =
        findByMemberId(memberId, FindOptions(withOnboarding = true))
            .flatMap { reactivateMemberByBeneficiary(it.toModel(), params) }
            .map { it.toTransport() }

    private suspend fun reactivateMemberByBeneficiary(
        beneficiaryWithOnboarding: BeneficiaryModel,
        params: MemberLifeCycleEvents? = null
    ): Result<BeneficiaryModel, Throwable> {
        logger.info(
            "BeneficiaryServiceImpl.handleReactivationWorkflow",
            "person_id" to beneficiaryWithOnboarding.personId,
            "member_id" to beneficiaryWithOnboarding.id,
            "params" to params
        )
        return when (params) {
            null -> handleDuplicateBeneficiaryReactivation(beneficiaryWithOnboarding)
            else -> handleOriginalBeneficiaryReactivation(beneficiaryWithOnboarding, params)
        }
    }

    private suspend fun handleOriginalBeneficiaryReactivation(
        beneficiary: BeneficiaryModel,
        params: MemberLifeCycleEvents
    ): Result<BeneficiaryModel, Throwable> =
        removeCancellationParameters(beneficiary)
            .flatMap {
                produceReactivationEvent(it, BeneficiaryUpdatedEvent(it.toTransport()))
                beneficiary.success()
            }
            .flatMap {
                memberService.reactivateMemberById(it.memberId, params)
                beneficiary.success()
            }

    private suspend fun removeCancellationParameters(beneficiary: BeneficiaryModel) = beneficiaryDataService.update(
        beneficiary.copy(
            canceledAt = null,
            canceledDescription = null,
            canceledReason = null,
            memberStatus = null
        )
    ).then {
        kafkaProducerService.produce(
            BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.UPDATED),
            it.id.toString()
        )
    }

    private suspend fun handleDuplicateBeneficiaryReactivation(beneficiaryWithOnboarding: BeneficiaryModel): Result<BeneficiaryModel, Throwable> =
        memberService.get(beneficiaryWithOnboarding.memberId)
            .flatMap { beneficiaryWithOnboarding.validateBeneficiary(it.productId) }
            .flatMapPair { memberService.reactivateMemberById(it.memberId) }
            .map { (member, beneficiary) ->
                Pair(
                    beneficiary.toNewPristineBeneficiary(member.id, member.status),
                    member.productId
                )
            }
            .flatMap { (beneficiary, productId) ->
                beneficiary.addAndProduceEvent(
                    productId,
                    beneficiary.onboarding!!.flowType,
                    beneficiary.onboarding!!.currentPhase!!.phase
                )
            }

    private suspend fun produceReactivationEvent(beneficiary: BeneficiaryModel, event: NotificationEvent<*>) {
        logger.info(
            "BeneficiaryServiceImpl::produceActivationEvent - Beneficiary reactivation event produced",
            "person_id" to beneficiary.personId,
            "beneficiary_id" to beneficiary.id,
            "current_user_id" to currentUserId(),
            "event_type" to event.name
        )
        kafkaProducerService.produce(event)
    }

    private fun shouldValidateCpplProduct(companyId: UUID): Boolean {
        val shouldUseMpp = FeatureService.inList(
            FeatureNamespace.MEMBERSHIP,
            "should_create_mpp_for_this_companies",
            companyId.toString(),
            false
        )
        return !shouldUseMpp
    }

    override suspend fun createBeneficiary(
        beneficiaryTransport: BeneficiaryTransport,
        initialProductId: UUID?,
        flowType: BeneficiaryOnboardingFlowType,
        cassiMemberInfo: CassiMemberInfo?,
        createOptions: BeneficiaryService.CreateOptions,
        metadata: Map<String, String>?,
    ): Result<Beneficiary, Throwable> =
        beneficiaryTransport.validateBeneficiary(createOptions.ignoreHiredAtValidation)
            .flatMap { getAndValidateBeneficiaryParentPerson(it) }
            .flatMap { beneficiaryParent ->
                val companyInfo = getCompanyContractAndSubContract(
                    beneficiaryTransport.companyId,
                    beneficiaryTransport.companySubContractId,
                    beneficiaryTransport.nationalId
                ).get()
                val (company, subcontract, contract) = companyInfo

                val cpplProductIds =
                    companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id).get()

                val validatedModel = company.checkProductExistsAndValidateBeneficiaryTransport(
                    beneficiary = beneficiaryTransport.copy(
                        companySubContractId = subcontract.id,
                        flowType = flowType
                    ),
                    initialProductId = initialProductId,
                    cpplProductIds = cpplProductIds,
                    shouldCheckProduct = shouldValidateCpplProduct(company.id),
                    contractId = contract.id,
                ).get()

                logger.info(
                    "[BeneficiaryServiceImpl::createBeneficiary] Beneficiary validated",
                    "beneficiary" to validatedModel.beneficiary.toResponse()
                )

                val person = createOrUpdatePerson(
                    newPerson = validatedModel.beneficiary.toPerson(validatedModel.product.brand!!),
                    ignoreMembershipValidation = createOptions.ignoreMembershipValidation,
                    shouldValidateAdditionalInfo = createOptions.shouldValidateAdditionalInfo,
                    companyId = beneficiaryTransport.companyId,
                ).get()

                val member = createMember(
                    person.id,
                    validatedModel.beneficiary.companyId,
                    subcontract.id,
                    cassiMemberInfo,
                    createOptions.changeProduct,
                    validatedModel.product,
                    beneficiaryTransport.activatedAt
                ).get()


                mountBeneficiary(
                    validatedModel.beneficiary.toBeneficiary(
                        member.id,
                        member.personId,
                        member.status
                    ).toModel(),
                    flowType,
                    companyInfo,
                    beneficiaryParent
                ).flatMap { (beneficiary, inferredFlow) ->
                    beneficiary.addAndProduceEvent(
                        validatedModel.initialProductId,
                        inferredFlow,
                        phase = if (member.active) BeneficiaryOnboardingPhaseType.REGISTRATION else BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                        metadata = metadata
                    )
                }
                    .andThen { company.assignToBillingAccountable(it.personId) }
                    .map { it.toTransport() }
            }

    private suspend fun getCompanyContractAndSubContract(
        companyId: UUID,
        subContractId: UUID?,
        nationalId: String
    ): Result<BeneficiaryCompanyInfo, Throwable> =
        companyService.get(companyId)
            .map { it.toModel() }
            .map { company ->
                coroutineScope {
                    val subcontract =
                        getValidSubcontract(
                            companyId,
                            subContractId,
                            nationalId
                        )

                    val contract = contractService.get(subcontract.contractId).map { it.toModel() }.get()

                    BeneficiaryCompanyInfo(company, subcontract, contract)
                }
            }

    private suspend fun getAndValidateBeneficiaryParentPerson(beneficiaryTransport: BeneficiaryTransport): Result<BeneficiaryParent?, Throwable> {
        val parentBeneficiaryId = beneficiaryTransport.parentBeneficiary
        val parentBeneficiary =
            parentBeneficiaryId?.let { beneficiaryDataService.get(it).getOrNullIfNotFound() }
        val parentPerson = parentBeneficiary?.let { personService.get(it.personId).getOrNullIfNotFound() }
            ?: return null.success()

        val beneficiarySubContractId = beneficiaryTransport.companySubContractId
        val parentSubContractId = parentBeneficiary.companySubContractId
        val nationalIdAlreadyUsedByHolder = beneficiaryTransport.nationalId == parentPerson.nationalId
        val holderIsAlsoDependent = parentBeneficiary.parentBeneficiary != null
        val beneficiarySubContractIsDifferentFromHolder =
            beneficiarySubContractId != null && parentSubContractId != null && beneficiarySubContractId != parentSubContractId

        logger.info(
            "BeneficiaryServiceImpl::getAndValidateBeneficiaryParentPerson",
            "national_id_already_used_by_holder" to nationalIdAlreadyUsedByHolder,
            "holder_is_also_dependent" to holderIsAlsoDependent,
            "beneficiary_sub_contract_is_different_from_holder" to beneficiarySubContractIsDifferentFromHolder,
            "parent_person_id" to parentPerson.id
        )

        return when {
            nationalIdAlreadyUsedByHolder -> {
                NationalIdAlreadyUsedByHolderException().failure()
            }

            holderIsAlsoDependent -> {
                val holderBeneficiaryId = parentBeneficiary.id
                val holderParentBeneficiaryId = parentBeneficiary.parentBeneficiary!!
                HolderIsDependentException(holderBeneficiaryId, holderParentBeneficiaryId).failure()
            }

            beneficiarySubContractIsDifferentFromHolder -> {
                SubcontractIsDifferentFromHolderException(
                    beneficiaryTransport.companySubContractId!!,
                    parentBeneficiary.companySubContractId!!
                ).failure()
            }

            else -> BeneficiaryParent(parentBeneficiary, parentPerson).success()
        }
    }

    private suspend fun BeneficiaryModel.addAndProduceEvent(
        initialProductId: UUID,
        flowType: BeneficiaryOnboardingFlowType,
        phase: BeneficiaryOnboardingPhaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
        metadata: Map<String, String>? = null,
    ) = beneficiaryDataService.add(this.fillGracePeriodConfig())
        .thenError {
            logger.error(
                "Beneficiary was not created some error happened",
                "person_id" to this.personId,
                "member_id" to this.memberId,
                "error_message" to it.message,
                it
            )
        }
        .then {
            logger.info(
                "Beneficiary Created",
                "beneficiary_id" to it.id,
                "person_id" to it.personId,
                "member_id" to it.memberId,
            )
        }
        .then {
            kafkaProducerService.produce(
                BeneficiaryCreatedEvent(it.toTransport(), initialProductId, flowType, phase, metadata = metadata),
                it.memberId.toString()
            )
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.CREATED),
                it.id.toString()
            )
            if (it.memberStatus == MemberStatus.ACTIVE) {
                kafkaProducerService.produce(
                    BeneficiaryCreatedForActiveMemberEvent(it.toTransport()),
                    it.memberId.toString()
                )
            }
        }

    private suspend fun CompanyModel.checkProductExistsAndValidateBeneficiaryTransport(
        beneficiary: BeneficiaryTransport,
        initialProductId: UUID?,
        cpplProductIds: List<UUID>,
        shouldCheckProduct: Boolean = true,
        contractId: UUID,
    ): Result<BeneficiaryTransportAndInitialProductId, Throwable> = cpplProductIds.ifEmpty {
        throw EmptyAvailableProductListException(
            contractId,
            id
        )
    }.let {
        val productId = if (shouldCheckProduct) {
            getInitialProductId(cpplProductIds, initialProductId).get()
        } else {
            initialProductId ?: this.defaultProductId!!
        }

        productService.getProduct(productId)
            .map { product ->
                BeneficiaryTransportAndInitialProductId(
                    beneficiary = beneficiary,
                    initialProductId = product.id,
                    product = product
                )
            }
            .then {
                logger.info(
                    "BeneficiaryServiceImpl::checkProductExistsAndValidateBeneficiaryTransport",
                    "first_name" to it.beneficiary.firstName,
                    "initial_product_id" to it.initialProductId,
                    "product_id" to it.product.id
                )
            }
            .thenError {
                logger.error(
                    "BeneficiaryServiceImpl::checkProductExistsAndValidateBeneficiaryTransport error",
                    "error_message" to it.message
                )
            }
    }

    private suspend fun getValidSubcontract(
        companyId: UUID,
        companySubcontractId: UUID?,
        nationalId: String
    ): CompanySubContractModel {
        logger.info(
            "BeneficiaryServiceImpl::getValidSubcontract",
            "company_id" to companyId,
            "company_subcontract_id" to companySubcontractId
        )

        val subContracts = subcontractService.findByCompanyId(companyId).pmapEach { it.toModel() }.get()
        val subContractMap = subContracts.associateBy { it.id }
        val subContractIds = subContracts.map { it.id }

        logger.info(
            "BeneficiaryServiceImpl::getValidSubcontract subcontracts found",
            "company_id" to companyId,
            "subcontract_ids" to subContractIds
        )
        if (subContracts.isEmpty()) {
            throw CompanySubcontractRequiredException(companyId, subContractIds)
        }
        if (subContractIds.size == 1) {
            return subContractMap[subContractIds.first()] ?: throw CompanySubcontractRequiredException(
                companyId,
                subContractIds
            )
        }
        if (companySubcontractId == null) {
            throw BeneficiaryMustHaveSubContractIdException(null, nationalId, companyId)
        }

        return when {
            !subContractIds.contains(companySubcontractId) -> throw MismatchCompanySubcontractException(
                companySubcontractId,
                companyId
            )

            else -> subContractMap[companySubcontractId] ?: throw CompanySubcontractRequiredException(
                companyId,
                subContractIds
            )
        }
    }

    private suspend fun getCompanyAndCpplProducts(companyId: UUID, companySubcontractId: UUID) =
        logger.info(
            "BeneficiaryServiceImpl::getCompanyAndCpplProducts",
            "company_id" to companyId
        ).let {
            companyService.get(companyId)
                .map { it.toModel() }
                .mapPair { company ->
                    companyProductPriceListingService.findCpplProductIdsForSubContract(companySubcontractId)
                        .get()
                        .ifEmpty {
                            contractService.findBySubcontractId(companySubcontractId)
                                .getOrNullIfNotFound().let {
                                    throw EmptyAvailableProductListException(
                                        it?.id,
                                        company.id
                                    )
                                }
                        }
                }
        }

    data class BeneficiaryTransportAndInitialProductId(
        val beneficiary: BeneficiaryTransport,
        val initialProductId: UUID,
        val product: Product,
    )

    private suspend fun createMember(
        personId: PersonId,
        companyId: UUID,
        subContractId: UUID,
        cassiMemberInfo: CassiMemberInfo?,
        changeProduct: Boolean,
        product: Product,
        activationDate: LocalDateTime? = null
    ) = logger.info(
        "BeneficiaryServiceImpl::createMember",
        "person_id" to personId,
        "company_id" to companyId,
        "cassi_member_info" to cassiMemberInfo,
        "change_product" to changeProduct,
        "product_id" to product.id,
        "activation_date" to activationDate,
    ).let {
        productService.getCurrentProductPriceListing(product.id)
            .map { productPriceListing -> product.toMemberProduct(productPriceListing.id) }
            .flatMap {
                createOrUpdateMember(
                    personId,
                    companyId,
                    subContractId,
                    it,
                    product,
                    cassiMemberInfo,
                    changeProduct,
                    activationDate,
                )
            }
    }

    private suspend fun createOrUpdateMember(
        personId: PersonId,
        companyId: UUID,
        subContractId: UUID,
        memberProduct: MemberProduct,
        product: Product,
        cassiMemberInfo: CassiMemberInfo?,
        changeProduct: Boolean,
        activationDate: LocalDateTime? = null
    ): Result<Member, Throwable> = buildOnboardingContract(personId, companyId, memberProduct)
        .flatMap { memberService.create(personId, it, cassiMemberInfo) }
        .coFoldError(
            MembershipAlreadyActiveMismatchException::class to br.com.alice.common.core.suspend() { ex ->
                logger.warn(
                    "BeneficiaryServiceImpl::createOrUpdateMember member already has active membership",
                    "person_id" to personId,
                    "company_id" to companyId,
                    "change_product" to changeProduct
                )
                if (changeProduct) {
                    logger.info(
                        "Trying to change beneficiary membership product",
                        "person_id" to personId,
                        "company_id" to companyId
                    )
                    maybeUpdateMembershipFromException(
                        ex,
                        personId,
                        companyId,
                        subContractId,
                        memberProduct,
                        product,
                        activationDate
                    )
                } else {
                    logger.info(
                        "Choose to not change beneficiary membership product",
                        "person_id" to personId,
                        "company_id" to companyId
                    )
                    ex.failure()
                }
            }
        )

    private suspend fun maybeUpdateMembershipFromException(
        exception: Throwable,
        personId: PersonId,
        companyId: UUID,
        subContractId: UUID,
        memberProduct: MemberProduct,
        product: Product,
        activationDate: LocalDateTime? = null
    ): Result<Member, Throwable> =
        memberService.getCurrent(personId)
            .flatMap {
                if (it.productId != memberProduct.id) {
                    return changeProduct(it, companyId, product, activationDate)
                } else if (it.isB2BOrAdesao) {
                    logger.info("check if beneficiary exists when product is B2B")
                    val beneficiary = findByMemberId(it.id).getOrNullIfNotFound()
                    if (beneficiary != null &&
                        (beneficiary.companyId != companyId || beneficiary.companySubContractId != subContractId)
                    ) {
                        return changeProduct(it, companyId, product, activationDate)
                    } else if (beneficiary == null) {
                        return it.success()
                    }
                }

                logger.warn(
                    "Request to create beneficiary matches product with current active membership",
                    "person_id" to personId,
                    "company_id" to companyId,
                    "current_member_id" to it.id,
                    "current_product_id" to it.productId,
                    "current_product_type" to it.productType,
                    "request_product_id" to product.id
                )
                BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE)
                return exception.failure()
            }

    private suspend fun changeProduct(
        member: Member,
        companyId: UUID,
        product: Product,
        activationDate: LocalDateTime? = null
    ) =
        companyService.isThisProductAvailableForCompany(companyId, product)
            .flatMap {
                val shouldKeepActivate = if (member.isB2BOrAdesao) member.active else false

                memberService.changeProduct(member.personId, it, shouldKeepActivate, activationDate)
            }
            .then {
                logger.info(
                    "Membership product changed for beneficiary",
                    "company_id" to companyId,
                    "person_id" to member.personId,
                    "old_member_id" to member.id,
                    "old_member_id" to member.id,
                    "old_product_id" to member.productId,
                    "old_product_type" to member.productType,
                    "old_member_status" to member.status,
                    "new_member_id" to it.id,
                    "new_member_id" to it.id,
                    "new_product_id" to it.productId,
                    "new_product_type" to it.productType,
                    "new_member_status" to it.status
                )
                BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS)
            }

    private fun Product.toMemberProduct(productPriceListingId: UUID): MemberProduct = MemberProduct(
        id = this.id,
        prices = emptyList(),
        priceListing = this.priceListing,
        productPriceListingId = productPriceListingId,
        type = this.type,
    )

    private suspend fun buildOnboardingContract(
        personId: PersonId,
        companyId: UUID,
        selectedProduct: MemberProduct
    ): Result<OnboardingContract, Throwable> = companyService.get(companyId)
        .map { it.contractsUrls.lastOrNull() ?: "no_contract_registered" }
        .map {
            OnboardingContract(
                personId = personId,
                documentUrl = it,
                selectedProduct = selectedProduct
            )
        }

    private suspend fun CompanyModel.assignToBillingAccountable(personId: PersonId) =
        (if (parentId == null) this.success() else companyService.getRootCompany(id).map { it.toModel() })
            .flatMap { billingAccountablePartyService.get(it.billingAccountablePartyId!!) }
            .flatMap { billingAccountablePartyService.assign(personId, it) }

    override suspend fun get(id: UUID, findOptions: FindOptions): Result<Beneficiary, Throwable> =
        beneficiaryDataService.find { buildGetBeneficiaryQueryFilter(id, findOptions.withDependents) }
            .map { extractHolderFromDependents(id, it) }
            .flatMap { withOptions(it, findOptions.copy(withDependents = false)) }
            .map { it.toTransport() }

    override suspend fun withOnboardingAndDependents(beneficiary: Beneficiary) =
        withOptions(beneficiary.toModel(), FindOptions(withOnboarding = true, withDependents = true))
            .map { it.toTransport() }

    override suspend fun update(model: Beneficiary): Result<Beneficiary, Throwable> {
        logger.info(
            "Updating a Beneficiary",
            "beneficiary" to model
        )
        return updateBeneficiary(model.toModel()).map { it.toTransport() }
    }

    override suspend fun validateToUpdate(model: Beneficiary): Result<Beneficiary, Throwable> {
        logger.info(
            "Updating a Beneficiary with validation",
            "beneficiary" to model
        )
        return model.toModel().validateAndUpdate().map { it.toTransport() }
    }

    override suspend fun delete(model: Beneficiary) = beneficiaryDataService.delete(model.toModel())

    override suspend fun updateInBatch(beneficiaries: List<Beneficiary>): Result<List<Beneficiary>, Throwable> =
        Result.of<List<BeneficiaryModel>, Throwable> {
            updateList(beneficiaries.map { it.toModel() })
        }.pmapEach { beneficiaryModel ->
            beneficiaryModel.toTransport().also {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(it, NotificationEventAction.UPDATED),
                    it.id.toString()
                )
            }
        }

    private suspend fun updateList(beneficiaries: List<BeneficiaryModel>) =
        beneficiaries.chunked(50).pmap { beneficiariesChunk ->
            logger.info(
                "BeneficiaryServiceImpl:updateList Updating Beneficiary list in chunks",
                "task_list_chunk_size" to beneficiariesChunk.size
            )
            beneficiaryDataService.updateList(beneficiariesChunk).get()
        }.flatten()


    override suspend fun findByCompanyId(
        companyId: UUID,
        findOptions: FindOptions,
        range: IntRange?
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find {
            val whereCondition = where { this.companyId.eq(companyId) and this.archived.eq(false) }
            range?.let { whereCondition.offset { it.first }.limit { it.count() } } ?: whereCondition
        }.flatMap { beneficiaries -> beneficiaries.map { withOptions(it, findOptions) }.lift() }
            .mapEach { it.toTransport() }

    override suspend fun findByCompanyIds(
        companyIds: List<UUID>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find { where { this.companyId.inList(companyIds) and this.archived.eq(false) } }
            .flatMap { beneficiaries -> beneficiaries.map { withOptions(it, findOptions) }.lift() }
            .mapEach { it.toTransport() }

    override suspend fun findByCompanySubContractId(
        companySubContractId: UUID,
        findOptions: FindOptions,
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find {
            where {
                this.companySubContractId.eq(companySubContractId) and this.archived.eq(
                    false
                )
            }
        }
            .flatMap { beneficiaries -> beneficiaries.map { withOptions(it, findOptions) }.lift() }
            .mapEach { it.toTransport() }

    override suspend fun findByMemberId(
        memberId: UUID,
        findOptions: FindOptions
    ): Result<Beneficiary, Throwable> = useReadDatabase {
        beneficiaryDataService.findOne { where { this.memberId.eq(memberId) and this.archived.eq(false) } }
            .flatMap { withOptions(it, findOptions) }
    }.map { it.toTransport() }

    override suspend fun findByMemberIds(
        memberIds: List<UUID>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> = useReadDatabase {
        beneficiaryDataService.find { where { this.memberId.inList(memberIds) and this.archived.eq(false) } }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
    }.mapEach { it.toTransport() }

    override suspend fun findByParentId(
        parentId: UUID,
        findOptions: FindOptions,
        onlyDirectDependents: Boolean
    ): Result<List<Beneficiary>, Throwable> = useReadDatabase {
        beneficiaryDataService.find {
            where {
                this.parentId.eq(parentId) and this.archived.eq(false) and
                        if (onlyDirectDependents) this.parentBeneficiaryRelationType.inList(
                            findParentTypesForDirectDependency()
                        ) else null
            }
        }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
    }.mapEach { it.toTransport() }

    private fun findParentTypesForDirectDependency() = listOf(
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.SPOUSE,
        ParentBeneficiaryRelationType.FOSTER_CHILD,
        ParentBeneficiaryRelationType.STEPCHILD,
    )

    override suspend fun findByParentPerson(
        parentPersonId: PersonId,
        memberStatuses: List<MemberStatus>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> = useReadDatabase {
        beneficiaryDataService.find {
            where {
                this.parentPersonId.eq(parentPersonId) and this.memberStatus.inList(memberStatuses) and this.archived.eq(
                    false
                )
            }
        }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
    }.mapEach { it.toTransport() }

    override suspend fun findByPersonId(
        personId: PersonId,
        findOptions: FindOptions
    ): Result<Beneficiary, Throwable> = useReadDatabase {
        memberService.getCurrent(personId)
            .flatMap {
                beneficiaryDataService.findOne {
                    where {
                        this.memberId.eq(it.id) and this.archived.eq(
                            false
                        )
                    }
                }
            }
            .flatMap { withOptions(it, findOptions) }
    }.map { it.toTransport() }

    override suspend fun findByPersonIds(
        personIds: List<PersonId>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> = useReadDatabase {
        memberService.findByPersonIds(personIds)
            .map { members -> members.map { it.id } }
            .flatMap {
                beneficiaryDataService.find {
                    where {
                        this.memberId.inList(it) and this.archived.eq(
                            false
                        )
                    }
                }
            }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
    }.mapEach { it.toTransport() }

    override suspend fun findByCompanyIdAndPersonIds(
        companyId: UUID,
        personIds: List<PersonId>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find {
            where {
                this.companyId.eq(companyId) and this.personId.inList(personIds) and this.archived.eq(
                    false
                )
            }
        }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
            .mapEach { it.toTransport() }

    private suspend fun findAndFilterActivatedAtByIds(ids: List<UUID>, activatedAt: LocalDate) =
        if (ids.isNotEmpty())
            findByIds(ids).map { it.filterBeforeActivatedAt(activatedAt) }
        else emptyList<Beneficiary>().success()

    override suspend fun findByIds(
        ids: List<UUID>,
        findOptions: FindOptions
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find { where { this.id.inList(ids) and this.archived.eq(false) } }
            .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
            .mapEach { it.toTransport() }

    override suspend fun findByGracePeriodAndStatus(
        range: IntRange,
        gracePeriodType: GracePeriodType,
        memberStatus: MemberStatus,
    ): Result<List<Beneficiary>, Throwable> = beneficiaryDataService.find {
        where {
            this.gracePeriodType.eq(gracePeriodType) and this.memberStatus.eq(memberStatus) and this.archived.eq(
                false
            )
        }.offset { range.first() }.limit { range.count() }
    }.mapEach { it.toTransport() }

    override suspend fun countByIds(ids: List<UUID>): Result<Int, Throwable> =
        beneficiaryDataService.count { where { this.id.inList(ids) and this.archived.eq(false) } }

    override suspend fun findByFilters(
        personId: PersonId?,
        companyId: UUID?,
        parentId: UUID?,
        currentPhase: BeneficiaryOnboardingPhaseType?,
        range: IntRange,
        findOptions: FindOptions,
    ): Result<List<Beneficiary>, Throwable> = useReadDatabase {
        when {
            personId != null ->
                memberService.getCurrent(personId)
                    .flatMap { findByMemberId(it.id).map { it.toModel() } }
                    .flatMap { withOptions(it, findOptions) }
                    .map { listOf(it) }

            currentPhase != null ->
                beneficiaryOnboardingService.findByCurrentPhase(
                    currentPhase,
                    range,
                    flowType = null,
                    findOptions = BeneficiaryOnboardingService.FindOptions(withPhases = false),
                ).mapEach { it.toModel() }
                    .map { beneficiaryOnboardings -> beneficiaryOnboardings.map { it.beneficiaryId } }
                    .flatMap { findByIds(it, findOptions) }
                    .mapEach { it.toModel() }

            else -> beneficiaryDataService.find {
                BeneficiaryLogic.buildFilterQuery(
                    companyId,
                    parentId,
                    range
                )
            }
                .flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
        }.mapEach { it.toTransport() }
    }

    override suspend fun getByIdAndPersonId(id: UUID, personId: PersonId): Result<Beneficiary, Throwable> =
        beneficiaryDataService.findOne {
            where {
                this.id.eq(id) and this.personId.eq(personId)
            }
        }.map { it.toTransport() }

    override suspend fun findByCanceledAtBetweenInclusive(
        startDate: LocalDate,
        endDate: LocalDate
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find {
            where {
                this.canceledAt.greaterEq(startDate.atStartOfDay()) and
                        this.canceledAt.lessEq(endDate.atStartOfDay()) and
                        this.archived.eq(false)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findByCanceledAtBetweenInclusivePaginated(
        startDate: LocalDate,
        endDate: LocalDate,
        offset: Int,
        limit: Int,
    ): Result<List<Beneficiary>, Throwable> =
        beneficiaryDataService.find {
            where {
                this.canceledAt.greaterEq(startDate.atStartOfDay()) and
                        this.canceledAt.lessEq(endDate.atStartOfDay()) and
                        this.archived.eq(false)
            }
                .offset { offset }
                .limit { limit }
        }.pmapEach { it.toTransport() }

    @Deprecated("Use the method findPendingByActivatedAtBetweenInclusivePaginated instead")
    override suspend fun findPendingByActivatedAtBetweenInclusive(
        startDate: LocalDate,
        endDate: LocalDate,
        findOptions: FindOptions
    ) =
        beneficiaryDataService.find {
            where {
                this.activatedAt.greaterEq(startDate.atStartOfDay()) and
                        this.activatedAt.lessEq(endDate.atEndOfTheDay()) and
                        this.archived.eq(false) and
                        this.memberStatus.eq(MemberStatus.PENDING)
            }.orderBy { this.parentBeneficiary }.sortOrder { desc }
        }.flatMap { beneficiaries ->
            beneficiaries.pmap { withOptions(it, findOptions) }
                .lift()
        }.mapEach { it.toTransport() }

    override suspend fun findPendingByActivatedAtBetweenInclusivePaginated(
        startDate: LocalDate,
        endDate: LocalDate,
        findOptions: FindOptions,
        offset: Int,
        limit: Int,
    ) =
        beneficiaryDataService.find {
            where {
                this.activatedAt.greaterEq(startDate.atStartOfDay()) and
                        this.activatedAt.lessEq(endDate.atEndOfTheDay()) and
                        this.archived.eq(false) and
                        this.memberStatus.eq(MemberStatus.PENDING)
            }
                .offset { offset }
                .limit { limit }
                .orderBy { this.parentBeneficiary }
                .sortOrder { desc }
        }.then {
            logger.info(
                "BeneficiaryService::findPendingByActivatedAtBetweenInclusivePaginated",
                "activatedAt" to startDate,
                "greaterEq" to startDate.atStartOfDay(),
                "lessEq" to endDate.atEndOfTheDay(),
                "limit" to limit,
                "offset" to offset,
            )
        }.flatMap { beneficiaries -> beneficiaries.pmap { withOptions(it, findOptions) }.lift() }
            .pmapEach { it.toTransport() }

    private suspend fun withOptions(
        beneficiary: BeneficiaryModel,
        findOptions: FindOptions
    ): Result<BeneficiaryModel, Throwable> = coroutineScope {
        val onboardingDeferred = async {
            val nullOnboarding: BeneficiaryOnboardingModel? = null

            if (findOptions.withOnboarding)
                beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id)
                    .map { it.toModel() }
                    .flatMapError {
                        if (it::class == NotFoundException::class)
                            nullOnboarding.success()
                        else
                            it.failure()
                    }
            else
                nullOnboarding.success()
        }

        val dependentsDeferred = async {
            val emptyDependents: List<BeneficiaryModel> = emptyList()
            if (findOptions.withDependents)
                findByParentId(beneficiary.id)
            else
                emptyDependents.success()
        }

        val onboarding = onboardingDeferred.await().get()
        val dependents = dependentsDeferred.await().get()

        beneficiary
            .withOnboarding(onboarding)
            .success().map {
                if (findOptions.withDependents)
                    it.withDependents(dependents)
                else
                    it
            }
    }

    private suspend fun findByParentId(parentId: UUID): Result<List<BeneficiaryModel>, Throwable> =
        beneficiaryDataService.find {
            where { this.parentId.eq(parentId) and this.archived.eq(false) }
        }

    override suspend fun countByFilters(
        personId: PersonId?,
        companyId: UUID?,
        parentId: UUID?,
        currentPhase: BeneficiaryOnboardingPhaseType?,
    ): Result<Int, Throwable> =
        when {
            personId != null ->
                memberService.findByPerson(personId)
                    .map { members -> members.map { it.id } }
                    .flatMap {
                        beneficiaryDataService.count {
                            where {
                                this.memberId.inList(it) and this.archived.eq(
                                    false
                                )
                            }
                        }
                    }

            currentPhase != null -> beneficiaryOnboardingService.countByCurrentPhase(currentPhase)
            else -> beneficiaryDataService.count { BeneficiaryLogic.buildFilterQuery(companyId, parentId) }
        }

    override suspend fun abortBeneficiaryScheduledCancel(
        beneficiaryId: UUID,
    ): Result<Beneficiary, Throwable> = beneficiaryDataService.get(beneficiaryId)
        .flatMap { it.validateAndAbortCancel() }
        .flatMap { updateBeneficiary(it) }
        .map { it.toTransport() }

    override suspend fun cancelBeneficiaryById(
        beneficiaryId: UUID,
        beneficiaryCancelation: BeneficiaryCancelation
    ): Result<Beneficiary, Throwable> {
        val today = LocalDate.now()
        val (canceledAt) = beneficiaryCancelation

        return when {
            canceledAt.isBefore(today) -> InvalidBeneficiaryCanceledRequest(
                beneficiaryId,
                canceledAt,
                today
            ).failure()

            canceledAt.isEqual(today) -> cancelBeneficiaryAndMembershipNow(
                beneficiaryId,
                beneficiaryCancelation
            )

            else -> cancelBeneficiary(beneficiaryId, beneficiaryCancelation).map { it.toTransport() }
        }
    }

    private suspend fun cancelBeneficiaryAndMembershipNow(
        beneficiaryId: UUID,
        beneficiaryCancelation: BeneficiaryCancelation
    ): Result<Beneficiary, Throwable> = cancelBeneficiary(beneficiaryId, beneficiaryCancelation)
        .andThen { memberService.cancelById(it.memberId) }
        .map { it.toTransport() }


    private suspend fun cancelBeneficiary(
        beneficiaryId: UUID,
        beneficiaryCancelation: BeneficiaryCancelation
    ): Result<BeneficiaryModel, Throwable> = beneficiaryDataService.get(beneficiaryId)
        .flatMap { beneficiary ->
            beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)
                .flatMapError {
                    if (it is BeneficiaryAlreadyCanceledException)
                        return@flatMap beneficiary.success()
                    else it.failure()
                }
                .flatMap { updateBeneficiary(it) }
                .then { metrifyBeneficiaryCanceled(it, beneficiaryCancelation) }
                .then { kafkaProducerService.produce(BeneficiaryCanceledEvent(it.toTransport())) }
        }

    private suspend fun BeneficiaryModel.validateAndAdd(
        initialProductId: UUID?,
        ignoreHiredAtValidation: Boolean = false
    ): Result<BeneficiaryModel, Throwable> =
        validateBeneficiary(initialProductId, ignoreHiredAtValidation)
            .flatMap { beneficiaryDataService.add(this.fillGracePeriodConfig()) }
            .thenError {
                logger.error(
                    "Beneficiary was not created some error happened",
                    "person_id" to this.personId,
                    "member_id" to this.memberId,
                    "error_message" to it.message,
                    it
                )
            }
            .then {
                logger.info(
                    "Beneficiary Created",
                    "beneficiary_id" to it.id,
                    "person_id" to it.personId,
                    "member_id" to it.memberId,
                )
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.CREATED),
                    it.id.toString()
                )
            }
            .coFoldDuplicated { BeneficiaryAlreadyExistsException(this.personId, this.memberId).failure() }

    private fun BeneficiaryModel.fillGracePeriodConfig() =
        if (gracePeriodType == null && gracePeriodTypeReason == null)
            copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT
            )
        else this

    private suspend fun BeneficiaryModel.validateAndUpdate(): Result<BeneficiaryModel, Throwable> =
        validateBeneficiary()
            .map { it.sanitizeRelationship() }
            .flatMap { updateBeneficiary(it) }

    private suspend fun BeneficiaryModel.sanitizeRelationship() =
        when (type) {
            BeneficiaryType.DEPENDENT -> beneficiaryDataService.get(parentBeneficiary!!)
                .map { copy(parentPerson = it.personId) }.get()

            else -> copy(parentBeneficiaryRelationType = null, parentPerson = null, parentBeneficiaryRelatedAt = null)
        }

    private suspend fun updateBeneficiary(beneficiary: BeneficiaryModel): Result<BeneficiaryModel, Throwable> =
        beneficiaryDataService.update(beneficiary)
            .then {
                kafkaProducerService.produce(BeneficiaryUpdatedEvent(it.toTransport()))
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.UPDATED),
                    it.id.toString()
                )
            }

    private suspend fun BeneficiaryModel.validateBeneficiary(
        initialProductId: UUID?,
        ignoreHiredAtValidation: Boolean = false
    ): Result<BeneficiaryModel, Throwable> = validateBeneficiary(ignoreHiredAtValidation)
        .flatMap {
            getCompanyAndCpplProducts(
                companyId,
                this.companySubContractId ?: throw BeneficiaryMustHaveSubContractIdException(
                    this.id,
                    null,
                    companyId
                ),
            )
        }
        .flatMap { (cpplProducts, company) ->
            company.getInitialProductId(
                cpplProducts,
                initialProductId
            )
        }
        .map { this }

    override suspend fun cancelActivationById(beneficiaryId: UUID): Result<Beneficiary, Throwable> =
        get(beneficiaryId).map { it.toModel() }
            .flatMapPair { memberService.get(it.memberId) }
            .flatMap { (member, beneficiary) -> cancelActivation(member, beneficiary) }
            .then {
                logger.info(
                    "cancel activation completed",
                    "memberId" to it.memberId,
                    "beneficiaryId" to it.id
                )
            }
            .map { it.toTransport() }
            .thenError { logger.error("error when cancel activation", "error" to it) }

    private suspend fun cancelActivation(
        member: Member,
        beneficiary: BeneficiaryModel
    ): Result<BeneficiaryModel, Throwable> =
        if (member.status != MemberStatus.PENDING)
            InvalidActivationCancellationStateException(beneficiary.id, member.status!!).failure()
        else
            cancelBeneficiaryById(
                beneficiary.id,
                BeneficiaryCancelation(
                    canceledAt = LocalDate.now(),
                    canceledReason = BeneficiaryCancelationReason.PRIOR_ACTIVATION,
                    hasContributed = false
                )
            ).map { it.toModel() }

    private suspend fun createOrUpdatePerson(
        newPerson: Person,
        ignoreMembershipValidation: Boolean,
        shouldValidateAdditionalInfo: Boolean = false,
        companyId: UUID
    ) = personService.create(
        person = newPerson,
        shouldValidateAdditionalInfo = shouldValidateAdditionalInfo
    )
        .flatMapPair {
            logger.info(
                "BeneficiaryServiceImpl::createOrUpdatePerson - starting personOnboarding creation",
                "person_id" to it.id,
            )
            gasProcessService.start(it.id)
        }
        .map { (_, person) -> person }
        .coFoldDuplicated {
            personService.findByNationalId(newPerson.nationalId)
                .flatMap {
                    tryUpdatePersonMembership(
                        it,
                        newPerson,
                        ignoreMembershipValidation,
                        companyId
                    )
                }
        }
        .coFoldError(
            DuplicatedItemException::class to {
                personService.findByNationalId(newPerson.nationalId)
                    .flatMap {
                        tryUpdatePersonMembership(
                            it,
                            newPerson,
                            ignoreMembershipValidation,
                            companyId
                        )
                    }
            },
        )

    private suspend fun tryUpdatePersonMembership(
        oldPerson: Person,
        newPerson: Person,
        ignoreMembershipValidation: Boolean,
        companyId: UUID
    ): Result<Person, Throwable> =
        if (ignoreMembershipValidation) {
            logger.info(
                "Create or update the person and ignore any membership activated",
                "person_id" to oldPerson.id
            )
            personService.update(oldPerson.toNewPerson(newPerson))
                .map { person ->
                    checkOrCreatePersonOnboarding(person.id)
                    person
                }
        } else {
            logger.info(
                "Create or update the person and validate any membership activated",
                "person_id" to oldPerson.id
            )

            updateMembership(
                oldPerson,
                newPerson,
                companyId,
            )
        }

    private suspend fun updateMembership(oldPerson: Person, newPerson: Person, companyId: UUID) =
        memberService.getCurrent(oldPerson.id).flatMap {
            when {
                it.status != MemberStatus.CANCELED -> {
                    logAlreadyAliceMemberException(oldPerson, companyId)
                    AlreadyAliceMemberException(personName = oldPerson.fullRegisterName).failure()
                }

                else -> personService.update(oldPerson.toNewPerson(newPerson))
                    .map { person ->
                        checkOrCreatePersonOnboarding(person.id)
                        person
                    }
            }
        }.coFoldNotFound {
            personService.update(oldPerson.toNewPerson(newPerson)).map { person ->
                checkOrCreatePersonOnboarding(person.id)
                person
            }
        }

    private suspend fun logAlreadyAliceMemberException(person: Person, companyId: UUID) {
        companyService.get(companyId).map {
            logger.info("Não foi possível incluir o beneficiário ${person.fullRegisterName} para a empresa: ${it.name}, esse membro já possui um plano ativo.")
        }
    }

    private suspend fun checkOrCreatePersonOnboarding(personId: PersonId) =
        onboardingService.findByPerson(personId)
            .then {
                logger.info(
                    "BeneficiaryServiceImpl::checkOrCreatePersonOnboarding - personOnboarding already exists",
                    "person_onboarding" to it,
                )
            }
            .coFoldNotFound {
                logger.info(
                    "BeneficiaryServiceImpl::checkOrCreatePersonOnboarding - starting personOnboarding creation",
                    "person_id" to personId,
                )
                gasProcessService.start(personId)
            }


    override suspend fun countUniquePerPersonByCompanyId(companyId: UUID) =
        beneficiaryDataService
            .count {
                where {
                    this.companyId.eq(companyId) and this.memberStatus.eq(MemberStatus.ACTIVE)
                }
            }

    override suspend fun getCompanyContractStarted(memberId: UUID) = useReadDatabase {
        findByMemberId(memberId)
            .map { beneficiary ->
                beneficiary.companySubContractId?.let { subcontractId ->
                    subcontractService.get(subcontractId)
                        .flatMap { contractService.get(it.contractId) }
                        .map { it.startedAt }.get()
                } ?: throw IllegalArgumentException("Company does not have a started at date")
            }
    }

    private fun shouldCheckForCreatedAtFromCassiNumberAccountTrigger() =
        FeatureService.get(
            FeatureNamespace.BUSINESS,
            DISABLE_CREATED_AT_CHECKING_FROM_CASSI_NUMBER_ACCOUNT_TRIGGER,
            true
        )

    override suspend fun triggerCassiAccountNumberForBeneficiaries(
        offset: Int,
        limit: Int,
    ) =
        cassiMemberService.findByAccountNumberEmpty(offset = offset, limit = limit).map { cassiMembers ->
            cassiMembers.map { it.memberId }
                .let { memberService.findByIds(it) }
                .map {
                    it.filterNot { member ->
                        member.isCanceled || (shouldCheckForCreatedAtFromCassiNumberAccountTrigger() && (member.createdAt.plusDays(
                            1
                        ).isAfter(
                            LocalDateTime.now()
                        )))
                    }
                }
                .flatMapPair { members -> personService.findByIds(members.map { it.personId.toString() }) }
                .then { (persons, members) ->
                    logger.info(
                        "TriggerCassiAccountNumberForBeneficiaries",
                        "total" to persons.size,
                        "persons" to persons.map { it.id },
                        "members" to members.map { it.id }
                    )
                }
                .map { (persons, members) ->
                    produceRequestCassiMemberUpdateEvent(
                        persons,
                        members
                    )
                }.get()

            cassiMembers.size
        }

    override suspend fun triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate(): Result<Boolean, Throwable> {

        logger.info(
            "triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate: initiate",
        )

        var offset = 0
        val limit = FeatureService.get(FeatureNamespace.BUSINESS, "cass_older_expiration_limit", 500)
        while (true) {
            val cassiMembers =
                cassiMemberService.findByOlderExpirationDatePaginated(LocalDateTime.now(), offset, limit).get()

            val cassiMembersGroups = cassiMembers.map { it.memberId }.chunked(CHUNK_GROUP_SIZE)

            logger.info(
                "triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate: groups created",
                "total_cassi_members" to cassiMembers.size
            )

            cassiMembersGroups.forEach { cassiMemberGroup ->
                memberService.findByIds(cassiMemberGroup)
                    .map {
                        it.filterNot { member ->
                            member.isCanceled || member.createdAt.plusDays(1).isAfter(
                                LocalDateTime.now()
                            )
                        }
                    }
                    .flatMapPair { members -> personService.findByIds(members.map { it.personId.toString() }) }
                    .then { (persons, members) ->
                        logger.info(
                            "TriggerCassiAccountNumberForBeneficiaries with older expiration date",
                            "total" to persons.size,
                            "persons" to persons.map { it.id },
                            "members" to members.map { it.id }
                        )
                    }
                    .flatMap { (persons, members) ->
                        produceRequestCassiMemberUpdateEvent(
                            persons,
                            members
                        ).success()
                    }
            }

            if (cassiMembers.size < limit) {
                logger.info("triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate: break")
                break
            }

            offset += limit
        }

        return true.success()
    }

    override suspend fun updateCassiMembershipByExpirationDate(
        limit: Int,
        offset: Int
    ): Result<Boolean, Throwable> {

        logger.info(
            "updateCassiMembershipByExpirationDate: initiate",
        )

        val cassiMembers =
            cassiMemberService.findByOlderExpirationDatePaginated(LocalDateTime.now(), limit, offset).get()
        val cassiMembersGroups = cassiMembers.map { it.memberId }.chunked(CHUNK_GROUP_SIZE)

        logger.info(
            "updateCassiMembershipByExpirationDate: groups created",
            "total_cassi_members", cassiMembers.size
        )

        cassiMembersGroups.forEach { cassiMemberGroup ->
            memberService.findByIds(cassiMemberGroup)
                .map {
                    it.filterNot { member ->
                        member.isCanceled || member.createdAt.plusDays(1).isAfter(
                            LocalDateTime.now()
                        )
                    }
                }
                .pmapEach { member ->
                    personService.get(member.personId).flatMap { cassiMemberService.updateCassiMember(it, member) }
                }
                .then {
                    logger.info(
                        "updateCassiMembershipByExpirationDate with older expiration date",
                        "total" to it.size,
                        "succeeded" to it.filter { it.isSuccess() }.size,
                    )
                }
        }

        return true.success()

    }

    private suspend fun produceRequestCassiMemberUpdateEvent(persons: List<Person>, members: List<Member>) {
        members.forEach { member ->
            val person = persons.first { it.id == member.personId }
            kafkaProducerService.produce(RequestCassiMemberUpdateEvent(member, person))
            logger.info(
                "RequestCassiMemberUpdateEvent produced successfully",
                "person_id" to person.id,
                "member_id" to member.id
            )
        }
    }

    override suspend fun checkBeneficiaryFlowType(
        personId: PersonId,
        beneficiaryOnboardingFlowType: BeneficiaryOnboardingFlowType
    ): Result<Boolean, Throwable> =
        findByPersonId(personId, FindOptions(withOnboarding = true)).map { it.onboarding?.flowType }
            .map { flowType ->
                flowType == beneficiaryOnboardingFlowType
            }

    override suspend fun countActiveWithNoPendingCancellation(
        companyId: UUID,
        type: BeneficiaryType?
    ): Result<Int, Throwable> =
        beneficiaryDataService.count {
            where {
                this.companyId.eq(companyId) and this.archived.eq(false) and
                        this.memberStatus.eq(MemberStatus.ACTIVE) and
                        this.canceledAt.isNull() and
                        type?.let { this.type.eq(it) }

            }
        }

    override suspend fun activateBeneficiary(beneficiaryId: UUID) =
        beneficiaryDataService.get(beneficiaryId).map {
            if (shouldActivateBeneficiary(it)) {
                it
            } else {
                logger.error("Beneficiary should not be activated", "beneficiary" to it)
                return@activateBeneficiary BeneficiaryActivationValidationException(it.toTransport()).failure()
            }
        }.flatMapPair {
            memberService.get(it.memberId)
        }.flatMap { (member, beneficiary) ->
            memberService.activateMember(member).map { beneficiary.copy(memberStatus = it.status) }
        }.map { it.toTransport() }

    override suspend fun checkContractAlreadySigned(memberId: UUID): Result<Boolean, Throwable> {
        val beneficiary = findByMemberId(memberId).get()
        val contractSigned = when (beneficiary.companySubContractId) {
            null -> false.success()
            else -> {
                subcontractService.get(beneficiary.companySubContractId!!).flatMap { subcontract ->
                    contractService.get(subcontract.contractId).map { contract ->
                        contract.contractFileIds.isNotEmpty()
                    }
                }
            }
        }
        return contractSigned
    }

    private suspend fun shouldActivateBeneficiary(beneficiary: BeneficiaryModel): Boolean {
        if (beneficiary.activatedAt.toLocalDate() > LocalDate.now()) {
            return false
        }

        beneficiary.parentPerson?.let { parentPersonId ->
            memberService.getCurrent(parentPersonId)
                .getOrNullIfNotFound()?.let {
                    if (!it.active) {
                        logger.info(
                            "Beneficiary should not activate because the parent is not activated yet",
                            "beneficiary_id" to beneficiary.id
                        )

                        return false
                    }

                    return true
                } ?: run {
                logger.info(
                    "Beneficiary should not activate because the parent register is not found",
                    "beneficiary_id" to beneficiary.id
                )

                return false
            }
        }

        val subcontract = findOrInferSubcontractForBeneficiary(beneficiary).get()

        if (subcontract.externalId == null) {
            logger.info(
                "Beneficiary should not activate because subcontract externalId is null",
                "beneficiary_id" to beneficiary.id
            )
            return false
        }

        return if (subcontract.paymentType == PaymentModel.PRE_PAY) {
            if (shouldCheckForPreActivationPayment(subcontract.id)) {
                logger.info(
                    "Checking for pre activation payment for beneficiary",
                    "beneficiary_id" to beneficiary.id
                )

                preActivationPaymentService.getBySubcontractId(
                    subContractId = subcontract.id,
                    options = PreActivationPaymentService.FindOptions(
                        listOf(PreActivationPaymentStatus.PAID)
                    )
                ).map {
                    if (it.isNotEmpty()) true else {
                        logger.info(
                            "Beneficiary should not activate because is prepay and pre activation payment is not paid",
                            "beneficiary_id" to beneficiary.id
                        )

                        false
                    }
                }.get()
            } else {
                memberInvoiceGroupService.getBySubcontractId(
                    subcontract.id, MemberInvoiceGroupService.FindOptions(
                        status = listOf(
                            MemberInvoiceGroupStatus.PAID,
                        )
                    )
                ).map {
                    if (it.isNotEmpty()) true else {
                        logger.info(
                            "Beneficiary should not activate because is prepay and member invoice group is not paid",
                            "beneficiary_id" to beneficiary.id
                        )

                        false
                    }
                }.get()
            }
        } else true
    }

    private suspend fun findOrInferSubcontractForBeneficiary(beneficiary: BeneficiaryModel) =
        if (beneficiary.companySubContractId != null) {
            subcontractService.get(beneficiary.companySubContractId!!)
        } else {
            subcontractService.findByCompanyId(beneficiary.companyId).map { subcontracts ->
                if (subcontracts.size == 1) {
                    val subcontract = subcontracts.first()
                    beneficiaryDataService.update(
                        beneficiary.copy(companySubContractId = subcontract.id)
                    ).then {
                        kafkaProducerService.produce(
                            BeneficiaryChangedEvent(it.toTransport(), NotificationEventAction.UPDATED),
                            it.id.toString()
                        )
                    }.get()
                    subcontract
                } else {
                    logger.error("Failed to infer subcontract for beneficiary", "beneficiary" to beneficiary)
                    throw FailedToInferSubcontractException(beneficiary.toTransport())
                }
            }
        }

    private fun isPreActivationEnabled() =
        FeatureService.get(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", false)

    private suspend fun shouldCheckForPreActivationPayment(subContractId: UUID): Boolean {

        return if (isPreActivationEnabled()) {
            val companyContract = contractService.findBySubcontractId(subContractId).get()

            companyContract.groupCompany?.let { groupCompany ->
                return FeatureService.inList(
                    FeatureNamespace.MONEY_IN,
                    "pre_activation_payment_for_group_company",
                    groupCompany,
                    false
                ).also {
                    logger.info(
                        "The FF pre_activation_payment_for_group_company", "values" to FeatureService.get(
                            FeatureNamespace.MONEY_IN,
                            "pre_activation_payment_for_group_company", emptyList<String>()
                        )
                    )
                }
            }

            false
        } else false

    }

    data class BeneficiaryCompanyInfo(
        val company: CompanyModel,
        val subContract: CompanySubContractModel,
        val contract: CompanyContractModel,
    )

    data class BeneficiaryParent(
        val beneficiary: BeneficiaryModel,
        val person: Person
    )

}
