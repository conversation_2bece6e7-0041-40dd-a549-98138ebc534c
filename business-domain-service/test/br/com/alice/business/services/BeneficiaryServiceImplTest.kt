package br.com.alice.business.services

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.BeneficiaryArchivedEvent
import br.com.alice.business.events.BeneficiaryCanceledEvent
import br.com.alice.business.events.BeneficiaryChangedEvent
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.business.events.BeneficiaryCreatedForActiveMemberEvent
import br.com.alice.business.events.BeneficiaryUpdatedEvent
import br.com.alice.business.events.NewBeneficiaryRelationshipAssignedEvent
import br.com.alice.business.events.RequestCassiMemberUpdateEvent
import br.com.alice.business.exceptions.AlreadyAliceMemberException
import br.com.alice.business.exceptions.BeneficiaryActivationValidationException
import br.com.alice.business.exceptions.BeneficiaryAlreadyExistsException
import br.com.alice.business.exceptions.BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany
import br.com.alice.business.exceptions.BeneficiaryMustHaveSubContractIdException
import br.com.alice.business.exceptions.CompanySubcontractRequiredException
import br.com.alice.business.exceptions.EmptyAvailableProductListException
import br.com.alice.business.exceptions.FailedToInferSubcontractException
import br.com.alice.business.exceptions.HolderIsDependentException
import br.com.alice.business.exceptions.InvalidActivationCancellationStateException
import br.com.alice.business.exceptions.InvalidBeneficiaryCanceledRequest
import br.com.alice.business.exceptions.InvalidBeneficiaryInitialProductException
import br.com.alice.business.exceptions.InvalidBeneficiaryOrMembership
import br.com.alice.business.exceptions.InvalidCancelationDescription
import br.com.alice.business.exceptions.InvalidDependentFieldException
import br.com.alice.business.exceptions.InvalidEmployeeFieldException
import br.com.alice.business.exceptions.InvalidHiredAtFieldException
import br.com.alice.business.exceptions.MismatchCompanySubcontractException
import br.com.alice.business.exceptions.NationalIdAlreadyUsedByHolderException
import br.com.alice.business.exceptions.SubcontractIsDifferentFromHolderException
import br.com.alice.business.logics.BeneficiaryLogic
import br.com.alice.business.logics.BeneficiaryLogic.validateAndUpdateCancelFields
import br.com.alice.business.logics.BeneficiaryLogic.withDependents
import br.com.alice.business.logics.BeneficiaryLogic.withOnboarding
import br.com.alice.business.logics.BeneficiaryOnboardingLogic
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.business.metrics.BeneficiaryMetric.Status.FAILURE
import br.com.alice.business.metrics.BeneficiaryMetric.Status.SUCCESS
import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.business.model.TestBeneficiaryTransportFactory
import br.com.alice.business.model.toBeneficiary
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.extensions.thenError
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.ContractFile
import br.com.alice.data.layer.models.ContractType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberLifecycleEventType
import br.com.alice.data.layer.models.MemberLifecycleReasonEvents
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatus.ACTIVE
import br.com.alice.data.layer.models.MemberStatus.CANCELED
import br.com.alice.data.layer.models.MemberStatus.PENDING
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.ProductType.ADESAO
import br.com.alice.data.layer.models.ProductType.B2B
import br.com.alice.data.layer.models.ProductType.B2C
import br.com.alice.data.layer.models.withPhases
import br.com.alice.data.layer.services.BeneficiaryModelDataService
import br.com.alice.membership.client.onboarding.GasProcessService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Ignore
import kotlin.test.Test

class BeneficiaryServiceImplTest {
    private val beneficiaryDataService: BeneficiaryModelDataService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val companyService: CompanyService = mockk()
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val personService: PersonService = mockk()
    private val gasProcessService: GasProcessService = mockk()
    private val cassiMemberService: CassiMemberService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val contractService: CompanyContractService = mockk()
    private val subcontractService: CompanySubContractService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val beneficiarySlot = slot<BeneficiaryModel>()
    private val beneficiaryListSlot = slot<List<BeneficiaryModel>>()

    private val beneficiaryService =
        BeneficiaryServiceImpl(
            beneficiaryDataService,
            beneficiaryOnboardingService,
            kafkaProducerService,
            companyService,
            memberService,
            productService,
            billingAccountablePartyService,
            personService,
            gasProcessService,
            cassiMemberService,
            onboardingService,
            contractService,
            subcontractService,
            memberInvoiceGroupService,
            companyProductPriceListingService,
            preActivationPaymentService
        )

    @BeforeTest
    fun setup() {
        mockkObject(BeneficiaryLogic, BeneficiaryOnboardingLogic, BeneficiaryMetric)
        beneficiarySlot.clear()
        beneficiaryListSlot.clear()
    }

    @Test
    fun `#archiveBeneficiaryById - get beneficiary and archive it`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel().copy(archived = false)
        val beneficiaryCanceled = beneficiary.copy(archived = true)

        coEvery { beneficiaryDataService.get(beneficiaryId) } returns beneficiary
        coEvery { beneficiaryDataService.update(beneficiaryCanceled) } returns beneficiaryCanceled
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryArchivedEvent(beneficiaryCanceled.toTransport())
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiaryCanceled.toTransport(),
                    NotificationEventAction.UPDATED
                ),
                beneficiaryCanceled.id.toString()
            )
        } returns mockk()

        val result = beneficiaryService.archiveBeneficiaryById(beneficiaryId)
        assertThat(result).isSuccessWithData(beneficiaryCanceled.toTransport())

        coVerifyOnce { beneficiaryDataService.get(any()) }
        coVerifyOnce { beneficiaryDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#get returns beneficiary with onboarding when requested`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        scope(this.id.eq(beneficiary.id)) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns listOf(beneficiary)
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding.toTransport()

        val expectedBeneficiary = beneficiary.withOnboarding(beneficiaryOnboarding).toTransport()

        val result =
            beneficiaryService.get(beneficiary.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true))
        assertThat(result).isSuccessWithData(expectedBeneficiary)

        coVerifyOnce { beneficiaryDataService.find(any()) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#get returns beneficiary with dependents when requested`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryDependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id).toModel()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        scope(this.id.eq(beneficiary.id) or this.parentId.eq(beneficiary.id)) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns listOf(beneficiary, beneficiaryDependent)

        val expectedBeneficiary = beneficiary.withDependents(listOf(beneficiaryDependent))

        val result =
            beneficiaryService.get(beneficiary.id, findOptions = BeneficiaryService.FindOptions(withDependents = true))
        assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

        coVerifyOnce { beneficiaryDataService.find(any()) }
    }

    @Test
    fun `#get returns beneficiary with no onboarding by default`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        scope(this.id.eq(beneficiary.id)) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns listOf(beneficiary)

        val result = beneficiaryService.get(beneficiary.id)
        assertThat(result).isSuccessWithData(beneficiary.toTransport())

        coVerifyOnce { beneficiaryDataService.find(any()) }
        coVerifyNone { beneficiaryOnboardingService.findByBeneficiaryId(any()) }
    }

    @Test
    fun `#get returns beneficiary with no onboarding when requested but not found`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        scope(this.id.eq(beneficiary.id)) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns listOf(beneficiary)
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns NotFoundException("Onboarding was not found")

        val result =
            beneficiaryService.get(beneficiary.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true))
        assertThat(result).isSuccessWithData(beneficiary.toTransport())

        coVerifyOnce { beneficiaryDataService.find(any()) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#get returns beneficiary with onboarding and dependents when requested`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryDependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id).toModel()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        scope(this.id.eq(beneficiary.id) or this.parentId.eq(beneficiary.id)) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns listOf(beneficiary, beneficiaryDependent)
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

        val expectedBeneficiary = beneficiary
            .withDependents(listOf(beneficiaryDependent))
            .withOnboarding(beneficiaryOnboarding.toModel())

        val result =
            beneficiaryService.get(
                beneficiary.id,
                findOptions = BeneficiaryService.FindOptions(
                    withOnboarding = true,
                    withDependents = true,
                )
            )

        assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

        coVerifyOnce { beneficiaryDataService.find(any()) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
    }

    @Test
    fun `#findByCompanyId returns a list of beneficiaries`() = runBlocking {
        val companyId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCompanyId(companyId)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companyId.eq(companyId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByCompanyIds returns a list of beneficiaries`() = runBlocking {
        val companyId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCompanyIds(listOf(companyId))
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companyId.inList(listOf(companyId)) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByCompanyId returns a list of beneficiaries given a range`() = runBlocking {
        val companyId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val range = IntRange(0, 19)

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCompanyId(
            companyId,
            BeneficiaryService.FindOptions(withOnboarding = false, withDependents = false),
            range
        )

        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.archived.eq(false)
                    }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        }
    }

    @Test
    fun `#findByCompanyId returns a list of beneficiaries with onboarding`() = runBlocking<Unit> {
        val companyId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

        val expectedBeneficiaries = beneficiaries.map { it.withOnboarding(beneficiaryOnboarding.toModel()) }

        val result = beneficiaryService.findByCompanyId(
            companyId,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companyId.eq(companyId) and this.archived.eq(false) }
            })
        }

        beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
    }

    @Test
    fun `#findByCompanyId returns a list of beneficiaries with dependents`() = runBlocking {
        val companyId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiaries.first().id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children

        val expectedBeneficiaries = beneficiaries.map { it.withDependents(children) }

        val result = beneficiaryService.findByCompanyId(
            companyId,
            findOptions = BeneficiaryService.FindOptions(withDependents = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companyId.eq(companyId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByCompanySubContractId returns a list of beneficiaries`() = runBlocking {
        val companySubContractId = RangeUUID.generate()
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(
                companySubContractId = companySubContractId
            ).toModel()
        )

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCompanySubContractId(companySubContractId)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companySubContractId.eq(companySubContractId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByCompanySubContractId returns a list of beneficiaries with onboarding`() = runBlocking<Unit> {
        val companySubContractId = RangeUUID.generate()
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(
                companySubContractId = companySubContractId
            ).toModel()
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

        val expectedBeneficiaries = beneficiaries.map { it.withOnboarding(beneficiaryOnboarding.toModel()) }

        val result = beneficiaryService.findByCompanySubContractId(
            companySubContractId,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true),
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companySubContractId.eq(companySubContractId) and this.archived.eq(false) }
            })
        }

        beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
    }

    @Test
    fun `#findByCompanySubContractId returns a list of beneficiaries with dependents`() = runBlocking {
        val companySubContractId = RangeUUID.generate()
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(companySubContractId = companySubContractId).toModel()
        )
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiaries.first().id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children

        val expectedBeneficiaries = beneficiaries.map { it.withDependents(children) }

        val result = beneficiaryService.findByCompanySubContractId(
            companySubContractId,
            findOptions = BeneficiaryService.FindOptions(withDependents = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.companySubContractId.eq(companySubContractId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByMemberId returns expected beneficiary`() = runBlocking {
        val memberId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        } returns beneficiary

        val result = beneficiaryService.findByMemberId(memberId)
        assertThat(result).isSuccessWithData(beneficiary.toTransport())

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByMemberIds returns expected beneficiary`() = runBlocking {
        val memberIds = listOf(RangeUUID.generate())
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()

        coEvery { beneficiaryDataService.find(any()) } returns listOf(beneficiary)

        val result = beneficiaryService.findByMemberIds(memberIds)
        assertThat(result).isSuccessWithData(listOf(beneficiary.toTransport()))

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.memberId.inList(memberIds) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByParentId returns a list of beneficiaries`() = runBlocking {
        val parentId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByParentId(parentId)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.parentId.eq(parentId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByParentId returns a list of beneficiaries with onlyDirectDependents`() = runBlocking {
        val parentId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val parentTypesForDirectDependency = listOf(
            ParentBeneficiaryRelationType.CHILD,
            ParentBeneficiaryRelationType.PARTNER,
            ParentBeneficiaryRelationType.SPOUSE,
            ParentBeneficiaryRelationType.FOSTER_CHILD,
            ParentBeneficiaryRelationType.STEPCHILD,
        )
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(parentId) and this.archived.eq(false) and this.parentBeneficiaryRelationType.inList(
                        parentTypesForDirectDependency
                    )
                }
            })
        } returns beneficiaries

        val result = beneficiaryService.findByParentId(parentId, onlyDirectDependents = true)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce { beneficiaryDataService.find(any()) }
    }

    @Test
    fun `#findByParentId returns a list of beneficiaries with onboarding`() = runBlocking<Unit> {
        val parentId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

        val expectedBeneficiaries = beneficiaries.map { it.withOnboarding(beneficiaryOnboarding.toModel()) }

        val result = beneficiaryService.findByParentId(
            parentId,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.parentId.eq(parentId) and this.archived.eq(false) }
            })
        }

        beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
    }

    @Test
    fun `#findByParentId returns a list of beneficiaries with dependents`() = runBlocking {
        val parentId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiaries.first().id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children

        val expectedBeneficiaries = beneficiaries.map { it.withDependents(children) }

        val result = beneficiaryService.findByParentId(
            parentId,
            findOptions = BeneficiaryService.FindOptions(withDependents = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.parentId.eq(parentId) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByParentId returns a list of beneficiaries with dependents and onboarding`() = runBlocking<Unit> {
        val parentId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiaries.first().id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding.toTransport()

        val expectedBeneficiaries =
            beneficiaries.map { it.withDependents(children).withOnboarding(beneficiaryOnboarding) }

        val result = beneficiaryService.findByParentId(
            parentId,
            findOptions = BeneficiaryService.FindOptions(withDependents = true, withOnboarding = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where { this.parentId.eq(parentId) and this.archived.eq(false) }
            })
        }
        beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
    }

    @Test
    fun `#findByPersonId returns beneficiary`() = runBlocking {
        val personId = PersonId()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val member = TestModelFactory.buildMember()

        coEvery { memberService.getCurrent(any()) } returns member
        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary

        val result = beneficiaryService.findByPersonId(personId)
        assertThat(result).isSuccessWithData(beneficiary.toTransport())

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(member.id) and this.archived.eq(false) }
            })
        }
    }

    @Test
    fun `#findByPersonId returns beneficiary with onboarding`() = runBlocking {
        val personId = PersonId()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()
        val member = TestModelFactory.buildMember()

        coEvery { memberService.getCurrent(any()) } returns member
        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding.toTransport()

        val expectedBeneficiary = beneficiary.withOnboarding(beneficiaryOnboarding)

        val result = beneficiaryService.findByPersonId(
            personId,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(member.id) and this.archived.eq(false) }
            })
        }

        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
    }

    @Test
    fun `#findByPersonId returns beneficiary with dependents`() = runBlocking {
        val personId = PersonId()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())
        val member = TestModelFactory.buildMember()

        coEvery { memberService.getCurrent(any()) } returns member
        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiary.id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children

        val expectedBeneficiary = beneficiary.withDependents(children)

        val result = beneficiaryService.findByPersonId(
            personId,
            findOptions = BeneficiaryService.FindOptions(withDependents = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(member.id) and this.archived.eq(false) }
            })
        }

    }

    @Test
    fun `#findByFilters - calls dataService`() = runBlocking {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())

        val range = IntRange(0, 19)
        val companyId = RangeUUID.generate()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.archived.eq(false)
                    }.orderBy { createdAt }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns beneficiaries
        val result = beneficiaryService.findByFilters(
            personId = null,
            companyId = companyId,
            parentId = null,
            currentPhase = null,
            range = range
        )
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce { beneficiaryDataService.find(any()) }
    }

    @Test
    fun `#findByFilters - calls dataService with onboarding`() = runBlocking<Unit> {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        val range = IntRange(0, 19)
        val companyId = RangeUUID.generate()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.archived.eq(false)
                    }.orderBy { createdAt }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns beneficiaries
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding.toTransport()

        val expectedBeneficiaries = beneficiaries.map { it.withOnboarding(beneficiaryOnboarding) }

        val result = beneficiaryService.findByFilters(
            personId = null,
            companyId = companyId,
            parentId = null,
            currentPhase = null,
            range = range,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyOnce { beneficiaryDataService.find(any()) }

        beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
    }

    @Test
    fun `#findByFilters given a PersonId, should find by member with onboarding`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember(personId = personId)
        coEvery { memberService.getCurrent(personId) } returns member

        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where {
                    this.memberId.eq(member.id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns beneficiary
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding.toTransport()

        val expectedBeneficiaries = listOf(beneficiary.withOnboarding(beneficiaryOnboarding))
        val range = IntRange(0, 19)

        val result = beneficiaryService.findByFilters(
            personId = personId,
            companyId = null,
            parentId = null,
            currentPhase = null,
            range = range,
            findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
        )

        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerifyNone { BeneficiaryLogic.buildFilterQuery(any(), any(), any()) }
        coVerifyOnce { memberService.getCurrent(personId) }
        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where {
                    this.memberId.eq(member.id) and this.archived.eq(
                        false
                    )
                }
            })
        }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
    }

    @Test
    fun `#findByFilters - when currentPhase is not null, should call findByCurrentPhase method`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary(id = beneficiaryId).toModel())
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        val range = IntRange(0, 19)
        val companyId = RangeUUID.generate()

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
        coEvery {
            beneficiaryOnboardingService.findByCurrentPhase(
                BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                range,
                flowType = null,
                findOptions = BeneficiaryOnboardingService.FindOptions(withPhases = false),
            )
        } returns listOf(
            beneficiaryOnboarding.toTransport()
        )

        val result = beneficiaryService.findByFilters(
            personId = null,
            companyId = companyId,
            parentId = null,
            currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            range = range,
        )
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce { beneficiaryDataService.find(any()) }
    }

    @Test
    fun `#findByFilters - when currentPhase is not null, should call findByCurrentPhase method with onboarding`() =
        runBlocking<Unit> {
            val beneficiaryId = RangeUUID.generate()
            val beneficiaries = listOf(TestModelFactory.buildBeneficiary(id = beneficiaryId).toModel())
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()

            val range = IntRange(0, 19)
            val companyId = RangeUUID.generate()

            coEvery { beneficiaryDataService.find(any()) } returns beneficiaries
            coEvery {
                beneficiaryOnboardingService.findByCurrentPhase(
                    BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    range,
                    flowType = null,
                    findOptions = BeneficiaryOnboardingService.FindOptions(withPhases = false),
                )
            } returns listOf(
                beneficiaryOnboarding.toTransport()
            )
            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns
                    beneficiaryOnboarding.toTransport()

            val expected = beneficiaries.map { it.withOnboarding(beneficiaryOnboarding) }

            val result = beneficiaryService.findByFilters(
                personId = null,
                companyId = companyId,
                parentId = null,
                currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                range = range,
                findOptions = BeneficiaryService.FindOptions(withOnboarding = true),
            )

            assertThat(result).isSuccessWithData(expected.map { it.toTransport() })

            coVerifyOnce { beneficiaryDataService.find(any()) }
            beneficiaries.map { coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(it.id) } }
        }

    @Test
    fun `#findByFilters - calls dataService with dependents`() = runBlocking {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val range = IntRange(0, 19)
        val companyId = RangeUUID.generate()
        val children = listOf(TestModelFactory.buildBeneficiary().toModel())

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.archived.eq(false)
                    }.orderBy { createdAt }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns beneficiaries
        coEvery {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentId.eq(beneficiaries.first().id) and this.archived.eq(
                        false
                    )
                }
            })
        } returns children

        val expectedBeneficiaries = beneficiaries.map { it.withDependents(children) }

        val result = beneficiaryService.findByFilters(
            personId = null,
            companyId = companyId,
            parentId = null,
            currentPhase = null,
            range = range,
            findOptions = BeneficiaryService.FindOptions(withDependents = true)
        )
        assertThat(result).isSuccessWithData(expectedBeneficiaries.map { it.toTransport() })

        coVerify(exactly = 2) { beneficiaryDataService.find(any()) }
    }

    @Test
    fun `#getByIdAndPersonId - calls dataService`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where { this.id.eq(beneficiary.id) and this.personId.eq(beneficiary.personId) }
            })
        } returns beneficiary

        val result = beneficiaryService.getByIdAndPersonId(
            beneficiary.id,
            beneficiary.personId
        )

        assertThat(result).isSuccessWithData(beneficiary.toTransport())

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.id.eq(beneficiary.id) and this.personId.eq(beneficiary.personId) }
            })
        }
    }

    @Test
    fun `#countByFilters - calls dataService`() = runBlocking {
        val count = 12
        val companyId = RangeUUID.generate()

        coEvery {
            beneficiaryDataService.count(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.archived.eq(false)
                    }
                }
            )
        } returns count
        val result = beneficiaryService.countByFilters(null, companyId, null, null)
        assertThat(result).isSuccessWithData(count)

        coVerifyOnce { beneficiaryDataService.count(any()) }
    }

    @Test
    fun `#countByFilters - when currentPhase is not null, should call countByCurrentPhase method`() = runBlocking {
        val currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val companyId = RangeUUID.generate()

        coEvery { beneficiaryOnboardingService.countByCurrentPhase(any()) } returns listOf(
            beneficiaryOnboarding
        ).count()

        val result = beneficiaryService.countByFilters(
            personId = null,
            companyId,
            parentId = null,
            currentPhase
        )
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { beneficiaryOnboardingService.countByCurrentPhase(currentPhase) }
    }

    @Test
    fun `#addBeneficiaryByBeneficiaryWithOnboardingInfo with valid initialProductId should return expected success`() =
        runBlocking {
            val initialProductId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val company = TestModelFactory.buildCompany(availableProducts = listOf(initialProductId))
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val phase = BeneficiaryOnboardingPhaseType.FINISHED
            val beneficiaryToAdd = beneficiary.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT,
            )

            coEvery { beneficiaryDataService.add(capture(beneficiarySlot)) } returns beneficiaryToAdd
            coEvery { companyService.get(beneficiary.companyId) } returns company
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(beneficiary.companySubContractId!!) } returns listOf(
                initialProductId
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = phase,
                    ),
                    beneficiaryToAdd.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    beneficiaryToAdd.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary.toTransport(),
                initialProductId = initialProductId,
                flowType = flowType,
                phase = phase,
            )
            assertThat(result).isSuccessWithData(beneficiaryToAdd.toTransport())

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(beneficiaryToAdd)

            coVerifyOnce { companyService.get(any()) }
            coVerifyOnce { beneficiaryDataService.add(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }

        }

    @Test
    fun `#addBeneficiaryByBeneficiaryWithOnboardingInfo with null initialProductId should return expected success`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val company = TestModelFactory.buildCompany()
            val initialProductId = RangeUUID.generate()
            val beneficiaryToAdd = beneficiary.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT,
            )

            coEvery { beneficiaryDataService.add(capture(beneficiarySlot)) } returns beneficiaryToAdd
            coEvery { companyService.get(beneficiary.companyId) } returns company
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(initialProductId)
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        initialProductId = initialProductId,
                        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    beneficiaryToAdd.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    beneficiaryToAdd.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary.toTransport(),
                initialProductId = initialProductId,
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            )
            assertThat(result).isSuccessWithData(beneficiaryToAdd.toTransport())

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(beneficiaryToAdd)
            coVerifyOnce { companyService.get(any()) }
            coVerifyOnce { beneficiaryDataService.add(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#addBeneficiaryForExistentMember with null hiredAt should return Exception`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary(hiredAt = null)
            val initialProductId = RangeUUID.generate()

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary,
                initialProductId = initialProductId,
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            )
            assertThat(result).isFailureOfType(InvalidHiredAtFieldException::class)

        }

    @Test
    fun `#addBeneficiaryForExistentMember with null hiredAt should return success when hiredAt null and skipValidatoin`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary(hiredAt = null).toModel()
            val company = TestModelFactory.buildCompany()
            val initialProductId = RangeUUID.generate()
            val beneficiaryToAdd = beneficiary.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT,
            )

            coEvery { beneficiaryDataService.add(capture(beneficiarySlot)) } returns beneficiaryToAdd
            coEvery { companyService.get(beneficiary.companyId) } returns company
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(initialProductId)
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        initialProductId = initialProductId,
                        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    beneficiaryToAdd.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    beneficiaryToAdd.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary.toTransport(),
                initialProductId = initialProductId,
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                ignoreHiredAtValidation = true
            )
            assertThat(result).isSuccessWithData(beneficiaryToAdd.toTransport())

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(beneficiaryToAdd)
            coVerifyOnce { companyService.get(any()) }
            coVerifyOnce { beneficiaryDataService.add(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }


    @Test
    fun `#addBeneficiaryByBeneficiaryWithOnboardingInfo with valid cassiMemberInfo should return expected success`() =
        runBlocking {
            val initialProductId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val company = TestModelFactory.buildCompany(availableProducts = listOf(initialProductId))
            val beneficiaryToAdd = beneficiary.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT,
            )

            coEvery { beneficiaryDataService.add(capture(beneficiarySlot)) } returns beneficiaryToAdd
            coEvery { companyService.get(beneficiary.companyId) } returns company
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        initialProductId = initialProductId,
                        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    beneficiaryToAdd.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiaryToAdd.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    beneficiaryToAdd.id.toString()
                )
            } returns mockk()
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(initialProductId)

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary.toTransport(),
                initialProductId = initialProductId,
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            )
            assertThat(result).isSuccessWithData(beneficiaryToAdd.toTransport())

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(beneficiaryToAdd)
            coVerifyOnce { companyService.get(any()) }
            coVerifyOnce { beneficiaryDataService.add(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#addBeneficiaryByBeneficiaryWithOnboardingInfo return expected error when beneficiary already exists`() =
        runBlocking {
            val initialProductId = RangeUUID.generate()
            val personId = PersonId()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = personId).toModel()
            val beneficiaryToAdd = beneficiary.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405,
                gracePeriodTypeReason = GracePeriodTypeReason.DEFAULT,
            )
            val company = TestModelFactory.buildCompany(availableProducts = listOf(initialProductId))
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val phase = BeneficiaryOnboardingPhaseType.FINISHED

            coEvery { companyService.get(beneficiary.companyId) } returns company
            coEvery { beneficiaryDataService.add(capture(beneficiarySlot)) } returns BeneficiaryAlreadyExistsException(
                personId,
                beneficiary.memberId
            )
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(initialProductId)

            val result = beneficiaryService.addBeneficiaryForExistentMember(
                beneficiary = beneficiary.toTransport(),
                initialProductId = initialProductId,
                flowType = flowType,
                phase = phase,
            )

            ResultAssert(result).isFailureOfType(BeneficiaryAlreadyExistsException::class)

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(beneficiaryToAdd)
            coVerifyOnce { companyService.get(any()) }
            coVerifyOnce { beneficiaryDataService.add(any()) }
        }

    @Test
    fun `#countByIds returns total of beneficiaries by ids`() = runBlocking {
        val total = 12
        val ids = listOf(RangeUUID.generate())

        coEvery { beneficiaryDataService.count(any()) } returns total

        val result = beneficiaryService.countByIds(ids)
        assertThat(result).isSuccessWithData(total)

        coVerifyOnce { beneficiaryDataService.count(queryEq { where { this.id.inList(ids) and this.archived.eq(false) } }) }
    }

    @Test
    fun `#findByIds returns companies by ids`() = runBlocking {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val ids = listOf(RangeUUID.generate())

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByIds(ids)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce { beneficiaryDataService.find(queryEq { where { this.id.inList(ids) and this.archived.eq(false) } }) }
    }

    @Test
    fun `#findByCanceledAtBetweenInclusive - returns the beneficiaries`() = runBlocking {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val canceledAtStart = LocalDate.now()
        val canceledAtNow = canceledAtStart.minusDays(10)

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCanceledAtBetweenInclusive(canceledAtStart, canceledAtNow)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.canceledAt.greaterEq(canceledAtStart.atStartOfDay()) and
                            this.canceledAt.lessEq(canceledAtNow.atStartOfDay()) and
                            this.archived.eq(false)
                }
            })
        }
    }

    @Test
    fun `#findByCanceledAtBetweenInclusivePaginated - returns the beneficiaries`() = runBlocking {
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val canceledAtStart = LocalDate.now()
        val canceledAtNow = canceledAtStart.minusDays(10)

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCanceledAtBetweenInclusivePaginated(canceledAtStart, canceledAtNow, 2, 3)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.canceledAt.greaterEq(canceledAtStart.atStartOfDay()) and
                            this.canceledAt.lessEq(canceledAtNow.atStartOfDay()) and
                            this.archived.eq(false)
                }.offset { 2 }.limit { 3 }
            })
        }
    }

    @Test
    fun `#abortBeneficiaryScheduledCancel if canceledAt is before today`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary(
            canceledAt = LocalDateTime.now().minusDays(1),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST
        ).toModel()

        coEvery { beneficiaryDataService.get(beneficiary.id) } returns beneficiary

        val result = beneficiaryService.abortBeneficiaryScheduledCancel(beneficiary.id)

        assertThat(result).isFailureOfType(InvalidBeneficiaryOrMembership::class)

        coVerifyNone { beneficiaryDataService.update(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#abortBeneficiaryScheduledCancel if canceledAt is null`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary(
            canceledAt = null,
            canceledReason = null
        ).toModel()

        coEvery { beneficiaryDataService.get(beneficiary.id) } returns beneficiary

        val result = beneficiaryService.abortBeneficiaryScheduledCancel(beneficiary.id)

        assertThat(result).isFailureOfType(InvalidBeneficiaryOrMembership::class)

        coVerifyNone { beneficiaryDataService.update(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#abortBeneficiaryScheduledCancel if canceledAt is after today`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary(
            canceledAt = LocalDateTime.now().plusDays(10),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST
        ).toModel()

        val updatedBeneficiary = beneficiary.copy(
            canceledAt = null,
            canceledReason = null
        )

        coEvery { beneficiaryDataService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryDataService.update(updatedBeneficiary) } returns updatedBeneficiary
        coEvery { kafkaProducerService.produce(any()) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiary = updatedBeneficiary.toTransport(),
                    eventAction = NotificationEventAction.UPDATED
                ),
                updatedBeneficiary.id.toString()
            )
        } returns mockk()
        val result = beneficiaryService.abortBeneficiaryScheduledCancel(beneficiary.id)

        assertThat(result).isSuccessWithData(
            beneficiary.copy(
                canceledAt = null,
                canceledReason = null
            ).toTransport()
        )

        coVerifyOnce { beneficiaryDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#findByCompanyIdAndPersonIds calls dataService with correct filters`() = runBlocking {
        val companyId = RangeUUID.generate()
        val personIds = listOf(PersonId(), PersonId())
        val findOptions = BeneficiaryService.FindOptions(withOnboarding = false, withDependents = false)
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary().toModel(),
            TestModelFactory.buildBeneficiary().toModel()
        )

        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByCompanyIdAndPersonIds(companyId, personIds, findOptions)

        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.companyId.eq(companyId) and
                            this.personId.inList(personIds) and
                            this.archived.eq(false)
                }
            })
        }
        coVerifyNone {
            beneficiaryDataService.find { where { this.parentId.eq(beneficiaries[0].id) and this.archived.eq(false) } }
        }
        coVerifyNone {
            beneficiaryDataService.find { where { this.parentId.eq(beneficiaries[1].id) and this.archived.eq(false) } }
        }
        coVerify { beneficiaryOnboardingService wasNot called }

    }

    @Test
    fun `#findByCompanyIdAndPersonIds calls dataService with correct filters if findOptions is selected`() =
        runBlocking {
            val companyId = RangeUUID.generate()
            val personIds = listOf(PersonId(), PersonId())
            val findOptions = BeneficiaryService.FindOptions(withOnboarding = true, withDependents = true)
            val beneficiary = TestModelFactory.buildBeneficiary()
            val beneficiaries = listOf(beneficiary.toModel())
            val dependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id).toModel()
            val dependents = listOf(dependent)
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiary.id)
            val expectedBeneficiary = beneficiary.copy(
                dependents = dependents.map { it.toTransport() },
                onboarding = beneficiaryOnboarding
            )
            val expectedBeneficiaries = listOf(expectedBeneficiary)

            coEvery {
                beneficiaryDataService.find(queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.personId.inList(personIds) and
                                this.archived.eq(false)
                    }
                })
            } returns beneficiaries

            coEvery {
                beneficiaryDataService.find(queryEq {
                    where {
                        this.parentId.eq(beneficiary.id) and this.archived.eq(
                            false
                        )
                    }
                })
            } returns dependents

            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(
                    beneficiary.id,
                    any()
                )
            } returns beneficiaryOnboarding

            val result = beneficiaryService.findByCompanyIdAndPersonIds(companyId, personIds, findOptions)

            assertThat(result).isSuccessWithData(expectedBeneficiaries)

            coVerifyOnce {
                beneficiaryDataService.find(queryEq {
                    where {
                        this.companyId.eq(companyId) and
                                this.personId.inList(personIds) and
                                this.archived.eq(false)
                    }
                })
            }

            coVerifyOnce {
                beneficiaryDataService.find(queryEq {
                    where {
                        this.parentId.eq(beneficiary.id) and this.archived.eq(
                            false
                        )
                    }
                })
            }
            coVerifyOnce {
                beneficiaryOnboardingService.findByBeneficiaryId(
                    beneficiary.id,
                    BeneficiaryOnboardingService.FindOptions(true)
                )
            }
        }

    @Test
    @Ignore
    fun `#update should update beneficiary correctly`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryUpdated = beneficiary.copy(hiredAt = LocalDateTime.now().minusDays(1))

        coEvery { beneficiaryDataService.update(any()) } returns beneficiaryUpdated
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryUpdatedEvent(beneficiaryUpdated.toTransport())
            )
        } returns mockk()

        val result = beneficiaryService.update(beneficiary.toTransport())
        assertThat(result).isSuccessWithData(beneficiaryUpdated.toTransport())

        coVerifyOnce { beneficiaryService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    @Ignore
    fun `#validateToUpdate should update with validation in beneficiary correctly`() = runBlocking<Unit> {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryUpdated = beneficiary.copy(hiredAt = LocalDateTime.now().minusDays(1))

        coEvery { beneficiaryDataService.update(any()) } returns beneficiaryUpdated
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryUpdatedEvent(beneficiaryUpdated.toTransport())
            )
        } returns mockk()

        val result = beneficiaryService.validateToUpdate(beneficiary.toTransport())
        assertThat(result).isSuccessWithData(beneficiaryUpdated.toTransport())

        coVerifyOnce { beneficiaryService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyNone { beneficiaryDataService.get(any()) }
    }

    @Test
    @Ignore
    fun `#validateToUpdate should update with validation and remove any relationship data when parent beneficiary is null`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary(
                type = BeneficiaryType.DEPENDENT,
                parentBeneficiary = RangeUUID.generate(),
                parentPerson = PersonId(),
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                parentBeneficiaryRelatedAt = LocalDateTime.now()
            ).toModel()

            val beneficiaryUpdated = beneficiary.copy(
                parentBeneficiary = null,
                parentPerson = null,
                parentBeneficiaryRelationType = null,
                parentBeneficiaryRelatedAt = null
            )

            coEvery { beneficiaryDataService.update(any()) } returns beneficiaryUpdated
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryUpdatedEvent(beneficiaryUpdated.toTransport())
                )
            } returns mockk()

            val result = beneficiaryService.validateToUpdate(
                beneficiary.copy(
                    parentBeneficiary = null,
                    type = BeneficiaryType.EMPLOYEE,
                    parentBeneficiaryRelationType = null,
                    parentBeneficiaryRelatedAt = null,
                ).toTransport()
            )
            assertThat(result).isSuccessWithData(beneficiaryUpdated.toTransport())

            coVerifyOnce { beneficiaryService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyNone { beneficiaryDataService.get(any()) }
        }

    @Test
    @Ignore
    fun `#validateToUpdate should update with validation and set the parentPerson`() =
        runBlocking {
            val parentBeneficary = TestModelFactory.buildBeneficiary().toModel()
            val beneficiary = TestModelFactory.buildBeneficiary(
                parentBeneficiary = parentBeneficary.id,
                parentPerson = PersonId(),
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                parentBeneficiaryRelatedAt = LocalDateTime.now(),
                type = BeneficiaryType.DEPENDENT,
            ).toModel()

            val beneficiaryUpdated = beneficiary.copy(parentPerson = parentBeneficary.personId)

            coEvery { beneficiaryDataService.update(any()) } returns beneficiaryUpdated
            coEvery { kafkaProducerService.produce(BeneficiaryUpdatedEvent(beneficiaryUpdated.toTransport())) } returns mockk()
            coEvery { beneficiaryDataService.get(parentBeneficary.id) } returns parentBeneficary

            val result = beneficiaryService.validateToUpdate(
                beneficiary.copy(parentBeneficiary = parentBeneficary.id).toTransport()
            )
            assertThat(result).isSuccessWithData(beneficiaryUpdated.toTransport())

            coVerifyOnce { beneficiaryService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { beneficiaryDataService.get(parentBeneficary.id) }
        }

    @Nested
    inner class CancelBeneficiary {

        @Test
        fun `#cancelBeneficiaryById if canceledAt is before today`() = runBlocking {
            val canceledAt = LocalDateTime.now().minusDays(1).toLocalDate()
            val beneficiaryId = RangeUUID.generate()
            val cancelationReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST
            val beneficiaryCancelation = BeneficiaryCancelation(canceledAt, cancelationReason, null, true)

            val result = beneficiaryService.cancelBeneficiaryById(beneficiaryId, beneficiaryCancelation)

            assertThat(result).isFailureOfType(InvalidBeneficiaryCanceledRequest::class)

            coVerifyNone { beneficiaryDataService.update(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#cancelBeneficiaryById if canceledAt is today but beneficiary has canceledAt filled`() = runBlocking {
            val canceledAt = LocalDateTime.now().toLocalDate()
            val beneficiaryId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary(canceledAt = LocalDateTime.now().minusDays(1)).toModel()
            val canceledMember = TestModelFactory.buildMember().cancel()
            val cancelationReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST
            val beneficiaryCancelation = BeneficiaryCancelation(canceledAt, cancelationReason, null, true)

            coEvery { beneficiaryDataService.get(beneficiaryId) } returns beneficiary
            coEvery { memberService.cancelById(beneficiary.memberId) } returns canceledMember

            val result = beneficiaryService.cancelBeneficiaryById(beneficiaryId, beneficiaryCancelation)

            assertThat(result).isSuccessWithData(beneficiary.toTransport())

            coVerifyOnce { beneficiaryDataService.get(any()) }
            coVerifyOnce { memberService.cancelById(any()) }
        }

        @Test
        fun `#cancelBeneficiaryById if canceledReason is another and canceledDescription is empty`() = runBlocking {
            val canceledAt = LocalDateTime.now().toLocalDate()
            val beneficiaryId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val cancelationReason = BeneficiaryCancelationReason.ANOTHER
            val beneficiaryCancelation = BeneficiaryCancelation(canceledAt, cancelationReason, null, true)

            coEvery { beneficiaryDataService.get(any()) } returns beneficiary

            val result = beneficiaryService.cancelBeneficiaryById(beneficiaryId, beneficiaryCancelation)

            assertThat(result).isFailureOfType(InvalidCancelationDescription::class)

            coVerifyOnce { beneficiaryDataService.get(beneficiaryId) }
            coVerifyNone { beneficiaryDataService.update(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#cancelBeneficiaryById if canceledAt is after today`() = runBlocking {
            val canceledAt = LocalDateTime.now().plusDays(1).toLocalDate()
            val beneficiaryId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val cancelationReason = BeneficiaryCancelationReason.ANOTHER
            val cancelationDescription = "Empresa alterou CNPJ"
            val beneficiaryCancelation =
                BeneficiaryCancelation(canceledAt, cancelationReason, cancelationDescription, true)

            coEvery { beneficiaryDataService.get(beneficiaryId) } returns beneficiary
            coEvery {
                beneficiaryDataService.update(
                    beneficiary.copy(
                        canceledAt = canceledAt.atEndOfTheDay(),
                        canceledReason = cancelationReason,
                        canceledDescription = cancelationDescription,
                        hasContributed = true
                    )
                )
            } returns beneficiary
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiary.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    beneficiary.id.toString()
                )
            } returns mockk()
            coEvery { kafkaProducerService.produce(BeneficiaryCanceledEvent(beneficiary.toTransport())) } returns mockk()
            coEvery { kafkaProducerService.produce(BeneficiaryUpdatedEvent(beneficiary.toTransport())) } returns mockk()

            val result = beneficiaryService.cancelBeneficiaryById(beneficiaryId, beneficiaryCancelation)
            assertThat(result).isSuccessWithData(beneficiary.toTransport())

            coVerifyOnce { beneficiaryDataService.get(any()) }
            coVerifyOnce { beneficiaryDataService.update(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
            verifyOnce { BeneficiaryMetric.metrifyBeneficiaryCanceled(any(), any()) }
        }

        @Test
        fun `#cancelBeneficiaryById if canceledAt is today`() = runBlocking {
            val canceledMember = TestModelFactory.buildMember().cancel()
            val canceledAt = LocalDateTime.now().toLocalDate()
            val beneficiaryId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val cancelationReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST
            val beneficiaryCancelation = BeneficiaryCancelation(canceledAt, cancelationReason, null, true)
            val canceledBeneficiary = beneficiary.copy(
                canceledAt = canceledAt.atEndOfTheDay(),
                canceledReason = cancelationReason,
                canceledDescription = null,
                hasContributed = true
            )

            coEvery { beneficiaryDataService.get(beneficiaryId) } returns beneficiary
            coEvery {
                beneficiaryDataService.update(canceledBeneficiary)
            } returns canceledBeneficiary
            coEvery { memberService.cancelById(beneficiary.memberId) } returns canceledMember
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = canceledBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    beneficiary.id.toString()
                )
            } returns mockk()
            coEvery { kafkaProducerService.produce(BeneficiaryCanceledEvent(canceledBeneficiary.toTransport())) } returns mockk()
            coEvery { kafkaProducerService.produce(BeneficiaryUpdatedEvent(canceledBeneficiary.toTransport())) } returns mockk()

            val result = beneficiaryService.cancelBeneficiaryById(beneficiaryId, beneficiaryCancelation)

            assertThat(result).isSuccessWithData(canceledBeneficiary.toTransport())

            coVerifyOnce { beneficiaryDataService.get(any()) }
            coVerifyOnce { beneficiaryDataService.update(any()) }
            coVerifyOnce { memberService.cancelById(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
            verifyOnce { BeneficiaryMetric.metrifyBeneficiaryCanceled(any(), any()) }
        }

        @Test
        fun `#cancelActivation should cancel Member and update Beneficiary`() = runBlocking {
            val member = TestModelFactory.buildMember(status = PENDING)
            val canceledMember = member.cancel()

            val beneficiaryId = RangeUUID.generate()
            val beneficiary = TestModelFactory.buildBeneficiary(
                id = beneficiaryId,
                memberId = member.id,
                activatedAt = LocalDateTime.now().plusDays(1)
            ).toModel()
            val canceledBeneficiary = beneficiary.validateAndUpdateCancelFields(
                BeneficiaryCancelation(
                    canceledAt = LocalDate.now(),
                    canceledReason = BeneficiaryCancelationReason.PRIOR_ACTIVATION,
                    hasContributed = false
                )
            ).get()

            coEvery {
                beneficiaryDataService.find(
                    queryEq {
                        where {
                            scope(this.id.eq(beneficiaryId)) and
                                    this.archived.eq(false)
                        }
                    }
                )
            } returns listOf(beneficiary)
            coEvery { beneficiaryDataService.get(beneficiaryId) } returns beneficiary
            coEvery { memberService.get(member.id) } returns member
            coEvery { memberService.cancelById(member.id) } returns canceledMember
            coEvery { beneficiaryDataService.update(any()) } returns canceledBeneficiary
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryUpdatedEvent(canceledBeneficiary.toTransport())
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCanceledEvent(canceledBeneficiary.toTransport())
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = canceledBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    canceledBeneficiary.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.cancelActivationById(beneficiaryId)
            assertThat(result).isSuccessWithData(canceledBeneficiary.toTransport())

            coVerifyOnce { beneficiaryDataService.find(any()) }
            coVerifyOnce { beneficiaryDataService.get(beneficiaryId) }
            coVerifyOnce { memberService.get(member.id) }
            coVerifyOnce { memberService.cancelById(member.id) }
            coVerifyOnce { beneficiaryDataService.update(canceledBeneficiary) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#cancelActivation should return InvalidActivationCancellationStateException when Member status is not PENDING`() =
            runBlocking {
                val member =
                    TestModelFactory.buildMember(status = MemberStatus.values().toList().minus(PENDING).random())

                val beneficiaryId = RangeUUID.generate()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    id = beneficiaryId,
                    memberId = member.id,
                    activatedAt = LocalDateTime.now().plusDays(1)
                ).toModel()

                coEvery {
                    beneficiaryDataService.find(
                        queryEq {
                            where {
                                scope(this.id.eq(beneficiaryId)) and
                                        this.archived.eq(false)
                            }
                        }
                    )
                } returns listOf(beneficiary)
                coEvery { memberService.get(member.id) } returns member

                val result = beneficiaryService.cancelActivationById(beneficiaryId)
                assertThat(result).isFailureOfType(InvalidActivationCancellationStateException::class)

                coVerifyOnce { beneficiaryDataService.find(any()) }
                coVerifyOnce { memberService.get(member.id) }
                coVerifyNone { memberService.cancelById(any()) }
                coVerifyNone { beneficiaryDataService.update(any()) }
            }
    }

    @Test
    fun `#canPersonHaveThisProduct should return BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany B2B`() =
        runBlocking {
            val personId = PersonId()
            val member = TestModelFactory.buildMember()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val product = TestModelFactory.buildProduct(type = B2B)

            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(RangeUUID.generate())

            val result = beneficiaryService.canPersonHaveThisProduct(personId, product)
            assertThat(result).isFailureOfType(BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany::class)

            coVerifyOnce { memberService.getCurrent(personId) }
            coVerifyOnce { beneficiaryDataService.findOne(any()) }
            coVerifyOnce { companyProductPriceListingService.findCpplProductIdsForSubContract(any()) }
        }

    @Test
    fun `#canPersonHaveThisProduct should return true if have product in available list B2B`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val product = TestModelFactory.buildProduct(type = B2B)

        coEvery { memberService.getCurrent(personId) } returns member
        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery {
            companyProductPriceListingService.findCpplProductIdsForSubContract(
                beneficiary.companySubContractId!!
            )
        } returns listOf(product.id)

        val result = beneficiaryService.canPersonHaveThisProduct(personId, product)
        assertThat(result).isSuccessWithData(product)

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { beneficiaryDataService.findOne(any()) }
        coVerifyOnce { companyProductPriceListingService.findCpplProductIdsForSubContract(any()) }
    }

    @Test
    fun `#canPersonHaveThisProduct should return BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany ADESAO`() =
        runBlocking {
            val personId = PersonId()
            val member = TestModelFactory.buildMember()
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val product = TestModelFactory.buildProduct(type = ADESAO)

            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
            coEvery {
                companyProductPriceListingService.findCpplProductIdsForSubContract(
                    beneficiary.companySubContractId!!
                )
            } returns listOf(RangeUUID.generate())

            val result = beneficiaryService.canPersonHaveThisProduct(personId, product)
            assertThat(result).isFailureOfType(BeneficiaryCannotHaveThisProductBecauseItsNotAvailableOnCompany::class)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { beneficiaryDataService.findOne(any()) }
            coVerifyOnce { companyProductPriceListingService.findCpplProductIdsForSubContract(any()) }
        }

    @Test
    fun `#canPersonHaveThisProduct should return true if have product in available list ADESAO`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val product = TestModelFactory.buildProduct(type = ADESAO)

        coEvery { memberService.getCurrent(personId) } returns member
        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery {
            companyProductPriceListingService.findCpplProductIdsForSubContract(
                beneficiary.companySubContractId!!
            )
        } returns listOf(product.id)

        val result = beneficiaryService.canPersonHaveThisProduct(personId, product)
        assertThat(result).isSuccessWithData(product)

        coVerifyOnce { memberService.getCurrent(personId) }
        coVerifyOnce { beneficiaryDataService.findOne(any()) }
        coVerifyOnce { companyProductPriceListingService.findCpplProductIdsForSubContract(any()) }
    }

    @Nested
    inner class CreateBeneficiary {
        val personId = PersonId()
        private val beneficiaryTransport =
            TestBeneficiaryTransportFactory.buildBeneficiaryTransport(companySubContractId = RangeUUID.generate())
        val person = TestModelFactory.buildPerson(personId, nationalId = beneficiaryTransport.nationalId)
        private val personOnboarding = TestModelFactory.buildPersonOnboarding(personId)
        val member = TestModelFactory.buildMember(personId = person.id, status = ACTIVE, productType = B2C)
        private val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING, productType = B2B)
        private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        private val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val initialProductId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        val company = TestModelFactory.buildCompany(
            id = beneficiaryTransport.companyId,
            availableProducts = listOf(initialProductId),
        )
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val contract = TestModelFactory.buildCompanyContract()
        val subcontract = TestModelFactory.buildCompanySubContract(
            id = beneficiaryTransport.companySubContractId!!,
            contractId = contract.id,
        )
        private val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)
        private val expectedBeneficiary = beneficiaryTransport.toBeneficiary(member.id, personId).toModel()

        @Test
        fun `#should create beneficiary as expected`() = runBlocking {
            val beneficiaryTransport = beneficiaryTransport.copy(hiredAt = LocalDateTime.now())
            val member = member.copy(status = PENDING)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { memberService.create(any(), any(), any()) } returns member
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(beneficiaryTransport.companySubContractId!!) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    expectedBeneficiary.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                null
            )

            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(any()) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match {
                        it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() &&
                                it.selectedProduct.id == product.id
                    },
                    any(),
                )
            }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#should create beneficiary as expected with TOTAL_EXEMPTION grace type and MANDATORY grace type reason`() =
            runBlocking {
                val beneficiaryTransport = beneficiaryTransport.copy(hiredAt = LocalDateTime.now())
                val member = member.copy(status = PENDING)
                val contract = TestModelFactory.buildCompanyContract().copy(companySize = CompanySize.MLA)
                val company = company.copy(contractIds = listOf(contract.id))
                val flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery { memberService.create(any(), any(), any()) } returns member
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
                coEvery { personService.create(any(), true) } returns person
                coEvery { gasProcessService.start(any()) } returns personOnboarding
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )
                coEvery { contractService.get(any()) } returns contract
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match {
                            it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() &&
                                    it.selectedProduct.id == product.id
                        },
                        any()
                    )
                }
                coVerifyOnce {
                    subcontractService.findByCompanyId(company.id)
                }
                coVerifyOnce {
                    beneficiaryDataService.add(match {
                        it.memberId == member.id
                                && it.gracePeriodType == GracePeriodType.TOTAL_EXEMPTION
                                && it.gracePeriodTypeReason == GracePeriodTypeReason.MLA
                    })
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#should create beneficiary as expected with FLOW inferred by logic`() =
            runBlocking {
                val beneficiaryTransport = beneficiaryTransport.copy(hiredAt = LocalDateTime.now())
                val member = member.copy(status = PENDING)
                val contract = TestModelFactory.buildCompanyContract().copy(companySize = CompanySize.MLA)
                val company =
                    company.copy(
                        contractIds = listOf(contract.id),
                        defaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                    )
                val flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery { memberService.create(any(), any(), any()) } returns member
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
                coEvery { personService.create(any(), true) } returns person
                coEvery { gasProcessService.start(any()) } returns personOnboarding
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    initialProductId
                )
                coEvery { contractService.get(any()) } returns contract
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result =
                    beneficiaryService.createBeneficiary(
                        beneficiaryTransport,
                        initialProductId,
                        BeneficiaryOnboardingFlowType.UNDEFINED,
                        null
                    )

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match {
                            it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() &&
                                    it.selectedProduct.id == product.id
                        }, any()
                    )
                }
                coVerifyOnce { subcontractService.findByCompanyId(company.id) }
                coVerifyOnce {
                    beneficiaryDataService.add(match {
                        it.memberId == member.id
                                && it.gracePeriodType == GracePeriodType.TOTAL_EXEMPTION
                                && it.gracePeriodTypeReason == GracePeriodTypeReason.MLA
                    })
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#when there is a already B2C activated membership`() = runBlocking {
            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = ACTIVE,
                productType = B2C
            )
            val newMember = TestModelFactory.buildMember(
                personId = person.id,
                status = PENDING,
                productType = B2B
            )
            val parentCompany =
                TestModelFactory.buildCompany(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val company = company.copy(parentId = parentCompany.id)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery {
                memberService.create(
                    any(),
                    any(),
                    any(),
                )
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { memberService.changeProduct(personId, product, false, any()) } returns newMember
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { companyService.getRootCompany(any()) } returns parentCompany
            coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    expectedBeneficiary.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(changeProduct = true),
            )

            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce { companyService.getRootCompany(company.id) }
            coVerifyOnce { companyService.isThisProductAvailableForCompany(company.id, product) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    any(),
                )
            }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
            coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newMember.id }) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#when there is a already B2C activated membership, but is not set to change it`() =
            runBlocking {
                val member = TestModelFactory.buildMember(personId = person.id, status = ACTIVE, productType = B2C)
                val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING, productType = B2B)

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery { personService.create(any(), true) } returns person
                coEvery { gasProcessService.start(any()) } returns personOnboarding
                coEvery {
                    memberService.create(
                        any(),
                        any(),
                        any(),
                    )
                } returns MembershipAlreadyActiveMismatchException(
                    personId
                )
                coEvery { memberService.getCurrent(personId) } returns member
                coEvery { memberService.changeProduct(personId, product, any(), any()) } returns newMember
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery { companyService.getRootCompany(any()) } returns company
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery {
                    billingAccountablePartyService.assign(
                        any(),
                        any()
                    )
                } returns personBillingAccountableParty
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isFailureOfType(MembershipAlreadyActiveMismatchException::class)

                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyNone { memberService.changeProduct(any(), any(), any(), any()) }
                coVerifyNone { beneficiaryDataService.add(any()) }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }

        @Test
        fun `#when there is a already B2C activated membership with same product`() = runBlocking {
            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = ACTIVE,
                product = product,
                productType = product.type
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery {
                memberService.create(any(), any(), any())
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(changeProduct = true),
            )
            assertThat(result).isFailureOfType(MembershipAlreadyActiveMismatchException::class)

            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE) }
            coVerifyNone { beneficiaryDataService.add(any()) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#when there is a already B2B activated membership with same product but different company`() =
            runBlocking {
                val product = TestModelFactory.buildProduct(id = initialProductId, type = B2B)
                val member = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    product = product,
                    productType = product.type
                )
                val beneficiary = TestModelFactory.buildBeneficiary(
                    personId = person.id, memberId = member.id
                ).toModel()
                val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery {
                    memberService.create(any(), any(), any())
                } returns MembershipAlreadyActiveMismatchException(personId)
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                } returns beneficiary
                coEvery { personService.create(any(), true) } returns person
                coEvery { gasProcessService.start(any()) } returns personOnboarding
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { memberService.changeProduct(personId, product, true, any()) } returns newMember
                coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = product.id,
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty

                val result = beneficiaryService.createBeneficiary(
                    beneficiaryTransport,
                    initialProductId,
                    flowType,
                    null,
                    BeneficiaryService.CreateOptions(changeProduct = true),
                )

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(any()) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match {
                            it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() &&
                                    it.selectedProduct.id == product.id
                        },
                        any(),
                    )
                }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#when there is a already B2B activated membership with same product but different subcontract`() =
            runBlocking {
                val product = TestModelFactory.buildProduct(id = initialProductId, type = B2B)
                val member = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    product = product,
                    productType = product.type
                )
                val beneficiary = TestModelFactory.buildBeneficiary(
                    personId = person.id,
                    memberId = member.id,
                    companyId = company.id
                ).toModel()
                val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery {
                    memberService.create(any(), any(), any())
                } returns MembershipAlreadyActiveMismatchException(personId)
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                } returns beneficiary
                coEvery { personService.create(any(), true) } returns person
                coEvery { gasProcessService.start(any()) } returns personOnboarding
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { memberService.changeProduct(personId, product, true, any()) } returns newMember
                coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = product.id,
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty

                val result = beneficiaryService.createBeneficiary(
                    beneficiaryTransport,
                    initialProductId,
                    flowType,
                    null,
                    BeneficiaryService.CreateOptions(changeProduct = true),
                )

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(any()) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match {
                            it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() &&
                                    it.selectedProduct.id == product.id
                        },
                        any(),
                    )
                }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#when there is a already B2B activated membership`() = runBlocking {
            val product = TestModelFactory.buildProduct(id = initialProductId, type = B2B)
            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = ACTIVE,
                product = product,
                productType = product.type
            )
            val beneficiary = TestModelFactory.buildBeneficiary(
                personId = person.id,
                memberId = member.id, companyId = company.id, companySubContractId = subcontract.id
            ).toModel()
            val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery {
                memberService.create(any(), any(), any())
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                beneficiaryDataService.findOne(queryEq {
                    where { this.memberId.eq(member.id) and this.archived.eq(false) }
                })
            } returns beneficiary
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(changeProduct = true),
            )

            assertThat(result).isFailureOfType(MembershipAlreadyActiveMismatchException::class)

            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce {
                beneficiaryDataService.findOne(queryEq {
                    where { this.memberId.eq(member.id) and this.archived.eq(false) }
                })
            }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE) }
            coVerifyNone { beneficiaryDataService.add(any()) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#when there is a already ADESAO activated membership`() = runBlocking {
            val product = TestModelFactory.buildProduct(id = initialProductId, type = ADESAO)
            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = ACTIVE,
                product = product,
                productType = product.type
            )
            val beneficiary = TestModelFactory.buildBeneficiary(
                personId = person.id,
                memberId = member.id,
                companySubContractId = subcontract.id,
                companyId = company.id
            ).toModel()
            val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery {
                memberService.create(any(), any(), any())
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                beneficiaryDataService.findOne(queryEq {
                    where { this.memberId.eq(member.id) and this.archived.eq(false) }
                })
            } returns beneficiary
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(changeProduct = true),
            ).thenError { print(it.message) }

            assertThat(result).isFailureOfType(MembershipAlreadyActiveMismatchException::class)

            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce {
                beneficiaryDataService.findOne(queryEq {
                    where { this.memberId.eq(member.id) and this.archived.eq(false) }
                })
            }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE) }
            coVerifyNone { beneficiaryDataService.add(any()) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#ignoring the membership validation`() = runBlocking {
            val newPerson = person.copy(email = "<EMAIL>")
            val personOnboarding = TestModelFactory.buildPersonOnboarding(newPerson.id)
            val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                companyId = company.id,
                email = newPerson.email,
                nationalId = person.nationalId,
            )
            val nationalId = newPerson.nationalId.onlyDigits().trim()

            coEvery { companyService.get(company.id) } returns company
            coEvery {
                personService.create(
                    match {
                        it.nationalId == beneficiaryTransport.nationalId.onlyDigits().trim()
                                && it.email == beneficiaryTransport.email
                    }, true
                )
            } returns DuplicatedItemException()
            coEvery { personService.findByNationalId(nationalId) } returns person
            coEvery {
                personService.update(
                    match {
                        it.nationalId == beneficiaryTransport.nationalId.onlyDigits()
                            .trim() && it.email == beneficiaryTransport.email
                    }
                )
            } returns newPerson
            coEvery { onboardingService.findByPerson(newPerson.id) } returns personOnboarding
            coEvery { productService.getProduct(initialProductId) } returns product
            coEvery { productService.getCurrentProductPriceListing(product.id) } returns currentProductPriceListing
            coEvery {
                memberService.create(
                    personId,
                    match {
                        it.personId.id == member.personId.id
                                && it.documentUrl == company.contractsUrls.last()
                                && it.selectedProduct.id == product.id
                    },
                    null,
                )
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
            coEvery { memberService.changeProduct(personId, product, false, any()) } returns newMember
            coEvery {
                beneficiaryDataService.add(
                    match {
                        it.memberId == newMember.id && it.personId == personId
                    }
                )
            } returns expectedBeneficiary
            coEvery { billingAccountablePartyService.get(company.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery {
                billingAccountablePartyService.assign(
                    personId,
                    billingAccountableParty
                )
            } returns personBillingAccountableParty
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    ),
                    expectedBeneficiary.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(
                    changeProduct = true,
                    ignoreMembershipValidation = true,
                ),
            )
            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce {
                personService.update(match {
                    it.nationalId == person.nationalId.onlyDigits().trim() && it.email == newPerson.email
                })
            }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    any(),
                )
            }
            coVerifyOnce { memberService.getCurrent(personId) }
            coVerifyOnce { companyService.isThisProductAvailableForCompany(company.id, product) }
            coVerifyOnce { memberService.changeProduct(personId, product, false, any()) }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
            coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newMember.id }) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
            coVerifyOnce { onboardingService.findByPerson(newPerson.id) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#when there is a already B2B product activated to membership but the beneficiary does not exist`() =
            runBlocking {
                val subcontractId = RangeUUID.generate()
                val product = product.copy(type = B2B)

                val member = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    product = product,
                    productType = product.type
                )
                val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)

                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companyId = company.id,
                    email = person.email,
                    nationalId = person.nationalId,
                    initialProductId = initialProductId,
                    companySubContractId = subcontractId,
                )

                val subcontract1 = TestModelFactory.buildCompanySubContract(
                    id = subcontractId,
                    companyId = company.id,
                    contractId = contract.id,
                )

                val subcontract2 = TestModelFactory.buildCompanySubContract(
                    companyId = company.id,
                    contractId = contract.id,
                )

                val expectedBeneficiary = beneficiaryTransport.toBeneficiary(member.id, personId, ACTIVE)
                    .copy(companySubContractId = subcontractId).toModel()

                coEvery { companyService.get(company.id) } returns company
                coEvery {
                    personService.create(
                        match {
                            it.nationalId == beneficiaryTransport.nationalId.onlyDigits().trim()
                                    && it.email == beneficiaryTransport.email
                        }, true
                    )
                } returns person
                coEvery { gasProcessService.start(person.id) } returns personOnboarding
                coEvery { productService.getProduct(initialProductId) } returns product
                coEvery { productService.getCurrentProductPriceListing(product.id) } returns currentProductPriceListing
                coEvery {
                    memberService.create(
                        personId,
                        match {
                            it.personId.id == member.personId.id
                                    && it.documentUrl == company.contractsUrls.last()
                                    && it.selectedProduct.id == product.id
                        },
                        null,
                    )
                } returns MembershipAlreadyActiveMismatchException(personId)
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                } returns NotFoundException("List of entities is empty")
                coEvery {
                    beneficiaryDataService.add(
                        match {
                            it.memberId == member.id && it.personId == personId
                        }
                    )
                } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(company.billingAccountablePartyId!!) } returns billingAccountableParty
                coEvery {
                    billingAccountablePartyService.assign(
                        personId,
                        billingAccountableParty
                    )
                } returns personBillingAccountableParty
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(beneficiaryTransport.companySubContractId!!) } returns listOf(
                    product.id
                )
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedForActiveMemberEvent(expectedBeneficiary.toTransport()),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract1, subcontract2)
                coEvery { contractService.get(subcontract.contractId) } returns contract

                val result = beneficiaryService.createBeneficiary(
                    beneficiaryTransport = beneficiaryTransport,
                    initialProductId = initialProductId,
                    flowType = flowType,
                    cassiMemberInfo = null,
                    createOptions = BeneficiaryService.CreateOptions(
                        changeProduct = true,
                        ignoreMembershipValidation = true
                    )
                )
                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { gasProcessService.start(person.id) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce {
                    memberService.create(
                        personId,
                        match {
                            it.personId.id == member.personId.id
                                    && it.documentUrl == company.contractsUrls.last()
                                    && it.selectedProduct.id == product.id
                        },
                        null,
                    )
                }
                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyOnce {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                }
                coVerifyOnce {
                    beneficiaryDataService.add(
                        match {
                            it.memberId == member.id && it.personId == personId
                        }
                    )
                }
                coVerifyNone { companyService.isThisProductAvailableForCompany(company.id, product) }
                coVerifyNone { memberService.changeProduct(personId, product, false, any()) }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce { subcontractService.findByCompanyId(company.id) }
                coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
                verifyNone { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
            }

        @Test
        fun `#when there is a already B2B activated membership with different product`() = runBlocking {
            val member = TestModelFactory.buildMember(personId = person.id, status = ACTIVE, productType = B2B)
            val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE, productType = B2B)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery {
                memberService.create(
                    any(),
                    any(),
                    any(),
                )
            } returns MembershipAlreadyActiveMismatchException(personId)
            coEvery { memberService.getCurrent(personId) } returns member
            coEvery { memberService.changeProduct(personId, product, true, any()) } returns newMember
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                    ),
                    expectedBeneficiary.memberId.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                initialProductId,
                flowType,
                null,
                BeneficiaryService.CreateOptions(changeProduct = true),
            )

            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce { companyService.isThisProductAvailableForCompany(company.id, product) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    any(),
                )
            }
            verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
            coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newMember.id }) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#when there is a already ADESAO product activated to membership but the beneficiary does not exist`() =
            runBlocking {
                val product = TestModelFactory.buildProduct(id = initialProductId, type = ADESAO)
                val member = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    product = product,
                    productType = product.type
                )
                val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = product.id)
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companyId = company.id,
                    email = person.email,
                    nationalId = person.nationalId,
                    initialProductId = initialProductId,
                )

                coEvery { companyService.get(company.id) } returns company
                coEvery {
                    personService.create(
                        match {
                            it.nationalId == beneficiaryTransport.nationalId.onlyDigits().trim()
                                    && it.email == beneficiaryTransport.email
                        }, true
                    )
                } returns person
                coEvery { gasProcessService.start(person.id) } returns personOnboarding
                coEvery { productService.getProduct(initialProductId) } returns product
                coEvery { productService.getCurrentProductPriceListing(product.id) } returns currentProductPriceListing
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )
                coEvery {
                    memberService.create(
                        personId,
                        match {
                            it.personId.id == member.personId.id
                                    && it.documentUrl == company.contractsUrls.last()
                                    && it.selectedProduct.id == product.id
                        },
                        null,
                    )
                } returns MembershipAlreadyActiveMismatchException(personId)
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                } returns NotFoundException("List of entities is empty")
                coEvery {
                    beneficiaryDataService.add(
                        match {
                            it.memberId == member.id && it.personId == personId
                        }
                    )
                } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(company.billingAccountablePartyId!!) } returns billingAccountableParty
                coEvery {
                    billingAccountablePartyService.assign(
                        personId,
                        billingAccountableParty
                    )
                } returns personBillingAccountableParty
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                            metadata = null
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result = beneficiaryService.createBeneficiary(
                    beneficiaryTransport = beneficiaryTransport,
                    initialProductId = initialProductId,
                    flowType = flowType,
                    cassiMemberInfo = null,
                    createOptions = BeneficiaryService.CreateOptions(
                        changeProduct = true,
                        ignoreMembershipValidation = true
                    )
                )

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { gasProcessService.start(person.id) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce {
                    memberService.create(
                        personId,
                        match {
                            it.personId.id == member.personId.id
                                    && it.documentUrl == company.contractsUrls.last()
                                    && it.selectedProduct.id == product.id
                        },
                        null,
                    )
                }
                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyOnce {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(member.id) and this.archived.eq(false) }
                    })
                }
                coVerifyOnce {
                    beneficiaryDataService.add(
                        match {
                            it.memberId == member.id && it.personId == personId
                        }
                    )
                }
                coVerifyNone { companyService.isThisProductAvailableForCompany(company.id, product) }
                coVerifyNone { memberService.changeProduct(personId, product, false, any()) }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
                verifyNone { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
            }

        @Test
        fun `#should create beneficiary with CassiMember`() = runBlocking {
            val cassiMember = TestModelFactory.buildCassiMember(
                accountNumber = "123456",
                startDate = LocalDateTime.of(2022, 3, 21, 0, 0, 0),
                expirationDate = LocalDateTime.of(2024, 3, 21, 0, 0, 0)
            )
            val cassiMemberInfo = CassiMemberInfo(
                accountNumber = cassiMember.accountNumber,
                startDate = "2022-03-21",
                expirationDate = "2022-03-21"
            )
            val subcontract = TestModelFactory.buildCompanySubContract(companyId = company.id)
            val expectedBeneficiary = beneficiaryTransport
                .toBeneficiary(member.id, person.id)
                .copy(companySubContractId = subcontract.id)
                .toModel()

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { memberService.create(any(), any(), any()) } returns member
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                        metadata = null
                    ),
                    member.id.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result =
                beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, cassiMemberInfo)

            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(person.id, billingAccountableParty) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    cassiMemberInfo,
                )
            }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#should create beneficiary ignoring hired at`() = runBlocking {
            val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(hiredAt = null)
            val subcontract = TestModelFactory.buildCompanySubContract(companyId = company.id)

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { memberService.create(any(), any(), any()) } returns member
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
            coEvery { subcontractService.findByCompanyId(beneficiaryTransport.companyId) } returns listOf(subcontract)
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                        metadata = null
                    ),
                    member.id.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result =
                beneficiaryService.createBeneficiary(
                    beneficiaryTransport,
                    initialProductId,
                    flowType,
                    cassiMemberInfo = null,
                    createOptions = BeneficiaryService.CreateOptions(ignoreHiredAtValidation = true)
                )

            assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { billingAccountablePartyService.assign(person.id, billingAccountableParty) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    any(),
                )
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#should throw error when product selected by beneficiary is not available for company`() = runBlocking {
            val company = TestModelFactory.buildCompany(id = beneficiaryTransport.companyId)

            coEvery { companyService.get(any()) } returns company

            coEvery { companyService.get(any()) } returns company
            coEvery { subcontractService.findByCompanyId(any()) } returns emptyList()

            val result = beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
            assertThat(result).isFailureOfType(CompanySubcontractRequiredException::class)

            coVerifyOnce { companyService.get(beneficiaryTransport.companyId) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#should throw error when product selected by beneficiary is not available for contract`() =
            runBlocking {
                val contract = contract.copy(availableProducts = listOf(RangeUUID.generate()))
                val company = TestModelFactory.buildCompany(
                    id = beneficiaryTransport.companyId,
                    contractIds = listOf(contract.id),
                    availableProducts = null
                )
                val subcontract = TestModelFactory.buildCompanySubContract(companyId = company.id)

                coEvery { companyService.get(any()) } returns company
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { subcontractService.findByCompanyId(any()) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    RangeUUID.generate()
                )

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
                assertThat(result).isFailureOfType(InvalidBeneficiaryInitialProductException::class)

                coVerifyOnce { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce { contractService.get(subcontract.contractId) }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }

        @Test
        fun `#should throw error when the contract or the company have no available product list`() =
            runBlocking {
                val contract = TestModelFactory.buildCompanyContract(availableProducts = null)
                val company = TestModelFactory.buildCompany(
                    id = beneficiaryTransport.companyId,
                    contractIds = listOf(contract.id),
                    availableProducts = null
                )
                val subcontract =
                    TestModelFactory.buildCompanySubContract(companyId = company.id, availableProducts = null)

                coEvery { companyService.get(any()) } returns company
                coEvery { contractService.get(any()) } returns contract
                coEvery { subcontractService.findByCompanyId(any()) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns emptyList()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isFailureOfType(EmptyAvailableProductListException::class)

                coVerifyOnce { companyService.get(beneficiaryTransport.companyId) }
            }

        @Test
        fun `#should throw error when the subcontract id does not match with the company's subcontracts`() =
            runBlocking {
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(

                )
                val contract = TestModelFactory.buildCompanyContract(availableProducts = null)
                val company = TestModelFactory.buildCompany(
                    id = beneficiaryTransport.companyId,
                    contractIds = listOf(contract.id),
                    availableProducts = listOf(initialProductId),
                )
                val subcontract1 = TestModelFactory.buildCompanySubContract(companyId = company.id)
                val subcontract2 = TestModelFactory.buildCompanySubContract(companyId = company.id)

                coEvery { companyService.get(any()) } returns company
                coEvery { contractService.get(any()) } returns contract
                coEvery { productService.getProduct(any()) } returns product
                coEvery { subcontractService.findByCompanyId(any()) } returns listOf(subcontract1, subcontract2)

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isFailureOfType(MismatchCompanySubcontractException::class)

                coVerifyOnce { companyService.get(beneficiaryTransport.companyId) }
            }

        @Test
        fun `#should throw error when the subcontract id was not informed at transport but the company has some subcontracts`() =
            runBlocking {
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companySubContractId = null
                )
                val contract = TestModelFactory.buildCompanyContract(availableProducts = null)
                val company = TestModelFactory.buildCompany(
                    id = beneficiaryTransport.companyId,
                    contractIds = listOf(contract.id),
                    availableProducts = listOf(initialProductId)
                )
                val subcontract1 = TestModelFactory.buildCompanySubContract(companyId = company.id)
                val subcontract2 = TestModelFactory.buildCompanySubContract(companyId = company.id)

                coEvery { companyService.get(any()) } returns company
                coEvery { contractService.get(any()) } returns contract
                coEvery { productService.getProduct(any()) } returns product
                coEvery { subcontractService.findByCompanyId(any()) } returns listOf(subcontract1, subcontract2)

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isFailureOfType(BeneficiaryMustHaveSubContractIdException::class)

                coVerifyOnce { companyService.get(beneficiaryTransport.companyId) }
            }

        @Test
        fun `#createBeneficiary should throw error when beneficiary type to employee is wrong`() = runBlocking {
            val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD
            )
            val company = TestModelFactory.buildCompany(
                id = beneficiaryTransport.companyId,
                availableProducts = listOf(initialProductId),
            )

            coEvery { companyService.get(beneficiaryTransport.companyId) } returns company

            val result = beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
            assertThat(result).isFailureOfType(InvalidEmployeeFieldException::class)

            coVerifyNone { companyService.get(beneficiaryTransport.companyId) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#createBeneficiary should throw error when beneficiary type to employee doesn't pass the hiredAt time`() =
            runBlocking {
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    hiredAt = null
                )
                val company = TestModelFactory.buildCompany(
                    id = beneficiaryTransport.companyId,
                    availableProducts = listOf(initialProductId),
                )

                coEvery { companyService.get(beneficiaryTransport.companyId) } returns company

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
                assertThat(result).isFailureOfType(InvalidHiredAtFieldException::class)

                coVerifyNone { companyService.get(beneficiaryTransport.companyId) }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }

        @Test
        fun `#createBeneficiary should throw error when parentBeneficiary is null to dependent`() = runBlocking {
            val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                type = BeneficiaryType.DEPENDENT,
                hiredAt = null,
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            )
            val company = TestModelFactory.buildCompany(
                id = beneficiaryTransport.companyId,
                availableProducts = listOf(initialProductId),
            )

            coEvery { companyService.get(any()) } returns company

            val result = beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
            assertThat(result).isFailureOfType(InvalidDependentFieldException::class)

            coVerifyNone { companyService.get(beneficiaryTransport.companyId) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#createBeneficiary should throw error if has an error in billingAccountableParty part`() = runBlocking {
            val cassiMember = TestModelFactory.buildCassiMember(
                accountNumber = "123456",
                startDate = LocalDateTime.of(2022, 3, 21, 0, 0, 0),
                expirationDate = LocalDateTime.of(2024, 3, 21, 0, 0, 0)
            )
            val cassiMemberInfo = CassiMemberInfo(
                accountNumber = cassiMember.accountNumber,
                startDate = "2022-03-21",
                expirationDate = "2022-03-21"
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(any()) } returns product
            coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
            coEvery { personService.create(any(), true) } returns person
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { memberService.create(any(), any(), any()) } returns member
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { billingAccountablePartyService.get(any()) } returns NotFoundException()
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                product.id
            )
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryCreatedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        initialProductId = initialProductId,
                        flowType = flowType,
                        phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
                        metadata = null
                    ),
                    member.id.toString()
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = expectedBeneficiary.toTransport(),
                        eventAction = NotificationEventAction.CREATED
                    ),
                    expectedBeneficiary.id.toString()
                )
            } returns mockk()

            val result =
                beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, cassiMemberInfo)
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
            coVerifyOnce { memberService.create(any(), any(), any()) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce {
                memberService.create(
                    match { it.id == member.personId.id },
                    match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                    cassiMemberInfo,
                )
            }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#createBeneficiary should update person and create beneficiary when it has same national id and member is cancelled`() =
            runBlocking {
                val newPerson = person.copy(email = "<EMAIL>")
                val memberCanceled = member.copy(status = CANCELED)

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery { memberService.create(any(), any(), any()) } returns member
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
                coEvery { personService.create(any(), true) } returns DuplicatedItemException()
                coEvery { personService.findByNationalId(any()) } returns person
                coEvery { memberService.getCurrent(any()) } returns memberCanceled
                coEvery { personService.update(any()) } returns newPerson
                coEvery { onboardingService.findByPerson(newPerson.id) } returns personOnboarding
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(expectedBeneficiary.companySubContractId!!) } returns listOf(
                    initialProductId
                )
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.REGISTRATION
                        ),
                        expectedBeneficiary.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                        any(),
                    )
                }
                coVerifyNone { gasProcessService.start(personId) }
                coVerifyOnce { onboardingService.findByPerson(newPerson.id) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
            }

        @Test
        fun `#createBeneficiary should update person and create beneficiary when it has when person already created`() =
            runBlocking {
                val newPerson = person.copy(email = "<EMAIL>")
                val memberCanceled = member.copy(status = CANCELED)

                coEvery { companyService.get(any()) } returns company
                coEvery { productService.getProduct(any()) } returns product
                coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing
                coEvery { memberService.create(any(), any(), any()) } returns member
                coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
                coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(expectedBeneficiary.companySubContractId!!) } returns listOf(
                    initialProductId
                )
                coEvery { personService.create(any(), true) } returns DuplicatedItemException("")
                coEvery { personService.findByNationalId(any()) } returns person
                coEvery { memberService.getCurrent(any()) } returns memberCanceled
                coEvery { personService.update(any()) } returns newPerson
                coEvery { onboardingService.findByPerson(newPerson.id) } returns personOnboarding
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.REGISTRATION
                        ),
                        member.id.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce { memberService.create(any(), any(), any()) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                        any(),
                    )
                }
                coVerifyNone { gasProcessService.start(personId) }
                coVerifyOnce { onboardingService.findByPerson(newPerson.id) }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#createBeneficiary - should return exception when member exists and it's active`() = runBlocking {
            val newPerson = person.copy(email = "<EMAIL>")
            val memberActive = member.copy(status = ACTIVE)
            val personName = person.fullRegisterName

            coEvery { companyService.get(any()) } returns company
            coEvery { productService.getProduct(initialProductId) } returns product.success()
            coEvery { beneficiaryDataService.add(any()) } returns expectedBeneficiary
            coEvery { personService.create(any(), true) } returns DuplicatedItemException().failure()
            coEvery { personService.findByNationalId(any()) } returns person
            coEvery { memberService.getCurrent(any()) } returns memberActive
            coEvery { personService.update(any()) } returns newPerson
            coEvery { gasProcessService.start(any()) } returns personOnboarding
            coEvery { contractService.get(subcontract.contractId) } returns contract
            coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
            coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(beneficiaryTransport.companySubContractId!!) } returns listOf(
                product.id
            )

            val result = beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
            assertThat(result).isFailureOfType(AlreadyAliceMemberException::class)

            val exception = result.failure()
            assert(
                exception.message == """Não foi possível incluir $personName, pois já existe um plano ativo para esse CPF. 
                    |Em breve, nossa equipe de suporte entrará em contato para te ajudar com esse caso.""".trimMargin()
            )

            coVerify { companyService.get(beneficiaryTransport.companyId) }
            coVerifyOnce {
                personService.create(
                    match { it.nationalId == person.nationalId.onlyDigits().trim() },
                    true
                )
            }
            coVerifyNone { companyService.getRootCompany(any()) }
            coVerifyNone { productService.getCurrentProductPriceListing(any()) }
            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
            coVerifyNone { memberService.create(any(), any()) }
            coVerifyOnce {
                contractService.get(subcontract.contractId)
                subcontractService.findByCompanyId(company.id)
            }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#createBeneficiary - should return exception when member exists and it's a dependent using the holder national id`() =
            runBlocking {
                val beneficiaryParent = TestModelFactory.buildBeneficiary().toModel()
                val email = "<EMAIL>"
                val personParent = TestModelFactory.buildPerson(personId, email = email)
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    parentBeneficiary = beneficiaryParent.id,
                    type = BeneficiaryType.DEPENDENT,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                    email = email,
                    nationalId = personParent.nationalId,
                    hiredAt = null
                )

                coEvery {
                    beneficiaryDataService.get(beneficiaryParent.id)
                } returns beneficiaryParent
                coEvery { personService.get(beneficiaryParent.personId) } returns personParent.success()

                coEvery { productService.getProduct(initialProductId) } returns product.success()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)

                assertThat(result).isFailureOfType(NationalIdAlreadyUsedByHolderException::class)

                coVerifyNone { beneficiaryDataService.add(any()) }
                coVerifyNone { personService.create(any()) }
                coVerifyNone { personService.update(any()) }
                coVerifyNone { gasProcessService.start(any()) }
                coVerifyNone { companyService.getRootCompany(any()) }
                coVerifyNone { productService.getCurrentProductPriceListing(any()) }
                coVerifyNone { billingAccountablePartyService.get(any()) }
                coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
                coVerifyNone { memberService.create(any(), any()) }
                coVerifyNone {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(any())
                }
            }

        @Test
        fun `#createBeneficiary - should return exception when holder is dependent`() =
            runBlocking {
                val person = TestModelFactory.buildPerson(personId, email = "<EMAIL>")
                val parentPerson = TestModelFactory.buildPerson(nationalId = "559.108.160-63")

                val beneficiaryParent = TestModelFactory
                    .buildBeneficiary(parentBeneficiary = RangeUUID.generate()).toModel()
                val newPerson = person.copy()
                val company = TestModelFactory.buildCompany(
                    availableProducts = listOf(initialProductId)
                )

                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companyId = company.id,
                    email = newPerson.email,
                    nationalId = person.nationalId,
                    type = BeneficiaryType.DEPENDENT,
                    parentBeneficiary = beneficiaryParent.id,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                    hiredAt = null,
                )

                coEvery {
                    beneficiaryDataService.get(beneficiaryParent.id)
                } returns beneficiaryParent
                coEvery { personService.get(beneficiaryParent.personId) } returns parentPerson.success()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
                assertThat(result).isFailureOfType(HolderIsDependentException::class)

                coVerifyNone { beneficiaryDataService.add(any()) }
                coVerifyNone { personService.create(any()) }
                coVerifyNone { personService.update(any()) }
                coVerifyNone { gasProcessService.start(any()) }
                coVerifyNone { companyService.getRootCompany(any()) }
                coVerifyNone { productService.getCurrentProductPriceListing(any()) }
                coVerifyNone { billingAccountablePartyService.get(any()) }
                coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
                coVerifyNone { memberService.create(any(), any()) }
                coVerifyNone {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }

        @Test
        fun `#should return exception when dependent subcontract is different from holder subcontract`() =
            runBlocking {
                val parentPerson = TestModelFactory.buildPerson(nationalId = "559.108.160-63")

                val beneficiaryParent = TestModelFactory.buildBeneficiary().toModel()
                val company = TestModelFactory.buildCompany(
                    availableProducts = listOf(initialProductId),
                )

                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companyId = company.id,
                    type = BeneficiaryType.DEPENDENT,
                    parentBeneficiary = beneficiaryParent.id,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                    hiredAt = null,
                )

                coEvery {
                    beneficiaryDataService.get(beneficiaryParent.id)
                } returns beneficiaryParent
                coEvery { personService.get(beneficiaryParent.personId) } returns parentPerson.success()

                val result =
                    beneficiaryService.createBeneficiary(beneficiaryTransport, initialProductId, flowType, null)
                assertThat(result).isFailureOfType(SubcontractIsDifferentFromHolderException::class)

                coVerifyNone { beneficiaryDataService.add(any()) }
                coVerifyNone { personService.create(any()) }
                coVerifyNone { personService.update(any()) }
                coVerifyNone { gasProcessService.start(any()) }
                coVerifyNone { companyService.getRootCompany(any()) }
                coVerifyNone { productService.getCurrentProductPriceListing(any()) }
                coVerifyNone { billingAccountablePartyService.get(any()) }
                coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
                coVerifyNone { memberService.create(any(), any()) }
                coVerifyNone { kafkaProducerService.produce(any()) }
                coVerifyNone {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
            }

        @Test
        fun `#createBeneficiary - should create a dependent beneficiary as expected`() =
            runBlocking {
                val person = TestModelFactory.buildPerson(personId, email = "<EMAIL>")
                val parentPerson = TestModelFactory.buildPerson(nationalId = "559.108.160-63")

                val beneficiaryParent = TestModelFactory.buildBeneficiary().toModel()
                val newPerson = person.copy()
                val personOnboarding = TestModelFactory.buildPersonOnboarding(newPerson.id)
                val member = TestModelFactory.buildMember(personId = person.id, status = ACTIVE, productType = B2C)
                val memberCanceled = member.copy(status = CANCELED)
                val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING, productType = B2B)
                val company = TestModelFactory.buildCompany(
                    availableProducts = listOf(initialProductId),
                )
                val beneficiaryTransport = TestBeneficiaryTransportFactory.buildBeneficiaryTransport(
                    companyId = company.id,
                    email = newPerson.email,
                    nationalId = person.nationalId,
                    type = BeneficiaryType.DEPENDENT,
                    parentBeneficiary = beneficiaryParent.id,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                    hiredAt = null,
                    companySubContractId = beneficiaryParent.companySubContractId
                )

                val nationalId = newPerson.nationalId.onlyDigits().trim()

                val expectedBeneficiary = beneficiaryTransport.toBeneficiary(member.id, personId)
                    .copy(parentPerson = beneficiaryParent.personId).toModel()

                coEvery { personService.get(beneficiaryParent.personId) } returns parentPerson.success()

                coEvery { companyService.get(company.id) } returns company
                coEvery {
                    personService.create(
                        match {
                            it.nationalId == beneficiaryTransport.nationalId.onlyDigits().trim()
                                    && it.email == beneficiaryTransport.email
                        }, true
                    )
                } returns DuplicatedItemException()
                coEvery { personService.findByNationalId(nationalId) } returns person
                coEvery {
                    memberService.getCurrent(personId)
                } returnsMany listOf(memberCanceled.success(), member.success())
                coEvery {
                    personService.update(
                        match {
                            it.nationalId == beneficiaryTransport.nationalId.onlyDigits()
                                .trim() && it.email == beneficiaryTransport.email
                        }
                    )
                } returns newPerson
                coEvery { onboardingService.findByPerson(newPerson.id) } returns NotFoundException()
                coEvery { gasProcessService.start(newPerson.id) } returns personOnboarding
                coEvery { productService.getProduct(initialProductId) } returns product
                coEvery { productService.getCurrentProductPriceListing(product.id) } returns currentProductPriceListing
                coEvery {
                    memberService.create(
                        personId,
                        match {
                            it.personId.id == member.personId.id
                                    && it.documentUrl == company.contractsUrls.last()
                                    && it.selectedProduct.id == product.id
                        },
                        null,
                    )
                } returns MembershipAlreadyActiveMismatchException(personId)
                coEvery { companyService.isThisProductAvailableForCompany(company.id, product) } returns product
                coEvery { memberService.changeProduct(personId, product, false, any()) } returns newMember
                coEvery { beneficiaryDataService.get(beneficiaryParent.id) } returns beneficiaryParent
                coEvery {
                    beneficiaryDataService.add(
                        match {
                            it.memberId == newMember.id && it.personId == personId
                        }
                    )
                } returns expectedBeneficiary
                coEvery { billingAccountablePartyService.get(company.billingAccountablePartyId!!) } returns billingAccountableParty
                coEvery {
                    billingAccountablePartyService.assign(
                        personId,
                        billingAccountableParty
                    )
                } returns personBillingAccountableParty
                coEvery { contractService.get(subcontract.contractId) } returns contract
                coEvery { subcontractService.findByCompanyId(company.id) } returns listOf(subcontract)
                coEvery { companyProductPriceListingService.findCpplProductIdsForSubContract(subcontract.id) } returns listOf(
                    product.id
                )
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            initialProductId = initialProductId,
                            flowType = flowType,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                        ),
                        member.id.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = expectedBeneficiary.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        expectedBeneficiary.id.toString()
                    )
                } returns mockk()

                val result = beneficiaryService.createBeneficiary(
                    beneficiaryTransport,
                    initialProductId,
                    flowType,
                    null,
                    BeneficiaryService.CreateOptions(
                        changeProduct = true,
                    ),
                )

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())


                coVerify(exactly = 2) { companyService.get(beneficiaryTransport.companyId) }
                coVerifyOnce {
                    personService.create(
                        match { it.nationalId == person.nationalId.onlyDigits().trim() },
                        true
                    )
                }
                coVerifyOnce { personService.findByNationalId(nationalId) }
                coVerifyOnce {
                    personService.update(match {
                        it.nationalId == person.nationalId.onlyDigits().trim() && it.email == newPerson.email
                    })
                }
                coVerifyOnce { onboardingService.findByPerson(newPerson.id) }
                coVerifyOnce { gasProcessService.start(newPerson.id) }
                coVerifyOnce { productService.getProduct(initialProductId) }
                coVerifyOnce { productService.getCurrentProductPriceListing(initialProductId) }
                coVerifyOnce {
                    memberService.create(
                        match { it.id == member.personId.id },
                        match { it.personId.id == member.personId.id && it.documentUrl == company.contractsUrls.last() && it.selectedProduct.id == product.id },
                        any(),
                    )
                }
                coVerify(exactly = 2) { memberService.getCurrent(personId) }
                coVerifyOnce { companyService.isThisProductAvailableForCompany(company.id, product) }
                coVerifyOnce { memberService.changeProduct(personId, product, false, any()) }
                verifyOnce { BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS) }
                coVerifyOnce { beneficiaryDataService.get(beneficiaryParent.id) }
                coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newMember.id && it.parentPerson == parentPerson.id }) }
                coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
                coVerifyOnce { billingAccountablePartyService.assign(personId, billingAccountableParty) }
                coVerifyOnce {
                    contractService.get(subcontract.contractId)
                    subcontractService.findByCompanyId(company.id)
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }
    }

    @Test
    fun `#countUniquePerCompany should return correctly if empty`() = runBlocking {
        val companyId = RangeUUID.generate()

        coEvery {
            beneficiaryDataService.count(
                queryEq { where { this.companyId.eq(companyId) and this.memberStatus.eq(ACTIVE) } }
            )
        } returns 0.success()

        val result = beneficiaryService.countUniquePerPersonByCompanyId(companyId)

        assertThat(result).isSuccessWithData(0)
    }

    @Test
    fun `#countUniquePerCompany should return correctly `() = runBlocking {
        val companyId = RangeUUID.generate()
        val count = 1
        coEvery {
            beneficiaryDataService.count(
                queryEq { where { this.companyId.eq(companyId) and this.memberStatus.eq(ACTIVE) } }
            )
        } returns count.success()

        val result = beneficiaryService.countUniquePerPersonByCompanyId(companyId)

        assertThat(result).isSuccessWithData(1)
    }


    @Test
    fun `#attachOnboardingAndDependents - should attach the onboarding and dependents of a beneficiary`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary().toModel()
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel()
            val beneficiaryDependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id).toModel()

            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns
                    beneficiaryOnboarding.toTransport()
            coEvery { beneficiaryDataService.find(any()) } returns listOf(beneficiaryDependent)

            val expected = beneficiary
                .withDependents(listOf(beneficiaryDependent))
                .withOnboarding(beneficiaryOnboarding)

            val result = beneficiaryService.withOnboardingAndDependents(beneficiary.toTransport())

            assertThat(result).isSuccessWithData(expected.toTransport())

            coVerifyOnce {
                beneficiaryOnboardingService.findByBeneficiaryId(
                    beneficiary.id,
                    BeneficiaryOnboardingService.FindOptions(withPhases = true)
                )
            }
            coVerifyOnce {
                beneficiaryDataService.find(queryEq {
                    where { this.parentId.eq(beneficiary.id) and this.archived.eq(false) }
                })
            }
        }

    @Test
    fun `#triggerCassiAccountNumberForBeneficiaries should produce RequestCassiMemberUpdateEvent from CassiMembers whose accountNumber's are null`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "disable_created_at_checking_from_cassi_number_account_trigger",
                false
            ) {
                val offset = 0
                val limit = 100
                val cassiMembersAccountNumberEmpty = mutableListOf<CassiMember>().apply {
                    repeat(12) {
                        this.add(
                            element = TestModelFactory.buildCassiMember(
                                accountNumber = null,
                                memberId = RangeUUID.generate()
                            )
                        )
                    }
                }

                val persons = mutableListOf<Person>().apply {
                    repeat(12) { this.add(element = TestModelFactory.buildPerson()) }
                }

                val members = mutableListOf<Member>().apply {
                    for (i in 0..9) {
                        this.add(
                            element = TestModelFactory.buildMember(
                                id = cassiMembersAccountNumberEmpty[i].memberId,
                                personId = persons[i].id,
                            ).copy(createdAt = LocalDateTime.now().minusDays(4)),
                        )
                    }
                }

                members.add(
                    element = TestModelFactory.buildMember(
                        id = cassiMembersAccountNumberEmpty[10].memberId,
                        personId = persons[10].id,
                        status = CANCELED
                    ).copy(createdAt = LocalDateTime.now().plusDays(4))
                )

                members.add(
                    element = TestModelFactory.buildMember(
                        id = cassiMembersAccountNumberEmpty[11].memberId,
                        personId = persons[11].id,
                        status = ACTIVE
                    )
                )

                coEvery { memberService.findByIds(any()) } returns members
                coEvery {
                    cassiMemberService.findByAccountNumberEmpty(
                        offset = offset,
                        limit = limit
                    )
                } returns cassiMembersAccountNumberEmpty
                coEvery { personService.findByIds(any()) } returns persons
                coEvery { kafkaProducerService.produce(any<RequestCassiMemberUpdateEvent>()) } returns mockk()

                val result = beneficiaryService.triggerCassiAccountNumberForBeneficiaries(offset = offset, limit = limit)

                assertThat(result).isSuccess()

                coVerifyOnce { cassiMemberService.findByAccountNumberEmpty() }
                coVerify(exactly = 11) { kafkaProducerService.produce(any()) }
            }
        }

    @Test
    fun `#findByActivatedAtBetweenInclusive happy path withOnboarding`() = runBlocking {
        val activatedAtNow = LocalDate.now()
        val activatedAtStart = activatedAtNow.minusDays(10)

        val members = listOf(
            TestModelFactory.buildMember(),
            TestModelFactory.buildMember()
        )

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
        val onboardings = listOf(
            TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase)).toModel(),
            TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase)).toModel(),
        )
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(
                activatedAt = activatedAtNow.atStartOfDay(), memberId = members[0].id
            ).toModel(),
            TestModelFactory.buildBeneficiary(
                activatedAt = activatedAtStart.atStartOfDay(), memberId = members[1].id
            ).toModel()
        )
        val beneficiariesWithOnboarding = listOf(
            beneficiaries[0].withOnboarding(onboardings[0]),
            beneficiaries[1].withOnboarding(onboardings[1])
        )

        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaries[0].id) } returns
                onboardings[0].toTransport()
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaries[1].id) } returns
                onboardings[1].toTransport()
        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findPendingByActivatedAtBetweenInclusive(
            activatedAtStart, activatedAtNow, BeneficiaryService.FindOptions(withOnboarding = true)
        )

        assertThat(result).isSuccessWithData(beneficiariesWithOnboarding.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.activatedAt.greaterEq(activatedAtStart.atStartOfDay()) and
                            this.activatedAt.lessEq(activatedAtNow.atEndOfTheDay()) and
                            this.archived.eq(false) and
                            this.memberStatus.eq(PENDING)
                }.orderBy { this.parentBeneficiary }.sortOrder { desc }
            })
        }
    }

    @Test
    fun `#findPendingByActivatedAtBetweenInclusivePaginated happy path withOnboarding`() = runBlocking {
        val activatedAtNow = LocalDate.now()
        val activatedAtStart = activatedAtNow.minusDays(10)
        val offset = 0
        val limit = 100

        val members = listOf(
            TestModelFactory.buildMember(),
            TestModelFactory.buildMember()
        )

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
        val onboardings = listOf(
            TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase)).toModel(),
            TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase)).toModel(),
        )
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(
                activatedAt = activatedAtNow.atStartOfDay(), memberId = members[0].id
            ).toModel(),
            TestModelFactory.buildBeneficiary(
                activatedAt = activatedAtStart.atStartOfDay(), memberId = members[1].id
            ).toModel()
        )
        val beneficiariesWithOnboarding = listOf(
            beneficiaries[0].withOnboarding(onboardings[0]),
            beneficiaries[1].withOnboarding(onboardings[1])
        )

        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaries[0].id) } returns
                onboardings[0].toTransport()
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaries[1].id) } returns
                onboardings[1].toTransport()
        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findPendingByActivatedAtBetweenInclusivePaginated(
            activatedAtStart, activatedAtNow, BeneficiaryService.FindOptions(withOnboarding = true), offset, limit
        )

        assertThat(result).isSuccessWithData(beneficiariesWithOnboarding.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.activatedAt.greaterEq(activatedAtStart.atStartOfDay()) and
                            this.activatedAt.lessEq(activatedAtNow.atEndOfTheDay()) and
                            this.archived.eq(false) and
                            this.memberStatus.eq(PENDING)
                }
                    .offset { offset }
                    .limit { limit }
                    .orderBy { this.parentBeneficiary }.sortOrder { desc }
            })
        }
    }

    @Test
    fun `#triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate should produce RequestCassiMemberUpdateEvent from active CassiMembers with oder expiration date`() =

        runBlocking {
            val cassiMembersAccountExpired = mutableListOf<CassiMember>().apply {
                repeat(600) {
                    this.add(
                        element = TestModelFactory.buildCassiMember(
                            accountNumber = null,
                            memberId = RangeUUID.generate()
                        )
                    )
                }
            }

            val persons = mutableListOf<Person>().apply {
                repeat(12) { this.add(element = TestModelFactory.buildPerson()) }
            }

            val members = mutableListOf<Member>().apply {
                for (i in 0..9) {
                    this.add(
                        element = TestModelFactory.buildMember(
                            id = cassiMembersAccountExpired[i].memberId,
                            personId = persons[i].id,
                        ).copy(createdAt = LocalDateTime.now().minusDays(4)),
                    )
                }
            }

            members.add(
                element = TestModelFactory.buildMember(
                    id = cassiMembersAccountExpired[10].memberId,
                    personId = persons[10].id,
                    status = CANCELED
                ).copy(createdAt = LocalDateTime.now().plusDays(4))
            )

            members.add(
                element = TestModelFactory.buildMember(
                    id = cassiMembersAccountExpired[11].memberId,
                    personId = persons[11].id,
                    status = ACTIVE
                )
            )

            coEvery { memberService.findByIds(any()) } returns members
            coEvery {
                cassiMemberService.findByOlderExpirationDatePaginated(
                    any(),
                    0,
                    500
                )
            } returns cassiMembersAccountExpired.take(500)
            coEvery {
                cassiMemberService.findByOlderExpirationDatePaginated(
                    any(),
                    500,
                    500
                )
            } returns cassiMembersAccountExpired.take(100)
            coEvery { personService.findByIds(any()) } returns persons
            coEvery { kafkaProducerService.produce(any<RequestCassiMemberUpdateEvent>()) } returns mockk()

            val result = beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate()
            assertThat(result).isSuccess()

            coVerify(exactly = 2) { cassiMemberService.findByOlderExpirationDatePaginated(any(), any(), any()) }
            coVerify(exactly = 2) { personService.findByIds(any()) }
            coVerify(exactly = 20) { kafkaProducerService.produce(any()) }

        }

    @Test
    fun `#updateCassiMembershipByExpirationDate should update CassiMembers with older expiration date`() =

        runBlocking {
            val memberMap = mutableMapOf<String, Pair<Member, CassiMember>>().apply {
                repeat(8) {
                    val person = TestModelFactory.buildPerson()
                    val cassiMember = TestModelFactory.buildCassiMember(
                        accountNumber = null,
                        memberId = RangeUUID.generate()
                    )
                    this[person.id.toString()] = TestModelFactory.buildMember(
                        id = cassiMember.memberId,
                        personId = person.id,
                    ).copy(createdAt = LocalDateTime.now().minusDays(4)) to cassiMember
                }
            }

            val person1 = TestModelFactory.buildPerson()
            val cassiMember1 = TestModelFactory.buildCassiMember(
                accountNumber = null,
                memberId = RangeUUID.generate()
            )
            memberMap[person1.id.toString()] = TestModelFactory.buildMember(
                id = cassiMember1.memberId,
                personId = person1.id,
                status = CANCELED
            ).copy(createdAt = LocalDateTime.now().plusDays(4)) to cassiMember1

            val person2 = TestModelFactory.buildPerson()
            val cassiMember2 = TestModelFactory.buildCassiMember(
                accountNumber = null,
                memberId = RangeUUID.generate()
            )
            memberMap[person2.id.toString()] = TestModelFactory.buildMember(
                id = cassiMember2.memberId,
                personId = person2.id,
                status = ACTIVE
            ) to cassiMember2

            coEvery { memberService.findByIds(any()) } returns memberMap.values.map { it.first }
            coEvery {
                cassiMemberService.findByOlderExpirationDatePaginated(
                    any(),
                    10,
                    0
                )
            } returns memberMap.values.map { it.second }
            coEvery { personService.get(any()) } returns TestModelFactory.buildPerson()
            coEvery { cassiMemberService.updateCassiMember(any(), any()) } returns TestModelFactory.buildMember()

            val result = beneficiaryService.updateCassiMembershipByExpirationDate(10, 0)
            assertThat(result).isSuccess()

            coVerifyOnce { cassiMemberService.findByOlderExpirationDatePaginated(any(), 10, 0) }
            coVerify(exactly = 8) { personService.get(any()) }
            coVerify(exactly = 8) { cassiMemberService.updateCassiMember(any(), any()) }
        }

    @Test
    fun `#reactivateMembership should reactivate beneficiary member B2B`(): Unit = runBlocking {
        val member = TestModelFactory.buildMember(productType = B2B)
        val currentPhase = TestModelFactory.buildBeneficiaryOnboardingPhase()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            initialProductId = member.productId,
            phases = listOf(currentPhase)
        )
        val beneficiary = TestModelFactory.buildBeneficiary(
            onboarding = beneficiaryOnboarding, memberId = member.id
        ).toModel()
        val company =
            TestModelFactory.buildCompany(id = beneficiary.companyId, availableProducts = listOf(member.productId))

        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { companyService.get(any()) } returns company
        coEvery { memberService.get(any()) } returns member
        coEvery { memberService.reactivateMemberById(any()) } returns member
        coEvery { beneficiaryDataService.add(any()) } returns beneficiary
        coEvery {
            companyProductPriceListingService.findCpplProductIdsForSubContract(
                beneficiary.companySubContractId!!
            )
        } returns listOf(member.productId)
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryCreatedEvent(
                    beneficiary = beneficiary.toTransport(),
                    initialProductId = member.productId,
                    flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                    phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                ),
                member.id.toString()
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiary = beneficiary.toTransport(),
                    eventAction = NotificationEventAction.CREATED
                ),
                beneficiary.id.toString()
            )
        } returns mockk()

        val result = beneficiaryService.reactivateMembershipByMember(beneficiary.memberId)
        assertThat(result).isSuccess()

        coVerifyOnce { memberService.reactivateMemberById(beneficiary.memberId) }
        coVerifyOnce { beneficiaryDataService.add(match { it.memberId == beneficiary.memberId }) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#reactivateMembership should reactivate beneficiary member B2B - no duplicate beneficiary flow`(): Unit =
        runBlocking {
            val member = TestModelFactory.buildMember(productType = B2B)
            val currentPhase = TestModelFactory.buildBeneficiaryOnboardingPhase()
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                initialProductId = member.productId,
                phases = listOf(currentPhase)
            )
            val beneficiary = TestModelFactory.buildBeneficiary(
                onboarding = beneficiaryOnboarding, memberId = member.id
            ).toModel()
            val params = TestModelFactory.buildMemberLifeCycleEvents(
                type = MemberLifecycleEventType.REACTIVATION,
                reason = MemberLifecycleReasonEvents.COURT_ORDER,
                actionAt = LocalDate.now(),
                memberId = member.id
            )

            coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

            coEvery { memberService.reactivateMemberById(beneficiary.memberId, params) } returns member
            coEvery { beneficiaryDataService.update(any()) } returns beneficiary

            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryUpdatedEvent(
                        beneficiary = beneficiary.toTransport()
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = beneficiary.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    beneficiary.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.reactivateMembershipByMember(beneficiary.memberId, params)
            assertThat(result).isSuccess()

            coVerifyOnce {
                memberService.reactivateMemberById(beneficiary.memberId, params)
                beneficiaryDataService.update(any())
            }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#reactivateMembership should reactivate beneficiary member ADESAO`(): Unit = runBlocking {
        val member = TestModelFactory.buildMember(productType = ADESAO)
        val currentPhase = TestModelFactory.buildBeneficiaryOnboardingPhase()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            initialProductId = member.productId,
            phases = listOf(currentPhase),
        )
        val beneficiary = TestModelFactory.buildBeneficiary(
            onboarding = beneficiaryOnboarding, memberId = member.id
        ).toModel()
        val company =
            TestModelFactory.buildCompany(id = beneficiary.companyId, availableProducts = listOf(member.productId))

        coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { companyService.get(any()) } returns company
        coEvery { memberService.get(any()) } returns member
        coEvery { memberService.reactivateMemberById(any()) } returns member
        coEvery { beneficiaryDataService.add(any()) } returns beneficiary
        coEvery {
            companyProductPriceListingService.findCpplProductIdsForSubContract(
                beneficiary.companySubContractId!!
            )
        } returns listOf(member.productId)
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryCreatedEvent(
                    beneficiary = beneficiary.toTransport(),
                    initialProductId = member.productId,
                    flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                    phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                ),
                member.id.toString()
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiary = beneficiary.toTransport(),
                    eventAction = NotificationEventAction.CREATED
                ),
                beneficiary.id.toString()
            )
        } returns mockk()

        val result = beneficiaryService.reactivateMembershipByMember(beneficiary.memberId)
        assertThat(result).isSuccess()

        coVerifyOnce { memberService.reactivateMemberById(beneficiary.memberId) }
        coVerifyOnce { beneficiaryDataService.add(match { it.memberId == beneficiary.memberId }) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#updateInBatch should update chunked beneficiaries123`(): Unit = runBlocking {
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary().toModel(),
            TestModelFactory.buildBeneficiary().toModel(),
            TestModelFactory.buildBeneficiary().toModel()
        )
        coEvery {
            beneficiaryDataService.updateList(capture(beneficiaryListSlot))
        } returns beneficiaries
        beneficiaries.forEach {
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = it.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    it.id.toString()
                )
            } returns mockk()
        }

        val result = beneficiaryService.updateInBatch(beneficiaries.map { it.toTransport() })
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })
        Assertions
            .assertThat(beneficiaryListSlot.captured)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(beneficiaries)
        coVerify(exactly = beneficiaries.size) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#findByParentPerson should list dependents of parent`(): Unit = runBlocking {
        val parentPersonId = PersonId()
        val beneficiaries = listOf(TestModelFactory.buildBeneficiary().toModel())
        val memberStatuses = listOf(ACTIVE)
        coEvery { beneficiaryDataService.find(any()) } returns beneficiaries

        val result = beneficiaryService.findByParentPerson(parentPersonId, memberStatuses)
        assertThat(result).isSuccessWithData(beneficiaries.map { it.toTransport() })

        coVerifyOnce {
            beneficiaryDataService.find(queryEq {
                where {
                    this.parentPersonId.eq(parentPersonId.id) and this.memberStatus.inList(memberStatuses) and this.archived.eq(
                        false
                    )
                }
            })
        }
    }

    @Nested
    inner class UpdateDependentWithNewParent {
        @Test
        fun `#should cancel the beneficiary and membership and create a new when it is active`(): Unit =
            runBlocking {
                val parentMember = TestModelFactory.buildMember(status = ACTIVE, activationDate = LocalDateTime.now())
                val parent = TestModelFactory.buildBeneficiary(memberId = parentMember.id)
                val currentMember = TestModelFactory.buildMember(status = ACTIVE)
                val newMember = TestModelFactory.buildMember(status = ACTIVE)
                val currentDependent = TestModelFactory.buildBeneficiary(
                    memberId = currentMember.id,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                ).toModel()
                    .withOnboarding(
                        TestModelFactory.buildBeneficiaryOnboarding().toModel()
                            .withPhases(listOf(TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()))
                    )

                val newDependent = currentDependent.copy(
                    id = RangeUUID.generate(),
                    memberId = newMember.id,
                    parentBeneficiary = parent.id,
                    parentPerson = parent.personId,
                )

                coEvery { memberService.get(parentMember.id) } returns parentMember
                coEvery { memberService.get(currentMember.id) } returns currentMember
                coEvery {
                    memberService.cancelAndCreateNewMember(
                        currentMember.id,
                        parentMember.activationDate
                    )
                } returns newMember
                coEvery { beneficiaryDataService.add(match { it.memberId == newDependent.memberId && it.parentPerson == parent.personId && it.parentBeneficiary == parent.id }) } returns newDependent
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = newDependent.toTransport(),
                            initialProductId = newDependent.onboarding!!.initialProductId,
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                        ),
                        newDependent.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        NewBeneficiaryRelationshipAssignedEvent(
                            previousBeneficiary = currentDependent.toTransport(),
                            newBeneficiary = newDependent.toTransport(),
                        )
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = newDependent.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        newDependent.id.toString()
                    )
                } returns mockk()

                val result = beneficiaryService.updateDependentWithNewParent(currentDependent.toTransport(), parent)

                assertThat(result).isSuccessWithData(newDependent.toTransport())

                coVerifyOnce { memberService.get(parentMember.id) }
                coVerifyOnce { memberService.get(currentMember.id) }
                coVerifyOnce { memberService.cancelAndCreateNewMember(currentMember.id, parentMember.activationDate) }
                coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newDependent.memberId && it.parentPerson == parent.personId && it.parentBeneficiary == parent.id }) }
                coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#should cancel the current beneficiary and the current membership to create a new one when it is active but it does not use parent activation date once they are on same subcontract`(): Unit =
            runBlocking {
                val parentMember = TestModelFactory.buildMember(status = ACTIVE, activationDate = LocalDateTime.now())
                val parent = TestModelFactory.buildBeneficiary(memberId = parentMember.id)
                val currentMember = TestModelFactory.buildMember(status = ACTIVE)
                val newMember = TestModelFactory.buildMember(status = ACTIVE)
                val currentDependent = TestModelFactory.buildBeneficiary(
                    memberId = currentMember.id,
                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                    companySubContractId = parent.companySubContractId,
                ).toModel()
                    .withOnboarding(
                        TestModelFactory.buildBeneficiaryOnboarding().toModel()
                            .withPhases(listOf(TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()))
                    )

                val newDependent = currentDependent.copy(
                    id = RangeUUID.generate(),
                    memberId = newMember.id,
                    parentBeneficiary = parent.id,
                    parentPerson = parent.personId,
                )

                coEvery { memberService.get(currentMember.id) } returns currentMember
                coEvery {
                    memberService.cancelAndCreateNewMember(
                        currentMember.id,
                        null,
                    )
                } returns newMember
                coEvery { beneficiaryDataService.add(match { it.memberId == newDependent.memberId && it.parentPerson == parent.personId && it.parentBeneficiary == parent.id }) } returns newDependent
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryCreatedEvent(
                            beneficiary = newDependent.toTransport(),
                            initialProductId = newDependent.onboarding!!.initialProductId,
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
                        ),
                        newDependent.memberId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        NewBeneficiaryRelationshipAssignedEvent(
                            previousBeneficiary = currentDependent.toTransport(),
                            newBeneficiary = newDependent.toTransport(),
                        )
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiary = newDependent.toTransport(),
                            eventAction = NotificationEventAction.CREATED
                        ),
                        newDependent.id.toString()
                    )
                } returns mockk()

                val result = beneficiaryService.updateDependentWithNewParent(currentDependent.toTransport(), parent)

                assertThat(result).isSuccessWithData(newDependent.toTransport())

                coVerifyNone { memberService.get(parentMember.id) }
                coVerifyOnce { memberService.get(currentMember.id) }
                coVerifyOnce { memberService.cancelAndCreateNewMember(currentMember.id, null) }
                coVerifyOnce { beneficiaryDataService.add(match { it.memberId == newDependent.memberId && it.parentPerson == parent.personId && it.parentBeneficiary == parent.id }) }
                coVerify(exactly = 3) { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#should update the beneficiary with the new parent id when it is pending`(): Unit = runBlocking {
            val currentMember = TestModelFactory.buildMember(status = PENDING)
            val parent = TestModelFactory.buildBeneficiary()
            val currentDependent = TestModelFactory.buildBeneficiary(
                memberId = currentMember.id,
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                parentPerson = parent.personId,
            ).toModel()
                .withOnboarding(
                    TestModelFactory.buildBeneficiaryOnboarding().toModel()
                        .withPhases(listOf(TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()))
                )

            val currentDependentUpdated =
                currentDependent.copy(parentPerson = parent.personId, parentBeneficiary = parent.id)

            coEvery { memberService.get(currentMember.id) } returns currentMember
            coEvery { beneficiaryDataService.update(capture(beneficiarySlot)) } returns currentDependentUpdated
            coEvery {
                kafkaProducerService.produce(
                    BeneficiaryChangedEvent(
                        beneficiary = currentDependentUpdated.toTransport(),
                        eventAction = NotificationEventAction.UPDATED
                    ),
                    currentDependentUpdated.id.toString()
                )
            } returns mockk()

            val result = beneficiaryService.updateDependentWithNewParent(currentDependent.toTransport(), parent)

            assertThat(result).isSuccessWithData(currentDependentUpdated.toTransport())

            Assertions
                .assertThat(beneficiarySlot.captured)
                .usingRecursiveComparison()
                .ignoringFields("createdAt", "updatedAt")
                .isEqualTo(currentDependentUpdated)
            coVerifyOnce {
                memberService.get(currentMember.id)
                beneficiaryDataService.update(any())
            }
            coVerifyNone {
                memberService.cancelAndCreateNewMember(any())
                beneficiaryDataService.add(any())
            }
            coVerifyOnce { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#should do nothing when the beneficiary is canceled`(): Unit = runBlocking {
            val currentMember = TestModelFactory.buildMember(status = CANCELED)
            val parent = TestModelFactory.buildBeneficiary()
            val currentDependent = TestModelFactory.buildBeneficiary(
                memberId = currentMember.id,
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            ).toModel()
                .withOnboarding(
                    TestModelFactory.buildBeneficiaryOnboarding().toModel()
                        .withPhases(listOf(TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()))
                )

            coEvery { memberService.get(currentMember.id) } returns currentMember

            val result = beneficiaryService.updateDependentWithNewParent(currentDependent.toTransport(), parent)

            assertThat(result).isSuccessWithData(currentDependent.toTransport())

            coVerifyOnce { memberService.get(currentMember.id) }
            coVerifyNone {
                memberService.cancelAndCreateNewMember(any())
                beneficiaryDataService.update(any())
                beneficiaryDataService.add(any())
                kafkaProducerService.produce(any())
            }
        }

        @Test
        fun `#should not return the beneficiary `(): Unit = runBlocking {
            val member = TestModelFactory.buildMember()
            val parent = TestModelFactory.buildBeneficiary()
            val dependent = TestModelFactory.buildBeneficiary(memberId = member.id, parentBeneficiary = parent.id)
                .toModel()
                .withOnboarding(
                    TestModelFactory.buildBeneficiaryOnboarding().toModel()
                        .withPhases(listOf(TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()))
                )

            val result = beneficiaryService.updateDependentWithNewParent(dependent.toTransport(), parent)
            assertThat(result).isSuccessWithData(dependent.toTransport())

            coVerifyNone { memberService.cancelAndCreateNewMember(any()) }
            coVerifyNone { beneficiaryDataService.add(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }
    }

    @Nested
    inner class GetCompanyContractStarted {

        @Test
        fun `#should return the contract startedAt date`(): Unit = runBlocking {
            val startedAt = LocalDate.now().plusDays(10)
            val contract = TestModelFactory.buildCompanyContract().copy(startedAt = startedAt)
            val subcontract = TestModelFactory.buildCompanySubContract().copy(contractId = contract.id)
            val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id).toModel()

            coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
            coEvery { subcontractService.get(beneficiary.companySubContractId!!) } returns subcontract
            coEvery { contractService.get(contract.id) } returns contract

            val result = beneficiaryService.getCompanyContractStarted(beneficiary.memberId)

            assertThat(result).isSuccessWithData(startedAt)

            coVerifyOnce {
                beneficiaryDataService.findOne(queryEq {
                    where { this.memberId.eq(beneficiary.memberId) and this.archived.eq(false) }
                })

                subcontractService.get(beneficiary.companySubContractId!!)

                contractService.get(contract.id)
            }
        }

        @Test
        fun `#should throw an error when the beneficiary does not belong to some subcontract`(): Unit =
            runBlocking {
                val startedAt = LocalDate.now().plusDays(10)
                val company = TestModelFactory.buildCompany(
                    contractStartedAt = startedAt.atStartOfDay(),
                    contractIds = emptyList(),
                )
                val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = null).toModel()

                coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary
                coEvery { companyService.get(company.id) } returns company


                val result = beneficiaryService.getCompanyContractStarted(beneficiary.memberId)

                assertThat(result).isFailureOfType(IllegalArgumentException::class)

                coVerifyOnce {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(beneficiary.memberId) and this.archived.eq(false) }
                    })
                }

                coVerifyNone { contractService.get(any()) }
            }

        @Test
        fun `#should throw an error when the company and contract does not have some startedAt date defined`(): Unit =
            runBlocking {
                val contract = TestModelFactory.buildCompanyContract().copy(startedAt = null)
                val subcontract = TestModelFactory.buildCompanySubContract().copy(contractId = contract.id)
                val beneficiary =
                    TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id)

                coEvery { beneficiaryDataService.findOne(any()) } returns beneficiary.toModel()
                coEvery { subcontractService.get(beneficiary.companySubContractId!!) } returns subcontract
                coEvery { contractService.get(contract.id) } returns contract


                val result = beneficiaryService.getCompanyContractStarted(beneficiary.memberId)

                assertThat(result).isFailureOfType(IllegalArgumentException::class)

                coVerifyOnce {
                    beneficiaryDataService.findOne(queryEq {
                        where { this.memberId.eq(beneficiary.memberId) and this.archived.eq(false) }
                    })

                    subcontractService.get(beneficiary.companySubContractId!!)

                    contractService.get(any())
                }
            }
    }

    @Nested
    inner class ActivateBeneficiary {
        @Test
        fun `#should activate the beneficiary dependent if the parent is activated`() =
            runBlocking {
                val subcontract = TestModelFactory.buildCompanySubContract(paymentType = PaymentModel.POST_PAY)
                val parentMember = TestModelFactory.buildMember(status = ACTIVE)
                val parentBeneficiary = TestModelFactory.buildBeneficiary(
                    memberId = parentMember.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    personId = parentMember.personId,
                ).toModel()
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    parentBeneficiary = parentBeneficiary.id,
                    parentPerson = parentBeneficiary.personId
                ).toModel()
                val activatedMember = member.copy(status = ACTIVE)
                val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberService.activateMember(member) } returns activatedMember
                coEvery { memberService.getCurrent(parentMember.personId) } returns parentMember

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerifyOnce { memberService.activateMember(member) }
            }

        @Test
        fun `#should not activate the beneficiary dependent when the parent is not activated`() =
            runBlocking {
                val subcontract = TestModelFactory.buildCompanySubContract(paymentType = PaymentModel.POST_PAY)
                val parentMember = TestModelFactory.buildMember(status = PENDING)
                val parentBeneficiary = TestModelFactory.buildBeneficiary(
                    memberId = parentMember.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    personId = parentMember.personId,
                ).toModel()
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    parentBeneficiary = parentBeneficiary.id,
                    parentPerson = parentBeneficiary.personId
                ).toModel()
                val activatedMember = member.copy(status = ACTIVE)
                val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberService.activateMember(member) } returns activatedMember
                coEvery { memberService.getCurrent(parentMember.personId) } returns parentMember

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(BeneficiaryActivationValidationException::class)

                coVerifyNone { memberService.activateMember(member) }
            }


        @Test
        fun `#should activate the beneficiary if activatedAt date is before now and subcontract is POST_PAY`() =
            runBlocking {
                val subcontract = TestModelFactory.buildCompanySubContract(paymentType = PaymentModel.POST_PAY)
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1)
                ).toModel()
                val activatedMember = member.copy(status = ACTIVE)
                val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberService.activateMember(member) } returns activatedMember

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerifyOnce { memberService.activateMember(member) }
            }

        @Test
        fun `#should activate the beneficiary if activatedAt date is before now and subcontract is PRE_PAY and MIG FIRST_PAYMENT was PAID`() =
            runBlocking {
                val contract = TestModelFactory.buildCompanyContract()
                val subcontract = TestModelFactory.buildCompanySubContract(
                    paymentType = PaymentModel.PRE_PAY,
                    contractId = contract.id
                )

                val memberInvoiceGroup =
                    TestModelFactory.buildMemberInvoiceGroup(
                        companySubcontractId = subcontract.id,
                        status = MemberInvoiceGroupStatus.PAID
                    )
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1)
                )
                val activatedMember = member.copy(status = ACTIVE)
                val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary.toModel()
                coEvery { memberService.activateMember(member) } returns activatedMember
                coEvery { memberInvoiceGroupService.getBySubcontractId(subcontract.id, any()) } returns listOf(
                    memberInvoiceGroup
                )

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isSuccessWithData(expectedBeneficiary)

                coVerifyOnce { memberService.activateMember(member) }
            }

        @Test
        fun `#should activate the beneficiary if activatedAt date is before now and subcontract is PRE_PAY and PAP FIRST_PAYMENT was PAID`() =
            runBlocking {
                withFeatureFlags(
                    FeatureNamespace.MONEY_IN to mapOf(
                        "pre_activation_payment_for_group_company" to listOf("0001"),
                        "enable_pre_activation_payment" to true
                    )
                ) {
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001"
                    )

                    val subcontract = TestModelFactory.buildCompanySubContract(
                        paymentType = PaymentModel.PRE_PAY,
                        contractId = contract.id
                    )

                    val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                        companySubContractId = subcontract.id,
                        status = PreActivationPaymentStatus.PAID
                    )
                    val member = TestModelFactory.buildMember()
                    val beneficiary = TestModelFactory.buildBeneficiary(
                        memberId = member.id,
                        companySubContractId = subcontract.id,
                        activatedAt = LocalDateTime.now().minusDays(1)
                    ).toModel()
                    val activatedMember = member.copy(status = ACTIVE)
                    val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                    coEvery { memberService.get(beneficiary.memberId) } returns member
                    coEvery { subcontractService.get(subcontract.id) } returns subcontract
                    coEvery { contractService.findBySubcontractId(subcontract.id) } returns contract
                    coEvery {
                        preActivationPaymentService.getBySubcontractId(
                            subcontract.id, PreActivationPaymentService.FindOptions(
                                status = listOf(PreActivationPaymentStatus.PAID)
                            )
                        )
                    } returns listOf(preActivationPayment)
                    coEvery { beneficiaryDataService.get(beneficiary.id) } returns beneficiary
                    coEvery { memberService.activateMember(member) } returns activatedMember

                    val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                    assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                    coVerifyOnce { memberService.activateMember(any()) }
                    coVerifyOnce { subcontractService.get(any()) }
                    coVerifyOnce { contractService.findBySubcontractId(any()) }
                    coVerifyOnce {
                        preActivationPaymentService.getBySubcontractId(
                            any(), any()
                        )
                    }
                    coVerifyOnce { beneficiaryDataService.get(any()) }
                    coVerifyOnce { memberService.activateMember(any()) }
                }
            }

        @Test
        fun `#shouldnt activate the beneficiary if activatedAt date is before now and subcontract is PRE_PAY and MIG FIRST_PAYMENT wasnt PAID`() =
            runBlocking {
                val contract = TestModelFactory.buildCompanyContract()
                val subcontract = TestModelFactory.buildCompanySubContract(
                    paymentType = PaymentModel.PRE_PAY,
                    contractId = contract.id
                )
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1)
                ).toModel()

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberInvoiceGroupService.getBySubcontractId(subcontract.id, any()) } returns emptyList()

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(BeneficiaryActivationValidationException::class)

                coVerifyNone { memberService.activateMember(member) }
            }

        @Test
        fun `#shouldnt activate the beneficiary if activatedAt date is before now and subcontract is PRE_PAY and PAP FIRST_PAYMENT wasnt PAID`() =
            runBlocking {
                withFeatureFlags(
                    FeatureNamespace.MONEY_IN to mapOf(
                        "pre_activation_payment_for_group_company" to listOf("0001"),
                        "enable_pre_activation_payment" to true
                    )
                ) {
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001"
                    )

                    val subcontract = TestModelFactory.buildCompanySubContract(
                        paymentType = PaymentModel.PRE_PAY,
                        contractId = contract.id
                    )

                    val member = TestModelFactory.buildMember()
                    val beneficiary = TestModelFactory.buildBeneficiary(
                        memberId = member.id,
                        companySubContractId = subcontract.id,
                        activatedAt = LocalDateTime.now().minusDays(1)
                    ).toModel()

                    coEvery { memberService.get(beneficiary.memberId) } returns member
                    coEvery { subcontractService.get(subcontract.id) } returns subcontract
                    coEvery { contractService.findBySubcontractId(subcontract.id) } returns contract
                    coEvery {
                        preActivationPaymentService.getBySubcontractId(
                            subcontract.id, PreActivationPaymentService.FindOptions(
                                status = listOf(PreActivationPaymentStatus.PAID)
                            )
                        )
                    } returns emptyList()
                    coEvery { beneficiaryDataService.get(beneficiary.id) } returns beneficiary

                    val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                    assertThat(result).isFailureOfType(BeneficiaryActivationValidationException::class)

                    coVerifyOnce { subcontractService.get(any()) }
                    coVerifyOnce { contractService.findBySubcontractId(any()) }
                    coVerifyOnce {
                        preActivationPaymentService.getBySubcontractId(
                            any(), any()
                        )
                    }
                    coVerifyOnce { beneficiaryDataService.get(any()) }
                    coVerifyNone { memberService.activateMember(any()) }
                }
            }

        @Test
        fun `#shouldnt activate the beneficiary if activatedAt date is before now but subcontract externalId is null`() =
            runBlocking {
                val subcontract = TestModelFactory.buildCompanySubContract(externalId = null)
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companySubContractId = subcontract.id,
                    activatedAt = LocalDateTime.now().minusDays(1)
                ).toModel()

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { subcontractService.get(subcontract.id) } returns subcontract
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberInvoiceGroupService.getBySubcontractId(subcontract.id, any()) } returns emptyList()

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(BeneficiaryActivationValidationException::class)

                coVerifyNone { memberService.activateMember(member) }
            }

        @Test
        fun `#shouldnt activate the beneficiary if activatedAt date is after now`() =
            runBlocking {
                val beneficiary = TestModelFactory.buildBeneficiary(
                    activatedAt = LocalDateTime.now().plusDays(1)
                ).toModel()

                coEvery { beneficiaryDataService.get(any()) } returns beneficiary

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(BeneficiaryActivationValidationException::class)

                coVerifyNone { memberService.activateMember(any()) }
            }

        @Test
        fun `#should activate the beneficiary if subcontractId is null and there is only one subcontract on company`() =
            runBlocking {
                val subcontract = TestModelFactory.buildCompanySubContract(paymentType = PaymentModel.POST_PAY)
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    companySubContractId = null
                ).toModel()
                val beneficiaryWithSubcontract = beneficiary.copy(companySubContractId = subcontract.id)
                val activatedMember = member.copy(status = ACTIVE)
                val expectedBeneficiary = beneficiary.copy(memberStatus = ACTIVE)

                coEvery { memberService.get(beneficiary.memberId) } returns member
                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { memberService.activateMember(member) } returns activatedMember
                coEvery { subcontractService.findByCompanyId(beneficiary.companyId) } returns listOf(subcontract)
                coEvery { beneficiaryDataService.update(beneficiaryWithSubcontract) } returns
                        beneficiaryWithSubcontract
                coEvery {
                    kafkaProducerService.produce(
                        BeneficiaryChangedEvent(
                            beneficiaryWithSubcontract.toTransport(),
                            NotificationEventAction.UPDATED
                        ),
                        beneficiaryWithSubcontract.id.toString()
                    )
                } returns mockk()

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isSuccessWithData(expectedBeneficiary.toTransport())

                coVerifyOnce {
                    memberService.activateMember(member)
                    kafkaProducerService.produce(any(), any())
                }
            }

        @Test
        fun `#shouldnt activate the beneficiary if subcontractId is null and there are multiple subcontracts on company`() =
            runBlocking {
                val subcontract1 = TestModelFactory.buildCompanySubContract()
                val subcontract2 = TestModelFactory.buildCompanySubContract()
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    companySubContractId = null
                ).toModel()

                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { subcontractService.findByCompanyId(beneficiary.companyId) } returns listOf(
                    subcontract1,
                    subcontract2
                )

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(FailedToInferSubcontractException::class)

                coVerifyNone { memberService.activateMember(any()) }
            }

        @Test
        fun `#shouldnt activate the beneficiary if subcontractId is null and there are no subcontracts on company`() =
            runBlocking {
                val member = TestModelFactory.buildMember()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    activatedAt = LocalDateTime.now().minusDays(1),
                    companySubContractId = null
                ).toModel()

                coEvery { beneficiaryDataService.get(any()) } returns beneficiary
                coEvery { subcontractService.findByCompanyId(beneficiary.companyId) } returns emptyList()

                val result = beneficiaryService.activateBeneficiary(beneficiary.id)

                assertThat(result).isFailureOfType(FailedToInferSubcontractException::class)

                coVerifyNone { memberService.activateMember(any()) }
            }
    }

    @Test
    fun `#countActiveWithNoPendingCancellation should return correctly when type is not null`() = runBlocking {
        val companyId = RangeUUID.generate()
        val count = 10
        coEvery {
            beneficiaryDataService.count(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and this.archived.eq(false) and
                                this.memberStatus.eq(ACTIVE) and
                                this.canceledAt.isNull() and
                                this.type.eq(BeneficiaryType.EMPLOYEE)
                    }
                }
            )
        } returns count.success()

        val result = beneficiaryService.countActiveWithNoPendingCancellation(companyId, BeneficiaryType.EMPLOYEE)

        assertThat(result).isSuccessWithData(count)
    }


    @Test
    fun `#countActiveWithNoPendingCancellation should return correctly when type is null`() = runBlocking {
        val companyId = RangeUUID.generate()
        val count = 10
        coEvery {
            beneficiaryDataService.count(
                queryEq {
                    where {
                        this.companyId.eq(companyId) and this.archived.eq(false) and
                                this.memberStatus.eq(ACTIVE) and
                                this.canceledAt.isNull()
                    }
                }
            )
        } returns count.success()

        val result = beneficiaryService.countActiveWithNoPendingCancellation(companyId, null)

        assertThat(result).isSuccessWithData(count)
    }

    @Test
    fun `#checkContractAlreadySigned contract signed`() = runBlocking {
        val memberId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val subContract = TestModelFactory.buildCompanySubContract()
        val contract = TestModelFactory.buildCompanyContract(
            contractFileIds = listOf(
                ContractFile(
                    id = UUID.randomUUID(),
                    type = ContractType.MAIN,
                )
            )
        )

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        } returns beneficiary

        coEvery {
            subcontractService.get(beneficiary.companySubContractId!!)
        } returns subContract

        coEvery {
            contractService.get(subContract.contractId)
        } returns contract

        val result = beneficiaryService.checkContractAlreadySigned(memberId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })

            subcontractService.get(beneficiary.companySubContractId!!)
            contractService.get(subContract.contractId)
        }
    }

    @Test
    fun `#checkContractAlreadySigned contract not signed`() = runBlocking {
        val memberId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val subContract = TestModelFactory.buildCompanySubContract()
        val contract = TestModelFactory.buildCompanyContract(
            contractFileIds = emptyList()
        )

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        } returns beneficiary

        coEvery {
            subcontractService.get(beneficiary.companySubContractId!!)
        } returns subContract

        coEvery {
            contractService.get(subContract.contractId)
        } returns contract

        val result = beneficiaryService.checkContractAlreadySigned(memberId)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })

            subcontractService.get(beneficiary.companySubContractId!!)
            contractService.get(subContract.contractId)
        }
    }

    @Test
    fun `#checkContractAlreadySigned contract not signed (beneficiary without subcontract)`() = runBlocking {
        val memberId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = null).toModel()

        coEvery {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        } returns beneficiary

        val result = beneficiaryService.checkContractAlreadySigned(memberId)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce {
            beneficiaryDataService.findOne(queryEq {
                where { this.memberId.eq(memberId) and this.archived.eq(false) }
            })
        }

        coVerifyNone {
            subcontractService.get(any())
            contractService.get(any())
        }
    }

}
