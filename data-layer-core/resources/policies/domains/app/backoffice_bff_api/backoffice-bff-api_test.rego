package app.backoffice_bff_api_test

import rego.v1

import data.app.backoffice_bff_api

test_unauthenticated_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated", "key": "<EMAIL>"},
                "resource": {"opaType": "StaffModel", "email": "<EMAIL>"}
            }
        ]
    }
}


test_staffmodel_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "StaffModel"}
            }
        ]
    }
}

test_staffmodel_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "StaffModel"}
            }
        ]
    }
}

test_appointmentmacro_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_appointmentmacro_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_providertestcodemodel_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            }
        ]
    }
}

test_providertestcodemodel_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            }
        ]
    }
}

test_testpreparationmodel_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_testpreparationmodel_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_testpreparationmodel_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_testpreparationmodel_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_testpreparationmodel_delete_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}


test_appointment_schedule_option_model_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_health_goal_model_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "HealthGoalModel"}
            }
        ]
    }
}

test_health_goal_model_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "HealthGoalModel"}
            }
        ]
    }
}

test_health_goal_model_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "HealthGoalModel"}
            }
        ]
    }
}

test_health_goal_model_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "HealthGoalModel"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_program_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}


test_healthformanswergroup_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormAnswerGroup"}
            }
        ]
    }
}

test_healthformanswergroup_count_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "HealthFormAnswerGroup"}
            }
        ]
    }
}

test_personinternalreference_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_personinternalreference_count_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}



test_featureconfigmodel_view_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_featureconfigmodel_count_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_featureconfigmodel_create_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_featureconfigmodel_update_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_featureconfigmodel_delete_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_prescriptionsentencemodel_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescriptionsentencemodel_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescriptionsentencemodel_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescriptionsentencemodel_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescriptionsentencemodel_delete_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}


test_healthcareteamrecommendationmodel_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthcareTeamRecommendationModel"}
            }
        ]
    }
}

test_healthcareteamrecommendationmodel_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthcareTeamRecommendationModel"}
            }
        ]
    }
}

test_healthcareteamrecommendationmodel_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthcareTeamRecommendationModel"}
            }
        ]
    }
}

test_healthcareteamrecommendationmodel_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthcareTeamRecommendationModel"}
            }
        ]
    }
}

test_healthform_view_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_healthform_count_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_healthform_create_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_healthform_update_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_medical_specialty_model_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "MedicalSpecialtyModel"}
            }
        ]
    }
}

test_medical_specialty_model_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "MedicalSpecialtyModel"}
            }
        ]
    }
}

test_medical_specialty_model_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "MedicalSpecialtyModel"}
            }
        ]
    }
}

test_specialist_opinion_view_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}

test_specialist_opinion_count_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}

test_specialist_opinion_update_product_tech_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}

test_specialist_opinion_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}

test_specialist_opinion_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}

test_specialist_opinion_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "SpecialistOpinion"}
            }
        ]
    }
}


test_feature_config_model_view_gas_b2c_allowed_members_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "key": "gas_b2c_allowed_members", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel", "namespace": "ALICE_APP"}
            }
        ]
    }
}

test_feature_config_model_create_gas_b2c_allowed_members_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "key": "gas_b2c_allowed_members", "role": "PRODUCT_TECH"},
                "resource": {"opaType": "FeatureConfigModel", "namespace": "ALICE_APP"}
            }
        ]
    }
}

test_feature_config_model_update_gas_b2c_allowed_members_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "key": "gas_b2c_allowed_members", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel", "namespace": "ALICE_APP"}
            }
        ]
    }
}

test_service_script_node_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH", "key": "gas_b2c_allowed_members"},
                "resource": {"opaType": "ServiceScriptNode", "namespace": "ALICE_APP"}
            }
        ]
    }
}

test_service_script_node_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH", "key": "gas_b2c_allowed_members"},
                "resource": {"opaType": "ServiceScriptNode", "namespace": "ALICE_APP"}
            }
        ]
    }
}



test_service_script_node_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_feature_config_model_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_feature_config_model_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_feature_config_model_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_feature_config_model_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}

test_feature_config_model_delete_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "FeatureConfigModel"}
            }
        ]
    }
}
test_service_script_relationship_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_health_form_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_section_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormSection"}
            }
        ]
    }
}

test_health_form_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_section_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormSection"}
            }
        ]
    }
}

test_health_form_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_section_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormSection"}
            }
        ]
    }
}

test_health_form_question_view_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormQuestion", "type": "TRIGGER"}
            }
        ]
    }
}

test_health_form_question_count_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormQuestion", "type": "TRIGGER"}
            }
        ]
    }
}

test_health_form_question_create_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormQuestion", "type": "ACTION"}
            }
        ]
    }
}

test_health_form_question_update_product_tech_health_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
                "resource": {"opaType": "HealthFormQuestion", "type": "ACTION"}
            }
        ]
    }
}


test_prescription_sentence_model_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_create_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_update_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_delete_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_view_virtual_clinic_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "VIRTUAL_CLINIC_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_count_virtual_clinic_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "VIRTUAL_CLINIC_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_create_virtual_clinic_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "VIRTUAL_CLINIC_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_update_virtual_clinic_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "VIRTUAL_CLINIC_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_delete_virtual_clinic_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "VIRTUAL_CLINIC_PHYSICIAN"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_service_script_node_count_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_outcome_conf_view_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_relationship_view_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_count_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_create_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_update_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_health_form_view_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_count_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_create_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_update_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_service_script_node_view_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_count_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_health_logic_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_view_program_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_count_program_manager_nutritionist_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}


test_prescription_sentence_model_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_delete_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_medicine_model_view_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "MedicineModel"}
            }
        ]
    }
}

test_medicine_model_count_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "MedicineModel"}
            }
        ]
    }
}

test_medicine_model_create_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "MedicineModel"}
            }
        ]
    }
}

test_medicine_model_update_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "MedicineModel"}
            }
        ]
    }
}

test_health_form_view_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_count_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_create_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_update_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_service_script_node_count_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_outcome_conf_view_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_node_count_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_outcome_conf_view_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_view_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}



test_service_script_relationship_view_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_count_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_create_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_update_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_node_view_trigger_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_action_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_trigger_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_count_action_chief_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_trigger_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_action_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_trigger_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_count_action_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_program_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_count_program_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_health_logic_digital_care_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_outcome_conf_view_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_node_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_outcome_conf_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_relationship_view_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_create_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_update_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_create_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_relationship_update_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_service_script_node_view_trigger_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_action_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_trigger_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_count_action_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_trigger_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_action_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_trigger_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_count_action_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_program_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_count_program_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_health_logic_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_view_program_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_count_program_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_count_health_logic_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_outcome_conf_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_node_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_cbo_code_model_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}
test_service_script_action_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_execution_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_bud_node_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_health_form_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}


test_alice_agora_working_hours_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_delete_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_channel_macro_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_delete_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}


test_service_script_node_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_create_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_update_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_appointment_macro_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_appointment_macro_count_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_appointment_macro_update_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_appointment_macro_create_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "AppointmentMacro"}
            }
        ]
    }
}

test_health_form_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_update_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_health_form_create_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_alice_agora_working_hours_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_alice_agora_working_hours_delete_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AliceAgoraWorkingHours"}
            }
        ]
    }
}

test_appointment_template_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_delete_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}


test_cbo_code_model_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_person_internal_reference_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_internal_reference_count_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_model_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_staff_model_view_health_ops_role_lead_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "StaffModel"}
            }
        ]
    }
}

test_staff_model_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "StaffModel"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_update_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_create_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_clinical_outcomes_consolidated_calculator_conf_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ClinicalOutcomesConsolidatedCalculatorConf"}
            }
        ]
    }
}

test_service_script_action_view_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_health_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}


test_outcome_conf_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_node_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_cbo_code_model_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_service_script_action_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_execution_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_bud_node_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_health_community_specialist_model_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            }
        ]
    }
}

test_health_community_specialist_model_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            }
        ]
    }
}

test_health_community_specialist_model_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            }
        ]
    }
}

test_health_community_specialist_model_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            }
        ]
    }
}


test_contact_model_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_delete_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_prescription_sentence_model_view_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_count_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_create_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_update_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}

test_prescription_sentence_model_delete_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "PrescriptionSentenceModel"}
            }
        ]
    }
}


test_service_script_node_view_trigger_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_trigger_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_update_trigger_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_view_action_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_create_action_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_action_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_view_program_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_create_program_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_update_program_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_create_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_update_health_logic_care_coord_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CARE_COORD_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_appointment_schedule_option_model_view_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_count_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_create_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_appointment_schedule_option_model_update_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "AppointmentScheduleOptionModel"}
            }
        ]
    }
}

test_healthcare_additional_team_view_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_additional_team_count_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_additional_team_create_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_additional_team_update_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}


test_contact_model_view_HEALTH_OPS_MULTI_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_count_HEALTH_OPS_MULTI_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_contact_model_delete_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_cbo_code_model_view_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_cbo_code_model_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "CboCodeModel"}
            }
        ]
    }
}

test_service_script_action_view_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_person_internal_reference_view_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_internal_reference_count_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_model_view_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_service_script_node_count_health_ops_multi_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_MULTI"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_count_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_view_program_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_create_program_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_update_program_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            }
        ]
    }
}

test_service_script_node_view_health_logic_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_create_health_logic_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_service_script_node_update_health_logic_health_ops_lead_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_OPS_LEAD"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_appointment_schedule_model_view_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "AppointmentScheduleModel"}
            }
        ]
    }
}

test_appointment_schedule_model_count_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "AppointmentScheduleModel"}
            }
        ]
    }
}

test_insurance_portability_request_model_view_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestModel"}
            }
        ]
    }
}

test_insurance_portability_request_model_count_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestModel"}
            }
        ]
    }
}

test_insurance_portability_request_model_create_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestModel"}
            }
        ]
    }
}

test_insurance_portability_request_model_update_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestModel"}
            }
        ]
    }
}

test_insurance_portability_request_model_delete_member_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "MEMBER_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestModel"}
            }
        ]
    }
}

test_healthcare_team_model_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_additional_team_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_additional_team_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_person_clinical_account_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_product_model_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "ProductModel"}
            }
        ]
    }
}

test_product_model_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "ProductModel"}
            }
        ]
    }
}

test_product_model_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "ProductModel"}
            }
        ]
    }
}

test_product_model_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "ProductModel"}
            }
        ]
    }
}


test_health_condition_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_delete_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_template_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_delete_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}


test_health_condition_view_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_create_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_update_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_delete_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_template_view_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_create_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_update_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_template_delete_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "HealthConditionTemplate"}
            }
        ]
    }
}

test_health_condition_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_delete_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}



test_beneficiary_onboarding_phase_view_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_count_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_create_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_update_chief_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_view_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_count_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_create_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_update_health_declaration_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "HEALTH_DECLARATION_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_view_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_count_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_create_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_beneficiary_onboarding_phase_update_risk_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
                "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}
            }
        ]
    }
}

test_healthcare_additional_team_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_additional_team_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthcareAdditionalTeam"}
            }
        ]
    }
}

test_healthcare_team_model_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}
test_person_internal_reference_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_internal_reference_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_person_model_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_health_condition_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_axis_view_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthConditionAxis"}
            }
        ]
    }
}

test_health_condition_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_axis_count_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthConditionAxis"}
            }
        ]
    }
}

test_health_condition_create_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_axis_create_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthConditionAxis"}
            }
        ]
    }
}

test_health_condition_update_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_axis_update_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthConditionAxis"}
            }
        ]
    }
}

test_health_condition_delete_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_health_condition_axis_delete_med_risk_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "MED_RISK"},
                "resource": {"opaType": "HealthConditionAxis"}
            }
        ]
    }
}

test_outcome_conf_view_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_outcome_conf_count_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "OutcomeConf"}
            }
        ]
    }
}

test_service_script_node_view_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_service_script_node_count_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}


test_service_script_action_view_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_update_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_action_create_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptAction"}
            }
        ]
    }
}

test_service_script_execution_view_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_update_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}

test_service_script_execution_create_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptExecution"}
            }
        ]
    }
}



test_bud_node_count_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_view_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_update_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_bud_node_create_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "BudNode"}
            }
        ]
    }
}

test_health_demand_monitoring_count_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthDemandMonitoring"}
            }
        ]
    }
}

test_health_demand_monitoring_view_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthDemandMonitoring"}
            }
        ]
    }
}

test_health_demand_monitoring_update_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthDemandMonitoring"}
            }
        ]
    }
}

test_health_demand_monitoring_create_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "HealthDemandMonitoring"}
            }
        ]
    }
}

test_service_script_node_view_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_update_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_create_med_ex_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MED_EX"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_view_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}

test_service_script_node_update_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            }
        ]
    }
}

test_service_script_node_create_quality_nurse_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "QUALITY_NURSE"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            }
        ]
    }
}


test_healthcare_team_model_view_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_person_model_view_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_healthcare_team_model_view_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_person_model_view_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_update_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_create_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_view_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_update_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_create_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_insurance_portability_health_insurance_model_view_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}
            }
        ]
    }
}

test_insurance_portability_health_insurance_model_count_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}
            }
        ]
    }
}

test_insurance_portability_health_insurance_model_update_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}
            }
        ]
    }
}

test_insurance_portability_health_insurance_model_create_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}
            }
        ]
    }
}

test_insurance_portability_health_insurance_model_delete_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}
            }
        ]
    }
}

test_insurance_portability_request_file_model_view_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestFileModel"}
            }
        ]
    }
}

test_insurance_portability_request_file_model_count_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityRequestFileModel"}
            }
        ]
    }
}

test_insurance_portability_request_file_model_update_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestFileModel"}
            }
        ]
    }
}

test_insurance_portability_request_file_model_create_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "InsurancePortabilityRequestFileModel"}
            }
        ]
    }
}

test_insurance_portability_request_file_model_delete_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "InsurancePortabilityRequestFileModel"}
            }
        ]
    }
}

test_health_plan_task_template_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_appointment_template_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NUTRITIONIST"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PHYSICAL_EDUCATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_PHYSICAL_EDUCATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_delete_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PSYCHOLOGIST"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}


test_health_plan_task_template_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_health_plan_task_template_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HealthPlanTaskTemplate"}
            }
        ]
    }
}

test_appointment_template_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_create_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_appointment_template_delete_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "AppointmentTemplate"}
            }
        ]
    }
}

test_health_professional_model_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR", "id": "subject-1"},
                "resource": {"opaType": "HealthProfessionalModel", "staffId": "subject-1"}
            }
        ]
    }
}

test_health_professional_model_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS", "id": "subject-2"},
                "resource": {"opaType": "HealthProfessionalModel", "staffId": "subject-2"}
            }
        ]
    }
}

test_health_professional_model_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR", "id": "subject-3"},
                "resource": {"opaType": "HealthProfessionalModel", "staffId": "subject-3"}
            }
        ]
    }
}

test_staff_model_view_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS", "id": "subject-4"},
                "resource": {"opaType": "StaffModel", "id": "subject-4"}
            }
        ]
    }
}

test_staff_model_count_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NUTRITIONIST", "id": "subject-5"},
                "resource": {"opaType": "StaffModel", "id": "subject-5"}
            }
        ]
    }
}

test_staff_model_update_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "MANAGER_NUTRITIONIST", "id": "subject-6"},
                "resource": {"opaType": "StaffModel", "id": "subject-6"}
            }
        ]
    }
}

test_staff_model_view_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR", "id": "subject-7"},
                "resource": {"opaType": "StaffModel", "id": "subject-7"}
            }
        ]
    }
}

test_staff_model_count_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR", "id": "subject-8"},
                "resource": {"opaType": "StaffModel", "id": "subject-8"}
            }
        ]
    }
}

test_staff_model_update_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR", "id": "subject-9"},
                "resource": {"opaType": "StaffModel", "id": "subject-9"}
            }
        ]
    }
}

test_staff_model_view_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS", "id": "subject-10"},
                "resource": {"opaType": "StaffModel", "id": "subject-10"}
            }
        ]
    }
}

test_staff_model_count_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS", "id": "subject-11"},
                "resource": {"opaType": "StaffModel", "id": "subject-11"}
            }
        ]
    }
}

test_staff_model_update_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS", "id": "subject-12"},
                "resource": {"opaType": "StaffModel", "id": "subject-12"}
            }
        ]
    }
}

test_person_internal_reference_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_member_model_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "MemberModel"}
            }
        ]
    }
}


test_person_model_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_test_code_model_view_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_test_code_model_count_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_test_code_model_update_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_test_code_model_create_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_test_preparation_model_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_delete_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_code_package_model_view_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodePackageModel"}
            }
        ]
    }
}

test_test_code_package_model_count_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodePackageModel"}
            }
        ]
    }
}

test_test_code_package_model_update_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodePackageModel"}
            }
        ]
    }
}

test_test_code_package_model_create_chief_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestCodePackageModel"}
            }
        ]
    }
}


test_person_model_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_person_model_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_test_preparation_model_view_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_count_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_update_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_create_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_test_preparation_model_delete_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}


test_person_internal_reference_view_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_member_model_view_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
                "resource": {"opaType": "MemberModel"}
            }
        ]
    }
}


test_file_vault_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "FileVault", "domain": "interop", "namespace": "exam_by_provider"}
            }
        ]
    }
}

test_file_vault_view_navigator_ops_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "NAVIGATOR_OPS"},
                "resource": {"opaType": "FileVault", "domain": "eita", "namespace": "provider_health_document"}
            }
        ]
    }
}


test_channel_tag_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelTag"}
            }
        ]
    }
}

test_channel_tag_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelTag"}
            }
        ]
    }
}

test_channel_tag_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelTag"}
            }
        ]
    }
}

test_channel_tag_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelTag"}
            }
        ]
    }
}

test_channel_tag_delete_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelTag"}
            }
        ]
    }
}

test_channel_macro_view_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_count_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_update_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_create_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}

test_channel_macro_delete_chief_navigator_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ChannelMacro"}
            }
        ]
    }
}


test_healthcare_team_model_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_person_clinical_account_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_update_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_create_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_healthcare_team_model_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_servicescriptnode_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_servicescriptrelationship_count_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}

test_servicescriptnode_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptNode"}
            }
        ]
    }
}

test_servicescriptrelationship_view_chief_digital_care_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "ServiceScriptRelationship"}
            }
        ]
    }
}


test_healthcare_team_model_view_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_healthcare_team_model_count_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_person_clinical_account_view_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_count_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_update_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_person_clinical_account_create_chief_physician_role_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_PHYSICIAN"},
                "resource": {"opaType": "PersonClinicalAccount"}
            }
        ]
    }
}

test_product_tech_staff_editor_StaffModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            }
        ],
        "rootService": {
            "name": "BACKOFFICE_BFF_API_ROOT_SERVICE_NAME"
        }
    }
}

test_product_tech_staff_editor_HealthProfessionalModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            }
        ]
    }
}

test_product_tech_health_staff_editor_ContactModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}


test_product_tech_health_staff_editor_StaffModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "StaffModel"}
            }
        ],
        "rootService": {
            "name": "BACKOFFICE_BFF_API_ROOT_SERVICE_NAME"
        }
    }
}

test_product_tech_health_staff_editor_HealthProfessionalModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "HealthProfessionalModel"}
            }
        ]
    }
}

test_product_tech_health_staff_editor_ContactModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH_STAFF_EDITOR"},
                "resource": {"opaType": "ContactModel"}
            }
        ]
    }
}

test_chief_digital_care_nurse_HealthcareTeamModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthcareTeamModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthcareTeamModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthcareTeamModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_chief_digital_care_nurse_TestCodeModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_chief_digital_care_physician_TestCodeModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_chief_digital_care_physician_HealthcareTeamChannel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamChannel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamChannel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamChannel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthcareTeamChannel"}
            }
        ]
    }
}

test_chief_digital_care_nurse_HealthCondition_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_chief_digital_care_nurse_TestPreparationModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_chief_digital_care_physician_HealthCondition_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthCondition"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "HealthCondition"}
            }
        ]
    }
}

test_chief_digital_care_physician_TestPreparationModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_chief_navigator_PersonInternalReference_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_chief_navigator_MemberModel_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "MemberModel"}
            }
        ]
    }
}

test_chief_digital_care_nurse_PersonInternalReference_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_chief_digital_care_nurse_MemberModel_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_DIGITAL_CARE_NURSE"},
                "resource": {"opaType": "MemberModel"}
            }
        ]
    }
}

test_chief_navigator_ProductModel_allowed if {
    {1,2} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ProductModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "ProductModel"}
            }
        ]
    }
}

test_chief_navigator_HippocratesHealthcareProfessionalModel_allowed if {
    {1,2,3} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            }
        ]
    }
}

test_chief_navigator_PersonModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR"},
                "resource": {"opaType": "PersonModel"}
            }
        ]
    }
}

test_chief_navigator_ops_HealthcareResourceGroupAssociationModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthcareResourceGroupAssociationModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthcareResourceGroupAssociationModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthcareResourceGroupAssociationModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "HealthcareResourceGroupAssociationModel"}
            }
        ]
    }
}

test_chief_navigator_ops_TestPreparationModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_chief_navigator_ops_TestPreparationModel_allowed if {
    {1} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "CHIEF_NAVIGATOR_OPS"},
                "resource": {"opaType": "RoutingRule", "name": "Chat ADM"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_view_count_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "MemberModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode"}
            },
            {
                "index": 3,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            },
            {
                "index": 4,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HealthcareTeamModel"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_view_count_create_allowed if {
    {1,2,3} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "HippocratesHealthcareProfessionalModel"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_TestCodeModel_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_StructuredAddress_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StructuredAddress"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StructuredAddress"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StructuredAddress"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StructuredAddress"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_ProviderTestCodeModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ProviderTestCodeModel"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_trigger_or_action_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode", "type": "TRIGGER"}
            },
            {
                "index": 2,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode", "type": "ACTION"}
            },
            {
                "index": 3,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptRelationship", "type": "TRIGGER"}
            },
            {
                "index": 4,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptRelationship", "type": "ACTION"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_program_or_health_logic_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode", "type": "PROGRAM"}
            },
            {
                "index": 2,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptNode", "type": "HEALTH_LOGIC"}
            },
            {
                "index": 3,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptRelationship", "type": "PROGRAM"}
            },
            {
                "index": 4,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "ServiceScriptRelationship", "type": "HEALTH_LOGIC"}
            }
        ]
    }
}

test_insurance_ops_health_institution_ops_community_specialist_or_health_professional_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StaffModel", "type": "COMMUNITY_SPECIALIST"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StaffModel", "type": "COMMUNITY_SPECIALIST"}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StaffModel", "type": "COMMUNITY_SPECIALIST"}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
                "resource": {"opaType": "StaffModel", "type": "COMMUNITY_SPECIALIST"}
            }
        ]
    }
}

test_insurance_ops_community_success_HealthForm_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "HealthForm"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "HealthForm"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "HealthForm"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"},
                "resource": {"opaType": "HealthForm"}
            }
        ]
    }
}

test_technique_nurse_view_allowed if {
    {1,2} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "MemberModel"}
            },
            {
                "index": 2,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_technique_nurse_view_count_create_update_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestCodeModel"}
            }
        ]
    }
}

test_technique_nurse_TestPreparationModel_allowed if {
    {1,2,3,4,5} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {"opaType": "StaffModel", "role": "TECHNIQUE_NURSE"},
                "resource": {"opaType": "TestPreparationModel"}
            }
        ]
    }
}

test_cx_ops_view_count_allowed if {
    {1,2,3,4} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CX_OPS"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CX_OPS"},
                "resource": {"opaType": "HealthCommunitySpecialistModel"}
            },
            {
                "index": 3,
                "action": "view",
                "subject": {"opaType": "StaffModel", "role": "CX_OPS"},
                "resource": {"opaType": "PersonInternalReference"}
            },
            {
                "index": 4,
                "action": "count",
                "subject": {"opaType": "StaffModel", "role": "CX_OPS"},
                "resource": {"opaType": "PersonInternalReference"}
            }
        ]
    }
}

test_email_domain_view_count_allowed if {
    {1,2} == backoffice_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "EmailDomain"},
                "resource": {"opaType": "HealthcareResourceModel"}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "EmailDomain"},
                "resource": {"opaType": "HealthcareResourceModel"}
            }
        ]
    }
}
