package app.exec_indicator_domain_service_test

import rego.v1

import data.app.exec_indicator_domain_service

test_view_count_update_create_models_allowed if {
	{1,2,3,4} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "GuiaWithProceduresModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "GuiaWithProceduresModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "GuiaWithProceduresModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "resource": {
                "opaType": "GuiaWithProceduresModel",
            },
        }]
	}
}

test_view_count_update_create__resource_bundle_specialty_pricing_update_model if {
	{1,2,3,4} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingUpdateModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingUpdateModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingUpdateModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingUpdateModel",
            },
        }]
	}
}

test_view_count_update_create__resource_bundle_specialty_model if {
	{1,2,3,4} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ResourceBundleSpecialtyModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "ResourceBundleSpecialtyModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "ResourceBundleSpecialtyModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "resource": {
                "opaType": "ResourceBundleSpecialtyModel",
            },
        }]
	}
}

test_view_count_update_create__resource_bundle_specialty_pricing_model if {
	{1,2,3,4} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingModel",
            },
        }]
	}
}

test_unauth_view_contact_allowed if {
	{1,2} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "ContactModel",
            },
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HealthCommunitySpecialistModel",
            },
        }]
	}
}

test_unauth_view_update_create_models_allowed if {
	{1,2,3} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "GlossAuthorizationInfoModel",
            },
        },
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "GlossAuthorizationInfoModel",
            },
		},
		{
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "GlossAuthorizationInfoModel",
            },
        }]
	}
}

test_unauth_view_backfill_models_allowed if {
	{1,2} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HealthEventsModel",
            },
		},
		{
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HealthcareResourceGroupModel",
            },
        }]
	}
}

test_unauth_view_update_create_person_health_event_allowed if {
	{1,2,3} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
        },
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
		},
		{
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
        }]
	}
}

test_unauth_view_update_magic_numbers_allowed if {
	{1,2} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "MagicNumbersModel",
            },
        },
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "MagicNumbersModel",
            },
		}]
	}
}

test_unauth_view_update_create_person_health_event_denied if {
	not {1,2,3} == exec_indicator_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "861a1b75-40f9-43ac-88e7-901a1c00e100"
            },
        },
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "861a1b75-40f9-43ac-88e7-901a1c00e100"
            },
		},
		{
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "bbe803d3-9c8d-4618-8e9f-292c4a2e6b00"
            },
            "resource": {
                "opaType": "PersonHealthEvent",
                "referencedModelId": "861a1b75-40f9-43ac-88e7-901a1c00e100"
            },
        }]
	}
}
