package app.membership_domain_service_subscribers_test

import data.app.membership_domain_service_subscribers
import rego.v1

test_unauth_view_own_models_allow if {
	{1, 2, 3, 4, 5} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "DeviceModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonInternalReference",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "AppointmentScheduleModel",
				"personId": "123",
			},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "ClinicalBackground",
				"personId": "123",
			},
		},
		{
			"index": 5,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "Appointment",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_OnboardingContract_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingContractModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingContractModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingContractModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_MemberRegistration_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberRegistrationModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberRegistrationModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberRegistrationModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_OnboardingBackgroundCheckModel_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingBackgroundCheckModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingBackgroundCheckModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "OnboardingBackgroundCheckModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_MemberModel_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "MemberModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_PersonRegistration_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonRegistrationModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonRegistrationModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonRegistrationModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_PersonLogin_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonLoginModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonLoginModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PersonLoginModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_ProductOrder_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "ProductOrderModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "ProductOrderModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "ProductOrderModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_HealthDeclaration_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "HealthDeclaration",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "HealthDeclaration",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "HealthDeclaration",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestFileModel",
				"personId": "123",
			},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestFileModel",
				"personId": "123",
			},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "InsurancePortabilityRequestFileModel",
				"personId": "123",
			},
		},
	]}
}

test_unauth_CRU_own_PromoCode_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PromoCodeModel",
				"ownerPersonId": "123",
			},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PromoCodeModel",
				"ownerPersonId": "123",
			},
		},
		{
			"index": 3,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "123"},
			"resource": {
				"opaType": "PromoCodeModel",
				"ownerPersonId": "123",
			},
		},
	]}
}

test_unauth_CR_count_FileVault_on_domain_allow if {
	{1, 2} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "term",
			},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "term",
			},
		},
	]}
}

test_unauth_RU_FileVault_on_domain_allow if {
	{1, 2} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "documents",
			},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "documents",
			},
		},
	]}
}

test_unauth_view_count_resources_allow if {
	resources = [
		"BeneficiaryModel",
		"BeneficiaryOnboardingModel",
		"BeneficiaryOnboardingPhaseModel",
		"CompanyModel",
		"CompanyContractModel",
		"CompanySubContractModel",
		"GenericFileVault",
	]

	every resource in resources {
		{1, 2} == membership_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauth_CRU_resources_allow if {
	resources = [
		"PersonOnboardingModel",
		"PersonModel",
		"PersonSalesInfo",
		"DealSalesInfo",
		"MemberProductPriceModel",
		"MemberProductPriceAdjustmentModel",
		"MemberAccreditedNetworkTrackerModel",
	]

	every resource in resources {
		{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauth_CR_MemberContractTerm_allow if {
	{1, 2} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_unauth_CRU_count_CassiMember_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_unauth_view_resources_allow if {
	resources = [
		"AppointmentScheduleEventTypeModel",
		"EventTypeProviderUnitModel",
		"MedicalSpecialtyModel",
		"ZendeskExternalReference",
		"PromoCodeModel",
		"Lead",
		"ProductModel",
		"ProductBundleModel",
		"PriceListingModel",
		"ProductPriceListingModel",
	]

	every resource in resources {
		{1} == membership_domain_service_subscribers.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_unauth_view_resources_2_group_allow if {
	resources = [
		"StaffModel",
		"HealthProductSimulation",
		"HealthProductSimulationGroup",
		"HealthCondition",
		"ProductPriceAdjustmentModel",
		"MemberModel",
		"ProviderModel",
		"PersonCalendlyModel",
		"MemberContractModel",
	]

	every resource in resources {
		{1} == membership_domain_service_subscribers.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_unauth_create_HealthPlanTaskStatusHistory_allow if {
	{1} == membership_domain_service_subscribers.allow with input as {"cases": [{
		"index": 1,
		"action": "create",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "HealthPlanTaskStatusHistoryModel"},
	}]}
}

test_unauth_view_update_MemberProductChangeScheduleModel_allow if {
	{1, 2} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_PersonModel_CRU_own_HealthDeclaration_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
	]}
}

test_PersonModel_CRU_own_HealthDeclaration_denied if {
	{1, 2, 3} != membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "1234"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "1234"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "PersonSubject", "id": "123"},
			"resource": {"opaType": "HealthDeclaration", "personId": "1234"},
		},
	]}
}

test_PersonModel_CRU_dependents_HealthDeclaration_allow if {
	{1, 2, 3} == membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "123"},
		},
	]}
}

test_PersonModel_CRU_dependents_HealthDeclaration_denied if {
	{1, 2, 3} != membership_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "789"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "789"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "PersonSubject", "dependentPersons": ["123", "321"]},
			"resource": {"opaType": "HealthDeclaration", "personId": "789"},
		},
	]}
}
