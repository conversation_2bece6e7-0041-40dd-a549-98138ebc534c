package app.system_ops_api_test

import rego.v1

import data.app.system_ops_api

test_health_professional_model_comprehensive if {
    {1,2,3,4,5,6,7,8,9,10,11,12,13} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 3,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CARE_COORD_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 7,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CARE_COORD_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 8,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CARE_COORD_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 9,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CARE_COORD_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 10,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_COMMUNITY"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 11,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_COMMUNITY"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 12,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_COMMUNITY"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 13,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_COMMUNITY"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 14,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "id": "123"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel",
                    "staffId": "123"
                }
            }
        ]
    }
}

test_health_professional_ops_profile_comprehensive if {
    {1,2,3,4,5,6,7,8} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 7,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS"
                },
                "resource": {
                    "opaType": "HealthProfessionalOpsProfileModel"
                }
            }
        ]
    }
}

test_company_model_comprehensive if {
    {1,2,3,4,5,6,7,8,9,10,11,12} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 7,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            },
            {
                "index": 11,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            },
            {
                "index": 12,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            }
        ]
    }
}

test_staff_view_refund if {
    set() == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "RefundCostInfoModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyRefundCostInfoModel"
                }
            }
        ]
    }
}

test_staff_view_person_internal_reference if {
    {1,2,3,4,5} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_RISK"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "MED_RISK"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "OPS"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            }
        ]
    }
}

test_staff_view_company_staff if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyStaffModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyStaffModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            }
        ]
    }
}

test_staff_update_company_staff if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyStaffModel"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyStaffModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            }
        ]
    }
}

test_staff_view_beneficiary if {
    {1,2,3,4,5} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CassiMemberModel"
                }
            }
        ]
    }
}

test_staff_view_company_contract if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            }
        ]
    }
}

test_staff_create_company_contract if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            }
        ]
    }
}

test_staff_view_eta if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "OutcomeConf"
                }
            }
        ]
    }
}

test_staff_update_eta if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "TRIGGER"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            }
        ]
    }
}

test_staff_view_health_logic if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "PROGRAM"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                }
            }
        ]
    }
}

test_staff_update_health_logic if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "HEALTH_LOGIC"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                }
            }
        ]
    }
}

test_staff_view_protocols if {
    {1,2,3,4,5} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BudNode"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BudNode"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "Protocol"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptExecution"
                }
            }
        ]
    }
}

test_staff_update_protocols if {
    {1,2,3,4,5} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BudNode"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "BudNode"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "Protocol"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 5,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigation"
                }
            }
        ]
    }
}

test_staff_view_and_update_prescriptions if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "DIGITAL_CARE_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PrescriptionSentenceModel"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "DIGITAL_CARE_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PrescriptionSentenceModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "DIGITAL_CARE_NURSE"
                },
                "resource": {
                    "opaType": "MedicineModel"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "DIGITAL_CARE_NURSE"
                },
                "resource": {
                    "opaType": "MedicineModel"
                }
            }
        ]
    }
}

test_staff_view_person_internal_reference if {
    {1} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            }
        ]
    }
}

test_staff_view_health_condition if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthCondition"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthConditionTemplate"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthConditionAxis"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "HealthConditionRelated"
                }
            }
        ]
    }
}

test_staff_view_and_update_specialist_opinion if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            }
        ]
    }
}

test_staff_view_sales_firm if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SalesFirm"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SalesFirmStaff"
                }
            }
        ]
    }
}

test_staff_upsert_sales_firm if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SalesFirm"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SalesFirmStaff"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "SalesAgent"
                }
            }
        ]
    }
}

test_email_domain_view_healthcare_resource if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "EmailDomain"
                },
                "resource": {
                    "opaType": "HealthcareResourceModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "EmailDomain"
                },
                "resource": {
                    "opaType": "HealthcareResourceModel"
                }
            }
        ]
    }
}

test_service_script_node_trigger if {
    {1,2,3} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "MED_EX"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "TRIGGER"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "MED_EX"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "TRIGGER"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "MED_EX"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "TRIGGER"
                }
            }
        ]
    }
}

test_service_script_node_action if {
    {1,2} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_DIGITAL_CARE_PHYSICIAN"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "ACTION"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "ACTION"
                }
            }
        ]
    }
}

test_service_script_node_program if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "PROGRAM"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "PROGRAM"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "PROGRAM"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "PROGRAM"
                }
            }
        ]
    }
}

test_service_script_node_health_logic if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "HEALTH_LOGIC"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "HEALTH_LOGIC"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "HEALTH_LOGIC"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptNode",
                    "type": "HEALTH_LOGIC"
                }
            }
        ]
    }
}

test_service_script_relationship if {
    {1,2,3,4} == system_ops_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                }
            }
        ]
    }
}
