package br.com.alice.data.layer.policies

import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.EHR_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.pipelines.subjects.CareCoordinationSubject
import br.com.alice.data.layer.pipelines.subjects.ChiefRiskSubject
import br.com.alice.data.layer.pipelines.subjects.DigitalCareProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.ExternalHealthProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.HealthAuditSubject
import br.com.alice.data.layer.pipelines.subjects.HealthOpsSubject
import br.com.alice.data.layer.pipelines.subjects.HealthProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.InternalHealthProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.OnSiteProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import br.com.alice.data.layer.policies.features.*
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Unauthenticated

val ehrPolicySet = policySet {

    match("at ehr-api", { rootService.name == EHR_API_ROOT_SERVICE_NAME }) {
        commonPolicySet()
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Community Specialist") { resource is StaffModel && resource.isCommunity() }
                match("any Health Professional") { resource is HealthProfessionalModel }
                match("StaffModel attempting to login with") { staffAttemptingToLoginWith() }
                match("Test Person") { testPerson() }
                match("Test Member") { testPersonResource(MemberModel::class) }
                match("any HealthPlanTask") {
                    resource is HealthPlanTask && resource.content?.get("token") == (subject as Unauthenticated).key
                }
                match("any Appointment") {
                    resource is Appointment && resource.excuseNotes.any { it.token == (subject as Unauthenticated).key }
                }
                match("any StaffModel") { resource is StaffModel }
            }
            allows(MagicNumbersModel::class, View, Update, Create)
        }

        match("Health Professionals", { subject is HealthProfessionalSubject || subject is CareCoordinationSubject }) {
            match("can view", { action is View }) {
                match("any ChannelFup") { resourceIs(ChannelFup::class) }
            }
            match("can view, update and create", { action is View || action is Update || action is Create }) {
                match("PersonHealthEvent associated with her") {
                    resource is PersonHealthEvent && resource.staffId == (subject as HealthProfessionalSubject).id
                }
                match("any RefundCounterReferral") { resource is RefundCounterReferralModel }
                match("any TimelineAiSummaryReview") { resource is TimelineAiSummaryReview }
            }
            match("can count", { action is Count }) {
                match("any PersonHealthEvent") { resourceIs(PersonHealthEvent::class) }
            }
            match("can view", { action is View }) {
                match("any HealthPlanTaskTemplate") { resource is HealthPlanTaskTemplate }
                match("any HealthPlanTaskGroupTemplate") { resource is HealthPlanTaskGroupTemplate }
                match("any AppointmentTemplate") { resource is AppointmentTemplate }
                match("any ScreeningNavigation") { resource is ScreeningNavigation }
            }

            includes(viewAppointmentMacro)
            includes(viewAssistanceSummary)
            allows(AppointmentProcedureExecuted::class, View, Count, Create, Delete)
            allows(SuggestedProcedure::class, View, Count)

        }

        match(
            "External Health Professionals with person in portfolio",
            { subject is ExternalHealthProfessionalSubject && subject.memberInPortfolio }) {
            match("can view", { action is View }) {
                match("any ExternalHealthInformation") { resource is ExternalHealthInformation }
                match("any PersonModel Clinical Account") { resource is PersonClinicalAccount }
                match("any MemberModel") { resource is MemberModel }
                match("any MemberModel Product Price") { resource is MemberProductPriceModel }
                match("any Onboarding") { resource is PersonOnboardingModel }
                match("any Appointment") { resource is Appointment }
                match("any AssistanceCare") { resource is AssistanceCare }
                match("any PersonTaskModel") { resource is PersonTaskModel }
                includes(viewQuestionnaire)
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any AppointmentScheduleModel") { resource is AppointmentScheduleModel }
                match("any OutcomeConf") { resource is OutcomeConf }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any Curiosity Note") { resource is CuriosityNoteModel }
                match("any Health Information") { resource is HealthInformation && resource !is Appointment && resource !is AssistanceCare }
                match("any PersonModel") { resource is PersonModel }
                match("any PersonModel Internal Reference") { resource is PersonInternalReference }
                match("any ServiceScriptNode") { resource is ServiceScriptNode }
                match("any PersonAdditionalInfoModel") { resource is PersonAdditionalInfoModel }
                match("some Appointment types") {
                    resource is Appointment && (
                            resource.isDefault ||
                                    resource.isAnnotation ||
                                    resource.isAnnotationHealthCommunity ||
                                    resource.isFollowUpVisit
                            )
                }
            }
            match("can count", { action is Count }) {
                match("any Health Information") { resourceIs(HealthInformation::class) }
            }
            includes(viewAppointmentsAndExams)
        }

        match("External Health Professionals", { subject is ExternalHealthProfessionalSubject }) {
            match("can view and create", { action is View || action is Create }) {
                match("any AppointmentEvolution") { resource is AppointmentEvolution }
            }
            match("can view", { action is View }) {
                match("any Timeline") { resource is Timeline }
            }
            match("can count", { action is Count }) {
                match("any Health Information") { resourceIs(HealthInformation::class) }
            }
            allows(AppointmentProcedureExecuted::class, View, Count, Create, Delete)
            allows(HealthcareResourceModel::class, View, Count)
            allows(HealthSpecialistResourceBundleModel::class, View, Count)
            allows(OngoingCompanyDeal::class, View, Count)
            allows(SuggestedProcedure::class, View, Count)
        }

        match("Chief Risk", { subject is ChiefRiskSubject }) {
            allows(AliceTestResultBundle::class, View, Count)
            allows(Appointment::class, View, Count, Create, Update)
            allows(AppointmentEvent::class, View, Count, Create, Update)
            allows(AppointmentEvolution::class, View, Count, Create, Update)
            allows(AppointmentMacro::class, View, Count)
            allows(AppointmentProcedureExecuted::class, View, Count)
            allows(AppointmentScheduleModel::class, View, Count)
            allows(AppointmentTemplate::class, View, Count, Create, Update)
            allows(AssistanceCare::class, View, Count)
            allows(AssistanceSummaryModel::class, View, Count)
            allows(BeneficiaryModel::class, View, Count)
            allows(CaseRecord::class, View, Count, Create)
            allows(ClinicalBackground::class, View, Count)
            allows(CompanyModel::class, View, Count)
            allows(CounterReferral::class, View, Count)
            allows(CuriosityNoteModel::class, View, Count, Create, Update)
            allows(DraftCommandModel::class, View, Count, Create, Update)
            allows(EinsteinAtendimento::class, View, Count)
            allows(ExternalHealthInformation::class, View, Count)
            allows(HealthcareResourceModel::class, View, Count)
            allows(HealthcareTeamModel::class, View, Count)
            allows(HealthDeclaration::class, View, Count, Create, Update)
            allows(HealthFormQuestionAnswer::class, View, Count)
            allows(HealthInformation::class, Count)
            allows(HealthLogicRecord::class, View, Count)
            allows(HealthInformation::class, View, Count)
            allows(HealthMeasurementModel::class, View, Count, Create, Update)
            allows(HealthPlan::class, View, Count, Create, Update)
            allows(HealthPlanTask::class, View, Count)
            allows(HealthPlanTaskReferrals::class, View, Count)
            allows(HealthPlanTaskGroup::class, View, Count, Create, Update)
            allows(HealthPlanTaskGroupTemplate::class, View, Count, Create, Update)
            allows(HealthPlanTaskTemplate::class, View, Count, Create, Update)
            allows(HealthSpecialistResourceBundleModel::class, View, Count)
            allows(HLActionRecommendation::class, View, Count)
            allows(HLAdherence::class, View, Count)
            allows(InsurancePortabilityRequestModel::class, View, Count)
            allows(MemberModel::class, View, Count)
            allows(MemberProductPriceModel::class, View, Count)
            allows(OnboardingContractModel::class, View, Count)
            allows(OngoingCompanyDeal::class, View, Count)
            allows(PersonModel::class, View, Count, Update)
            allows(PersonAdditionalInfoModel::class, View, Count, Create, Update)
            allows(PersonCase::class, View, Count)
            allows(PersonClinicalAccount::class, View, Count)
            allows(PersonGracePeriod::class, View, Count, Create, Update)
            allows(PersonIdentityValidationModel::class, View, Count)
            allows(PersonInternalReference::class, View, Count, Create)
            allows(PersonOnboardingModel::class, View, Count)
            allows(PregnancyModel::class, View, Count, Create, Update)
            allows(ProductModel::class, View, Count)
            allows(ProductBundleModel::class, View, Count)
            allows(ProductPriceListingModel::class, View, Count)
            allows(PriceListingModel::class, View, Count)
            allows(ProviderModel::class, View, Count)
            allows(ProviderUnitModel::class, View, Count)
            allows(Risk::class, View, Count, Create, Update)
            allows(TertiaryIntentionTouchPoint::class, View, Count)
            allows(TestResultFileModel::class, View, Count, Create, Update)
            allows(Timeline::class, View, Count)
        }

        match("Alice Health Professionals", {
            subject is InternalHealthProfessionalSubject ||
                    subject is DigitalCareProfessionalSubject ||
                    subject is CareCoordinationSubject ||
                    subject is OnSiteProfessionalSubject
        }) {
            match("can view", { action is View }) {
                includes(viewQuestionnaire)
                match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
                match("any ExternalHealthInformation") { resource is ExternalHealthInformation }
                match("any PersonClinicalAccount") { resource is PersonClinicalAccount }
                match("any PersonGracePeriod") { resource is PersonGracePeriod }
                match("any MemberModel") { resource is MemberModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
                match("any Onboarding") { resource is PersonOnboardingModel }
                match("any AssistanceCare") { resource is AssistanceCare }
                match("any PersonTaskModel") { resource is PersonTaskModel }
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any AppointmentScheduleModel") { resource is AppointmentScheduleModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Product") { resource is ProductModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any ProductPriceListing") { resource is ProductPriceListingModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Provider") { resource is ProviderModel }
                match("any OutcomeConf") { resource is OutcomeConf }
                match("any OngoingCompanyDeal") { resource is OngoingCompanyDeal }
                match("any PersonIdentityValidationModel") { resource is PersonIdentityValidationModel }
                match("any HealthSpecialistResourceBundle") { resource is HealthSpecialistResourceBundleModel }
                match("any HealthcareResource") { resource is HealthcareResourceModel }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any Curiosity Note") { resource is CuriosityNoteModel }
                match("any Health Information") { resource is HealthInformation && resource !is Appointment && resource !is AssistanceCare }
                match("any PersonModel") { resource is PersonModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any ServiceScriptNode") { resource is ServiceScriptNode }
                match("any PersonAdditionalInfoModel") { resource is PersonAdditionalInfoModel }
                match("any Appointment") { resource is Appointment }
                match("any HealthPlanTaskTemplate") { resource is HealthPlanTaskTemplate }
                match("any HealthPlanTaskGroupTemplate") { resource is HealthPlanTaskGroupTemplate }
                match("any AppointmentTemplate") { resource is AppointmentTemplate }
                match("any HealthMeasurementTypeModel") { resource is HealthMeasurementTypeModel }
                match("any HealthMeasurementCategory") { resource is HealthMeasurementCategoryModel }
                match("any DraftCommand") { resource is DraftCommandModel }
            }
            match("can count", { action is Count }) {
                match("any Health Information") { resourceIs(HealthInformation::class) }
                match("any HealthSpecialistResourceBundle") { resource is HealthSpecialistResourceBundleModel }
                match("any HealthcareResource") { resource is HealthcareResourceModel }
            }
            allows(AppointmentProcedureExecuted::class, View, Count, Create, Delete)
            allows(SuggestedProcedure::class, View, Count)
            allows(ProviderUnitModel::class, View, Count)
            includes(viewAppointmentsAndExams)
            includes(viewSpecialistOpinion)
            includes(viewHealthPlanTaskReferrals)
            includes(upsertSpecialistOpinion)
            includes(viewStaffFileVault)
            includes(createStaffFileVault)
            includes(viewFileVault)
            includes(viewAppointmentMacro)
            includes(viewAssistanceSummary)
        }

        match("Navigator Ops", { subject is StaffSubject && subject.isNavigatorOps() }) {
            includes(viewCaseRecord)
        }

        match("Health Declaration Nurse", { subject is StaffSubject && subject.isHealthDeclarationNurse() }) {
            match("can view", { action is View }) {
                match("any MemberContract") { resource is MemberContractModel }
                match("any OnboardingContractModel") { resource is OnboardingContractModel }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any PersonGracePeriod") { resource is PersonGracePeriod }
                match("any Statement of Health Appointment") { resource is Appointment && resource.isStatementOfHealth }
            }
            match("can view", { action is View }) {
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
            }
        }

        match("Med and Enf Risk", { subject is StaffSubject && (subject.isMedRisk() || subject.isRiskNurse()) }) {
            match("can view", { action is View }) {
                match("any OnboardingContractModel") { resource is OnboardingContractModel }
            }

            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any PersonGracePeriod") { resource is PersonGracePeriod }
                match("any Statement of Health Appointment") { resource is Appointment && resource.isStatementOfHealth }
            }
        }

        match("AssistanceCare Professional", { subject is StaffSubject && subject.isAssistanceCare() }) {
            match("can update and create", { action is Update || action is Create }) {
                match("AssistanceCare Appointment") {
                    (resource is Appointment && (resource.isAssistanceCare || resource.isAnnotation || resource.isAnnotationHealthCommunity || resource.isOnSite)) || resource is AssistanceCare
                }
            }
        }

        match(
            "Physician Health Professionals",
            { subject is OnSiteProfessionalSubject || (subject is HealthProfessionalSubject && subject.isPhysician()) }) {
            match("can update and create", { action is Update || action is Create }) {
                match("OnSite Appointment") {
                    (resource is Appointment && (resource.isOnSite || resource.isDefault)) || resource is AssistanceCare
                }
            }
        }

        match("HealthCareTeamNurse Professional", { subject is StaffSubject && subject.isImmersionTeamNurse() }) {
            match("can update and create", { action is Update || action is Create }) {
                match("HealthPlanCare Appointment") {
                    (resource is Appointment && (resource.isHealthPlanCare)) || resource is AssistanceCare
                }
            }
        }

        match("Immersion Health Professional", { subject is StaffSubject && subject.isImmersionTeam() }) {
            match("can update and create", { action is Update || action is Create }) {
                match("Immersion Appointment") {
                    resource is Appointment && resource.isImmersion
                }
            }
        }

        match("CX Ops", { subject is StaffSubject && (subject.isCXOps()) }) {
            match("can view", { action is View }) {
                match("any PersonModel") { resource is PersonModel }
                match("any PersonModel Clinical Account") { resource is PersonClinicalAccount }
                match("any PersonModel Internal Reference") { resource is PersonInternalReference }
                match("any MemberModel") { resource is MemberModel }
                match("any MemberModel Product Price") { resource is MemberProductPriceModel }
                match("any PersonAdditionalInfoModel") { resource is PersonAdditionalInfoModel }
                match("any Onboarding") { resource is PersonOnboardingModel }
            }
            match("can count", { action is Count }) {
                match("any PersonModel Health Event") { resourceIs(PersonHealthEvent::class) }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any CX PersonModel Health Event") { resource is PersonHealthEvent && resource.isCxOpsCategory() }
            }
        }

        match(
            "Insurance Ops - Health Institution Ops",
            { subject is StaffSubject && subject.isInsuranceOpsHealthInstitutionOps() }) {
            includes(viewHealthPlan)
            includes(viewAppointmentsAndExams)
            includes(viewEhrClinicalBackground)
            includes(viewMemberProfile)
        }

        match(
            "HealthOps Lead",
            {
                subject is StaffSubject && subject.isHealthOpsLead()
            }) {
            includes(viewAndUpdateHealthPlan)
            includes(viewAppointmentsAndExams)
            includes(viewEhrClinicalBackground)
            includes(viewMemberProfile)
            includes(viewCaseRecord)
            includes(viewClinicalOutcomeRecordInListOfHealthDemandDetails)
            match("can view", { action is View }) {
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
                match("any AppointmentProcedureExecuted") { resourceIs(AppointmentProcedureExecuted::class) }
                match("any HealthcareResource") { resourceIs(HealthcareResourceModel::class) }
                match("any HealthInformation") { resourceIs(HealthInformation::class) }
                match("any HealthSpecialistResourceBundle") { resourceIs(HealthSpecialistResourceBundleModel::class) }
            }
        }

        match("Product and Tech", { subject is StaffSubject && subject.isProductTech() }) {
            includes(viewQuestionnaire)
            match("can view", { action is View }) {
                match("any External Health Information of Test PersonModel") {
                    testPersonResource(
                        ExternalHealthInformation::class
                    )
                }
                match("any ChannelTag") { resource is ChannelTag }
                match("MemberModel of Test PersonModel") { testPersonResource(MemberModel::class) }
                match("Test PersonModel Onboarding") { testPersonResource(PersonOnboardingModel::class) }
                match("any HaocDocument") { resource is HaocDocument }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("Test PersonModel") { testPerson() }
                match("Test MemberModel") { testPersonResource(MemberModel::class) }
                match("any Test Code") { resource is TestCodeModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any TestPreparationModel") { resource is TestPreparationModel }
                match("Curiosity Note of Test PersonModel") { testPersonResource(CuriosityNoteModel::class) }
                match("PersonModel Clinical Account of Test PersonModel") { testPersonResource(PersonClinicalAccount::class) }
                match("Health Information of Test PersonModel") { testPersonResource(HealthInformation::class) }
                match("PersonModel Internal Reference of Test PersonModel") { testPersonResource(PersonInternalReference::class) }
                match("any MedicineModel") { resource is MedicineModel }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any ProviderUnitTestCode") { resource is ProviderUnitTestCodeModel }
                match("any ServiceScriptNode") { resource is ServiceScriptNode }
                match("Test PersonModel PersonAdditionalInfoModel") { testPersonResource(PersonAdditionalInfoModel::class) }
                match("Test PersonModel HealthDeclaration") { testPersonResource(HealthDeclaration::class) }
                match("Any HealthPlanTaskTemplate") { resource is HealthPlanTaskTemplate }
                match("Any HealthPlanTaskGroupTemplate") { resource is HealthPlanTaskGroupTemplate }
                match("any AppointmentTemplate") { resource is AppointmentTemplate }
                match("Any HealthMeasurementTypeModel") { resource is HealthMeasurementTypeModel }
                match("Any HealthMeasurementCategory") { resource is HealthMeasurementCategoryModel }
            }
            match("can count", { action is Count }) {
                match("her Health Information") { herOwn(HealthInformation::class) }
                match("any PersonModel Health Event") { resourceIs(PersonHealthEvent::class) }
            }
            match("can delete", { action is Delete }) {
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
            }
        }

        match("De-identified HI viewer", { subject is StaffSubject && subject.isDeIdentifiedHiViewer() }) {
            match("can view", { action is View }) {
                match("any Health Information") { resource is HealthInformation }
            }
        }

        match("StaffSubject", { subject is StaffSubject }) {
            includes(viewAnyMemberPhoto)
            includes(viewQuestionnaire)
            match("can view", { action is View }) {
                match("any MedicineModel") { resource is MedicineModel }
                match("any Prescription Sentence") { resource is PrescriptionSentenceModel }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any Channel Macro") { resource is ChannelMacro }
                match("any ChannelTag") { resource is ChannelTag }
                match("any ConsolidatedAccreditedNetwork") { resource is ConsolidatedAccreditedNetwork }
                match("any Product") { resource is ProductModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any ProductPriceListing") { resource is ProductPriceListingModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any ProviderUnitTestCode") { resource is ProviderUnitTestCodeModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any MedicalSpecialty") { resource is MedicalSpecialtyModel }
                match("any Healthcare Team") { resource is HealthcareTeamModel }
                match("any HealthcareAdditionalTeam") { resource is HealthcareAdditionalTeam }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any Test Code") { resource is TestCodeModel }
                match("any ServiceScriptNode") { resource is ServiceScriptNode }
                match("any ServiceScriptRelationship") { resource is ServiceScriptRelationship }
                match("any ServiceScriptAction") { resource is ServiceScriptAction }
                match("any BudNode") { resource is BudNode }
                match("any Protocol") { resource is Protocol }
                match("any TestCodePackage") { resource is TestCodePackageModel }
                match("any TestCode") { resource is TestCodeModel }
                match("any HealthCondition") { resource is HealthCondition }
                match("any HealthConditionTemplate") { resource is HealthConditionTemplate }
                match("any HealthConditionAxis") { resource is HealthConditionAxis }
                match("any HealthConditionRelated") { resource is HealthConditionRelated }
                match("any HealthMeasurementTypeModel") { resource is HealthMeasurementTypeModel }
                match("any HealthMeasurementCategory") { resource is HealthMeasurementCategoryModel }
                match("any AppointmentScheduleOptionModel") { resource is AppointmentScheduleOptionModel }
                match("any AppointmentScheduleEventTypeModel") { resource is AppointmentScheduleEventTypeModel }
                match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
                match("any TrackPersonABModel") { resource is TrackPersonABModel }
                match("any RefundCounterReferralModel") { resource is RefundCounterReferralModel }
                match("any TotvsGuia") { resource is TotvsGuiaModel }
                match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
                match("any CassiSpecialist") { resource is CassiSpecialistModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
            }
            match("can create", { action is Create }) {
                match("any AppointmentAutofillHistory") {
                    resource is AppointmentAutofillHistory
                }
            }
            match("can update", { action is Update }) {
                match("her own Appointment") {
                    resource is Appointment && resource.staffId == (subject as StaffSubject).id
                }
            }
            allows(ProviderUnitModel::class, View, Count)
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("herself") { herself() }
                match("any ServiceScriptNavigation") { resource is ServiceScriptNavigation }
                match("any ServiceScriptNavigationGroup") { resource is ServiceScriptNavigationGroup }
                match("any ServiceScriptExecution") { resource is ServiceScriptExecution }
                match("her own StaffSignTokenModel") { herOwn(StaffSignTokenModel::class) }
            }

            includes(viewBeneficiary)
        }

        match("Health Community Specialist", { subject is HealthCommunitySpecialistModel || subject is EmailDomain }) {
            match("can view", { action is View }) {
                match("any ConsolidatedAccreditedNetwork") { resource is ConsolidatedAccreditedNetwork }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any ExternalHealthInformation") { resource is ExternalHealthInformation }
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any HealthInformation") { resource is HealthInformation }
                match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
                match("any Person") { resource is PersonModel }
                match("any Person Clinical Account") { resource is PersonClinicalAccount }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any MedicalSpecialty") { resource is MedicalSpecialtyModel }
                match("any Member") { resource is MemberModel }
                match("any Member Product Price") { resource is MemberProductPriceModel }
                match("any Product") { resource is ProductModel }
                match("any Provider") { resource is ProviderModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any ProductPriceListing") { resource is ProductPriceListingModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any CuriosityNote") { resource is CuriosityNoteModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any PersonAdditionalInfoModel") { resource is PersonAdditionalInfoModel }
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
                match("any HealthCondition") { resource is HealthCondition }
                match("any HealthConditionTemplate") { resource is HealthConditionTemplate }
                match("any HealthConditionAxis") { resource is HealthConditionAxis }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any ProviderUnitTestCode") { resource is ProviderUnitTestCodeModel }
                match("any TestCode") { resource is TestCodeModel }
                match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
                match("any Appointment") { resource is Appointment }
                match("any Timeline") { resource is Timeline }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("herself") { herself() }
                match("any Counter Referral") { resource is CounterReferral }
                match("any Counter Referral Relevance") { resource is CounterReferralRelevance }
                match("any HealthPlan Referral") {
                    resource is HealthPlanTask &&
                            resource.type == HealthPlanTaskType.REFERRAL
                }
                match("any HealthCommunityUnreferencedAccessModel") { resourceIs(HealthCommunityUnreferencedAccessModel::class) }
            }
            match("can count", { action is Count }) {
                match("any Health Information") { resourceIs(HealthInformation::class) }
            }
            allows(ProviderUnitModel::class, View, Count)
            includes(viewBeneficiary)
            includes(viewSpecialistOpinion)
            includes(viewHealthPlanTaskReferrals)
            includes(upsertSpecialistOpinion)
            includes(viewStaffFileVault)
            includes(createStaffFileVault)
            includes(viewFileVault)
        }

        match("Health Specialist", { subject is StaffSubject && subject.isCommunity() }) {
            match("can view", { action is View }) {
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any ExternalHealthInformation") { resource is ExternalHealthInformation }
                match("any HealthDeclaration") { resource is HealthDeclaration }
                match("any HealthInformation") { resource is HealthInformation }
                match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
                match("any Person") { resource is PersonModel }
                match("any Person Clinical Account") { resource is PersonClinicalAccount }
                match("any Appointment") { resource is Appointment }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any MemberModel") { resource is MemberModel }
                match("any MemberModel Product Price") { resource is MemberProductPriceModel }
                match("any MedicalSpecialty") { resource is MedicalSpecialtyModel }
                match("any Provider") { resource is ProviderModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any Product") { resource is ProductModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any ProductPriceListing") { resource is ProductPriceListingModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any CuriosityNote") { resource is CuriosityNoteModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any PersonAdditionalInfoModel") { resource is PersonAdditionalInfoModel }
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
                match("any HealthCondition") { resource is HealthCondition }
                match("any HealthConditionTemplate") { resource is HealthConditionTemplate }
                match("any HealthConditionAxis") { resource is HealthConditionAxis }
                match("any HealthConditionRelated") { resource is HealthConditionRelated }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any ProviderUnitTestCode") { resource is ProviderUnitTestCodeModel }
                match("any TestCode") { resource is TestCodeModel }
                match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("herself") { herself() }
                match("any Counter Referral") { resource is CounterReferral }
                match("any Counter Referral Relevance") { resource is CounterReferralRelevance }
                match("any HealthPlan Referral") {
                    resource is HealthPlanTask &&
                            resource.type == HealthPlanTaskType.REFERRAL
                }
                match("any HealthCommunityUnreferencedAccessModel") { resourceIs(HealthCommunityUnreferencedAccessModel::class) }
            }
            match("can count", { action is Count }) {
                match("any Health Information") { resourceIs(HealthInformation::class) }
            }

            includes(viewBeneficiary)
            includes(viewSpecialistOpinion)
            includes(viewHealthPlanTaskReferrals)
            includes(upsertSpecialistOpinion)
            includes(viewStaffFileVault)
            includes(createStaffFileVault)
            includes(viewFileVault)
        }

        match("All StaffModel", { subject is StaffModel }) {
            includes(viewBeneficiary)
        }

        match(
            "Insurance Ops - Community Success",
            { subject is StaffSubject && subject.isInsuranceOpsCommunitySuccess() }) {
            includes(viewMemberProfile)
            includes(viewHealthPlan)
            includes(viewAppointmentsAndExams)
            includes(viewEhrClinicalBackground)

            match("can view", { action is View }) {
                match("any InsurancePortabilityRequest") { resource is InsurancePortabilityRequestModel }
            }
        }

        match(
            "Health - Técnica(o) de Enfermagem Casa Alice",
            { subject is StaffSubject && subject.isTechniqueNurse() }) {
            includes(viewAppointmentsAndExams)
            includes(viewAndUpdateHealthPlan)
            includes(viewAndUpdateEhrClinicalBackground)
            includes(viewAndUpdateMemberProfile)
        }

        match("Tertiary Intention Touch Point", { subject is StaffSubject && subject.isAliceHealthProfessional() }) {
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any TertiaryIntentionTouchPoint") { resource is TertiaryIntentionTouchPoint }
            }
        }

        match("Product Tech - Health", { subject is StaffSubject && subject.isProductTechHealth() }) {
            match("any AppointmentProcedureExecuted") { resourceIs(AppointmentProcedureExecuted::class) }
            match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
            match("any Curiosity Note") { resourceIs(CuriosityNoteModel::class) }
            match("any HealthcareResource") { resourceIs(HealthcareResourceModel::class) }
            match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
            match("any HealthInformation") { resourceIs(HealthInformation::class) }
            match("any HealthSpecialistResourceBundle") { resourceIs(HealthSpecialistResourceBundleModel::class) }
            match("any MemberModel") { resourceIs(MemberModel::class) }
            match("any PersonModel") { resourceIs(PersonModel::class) }
            match("any PersonAdditionalInfoModel") { resourceIs(PersonAdditionalInfoModel::class) }
            match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
            match("any PersonGracePeriod") { resourceIs(PersonGracePeriod::class) }
            match("any PersonInternalReference") { resourceIs(PersonInternalReference::class) }
            match("any StaffModel") { resourceIs(StaffModel::class) }
            includes(viewAppointmentMacro)
        }

        match("Health Audit Subject", { subject is HealthAuditSubject }) {
            match("can view", { action is View || action is Count }) {
                match("any ExternalHealthInformation") { resourceIs(ExternalHealthInformation::class) }
                match("any HealthInformation") { resourceIs(HealthInformation::class) }
                match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
                match("any MemberModel") { resourceIs(MemberModel::class) }
                match("any MemberProductPriceModel") { resourceIs(MemberProductPriceModel::class) }
                match("any Onboarding") { resourceIs(PersonOnboardingModel::class) }
                match("any PersonTaskModel") { resourceIs(PersonTaskModel::class) }
                match("any InsurancePortabilityRequest") { resourceIs(InsurancePortabilityRequestModel::class) }
                match("any Curiosity Note") { resourceIs(CuriosityNoteModel::class) }
                match("any PersonModel") { resourceIs(PersonModel::class) }
                match("any PersonInternalReference") { resourceIs(PersonInternalReference::class) }
                match("any PersonAdditionalInfoModel") { resourceIs(PersonAdditionalInfoModel::class) }
                match("any HealthPlanTaskTemplate") { resourceIs(HealthPlanTaskTemplate::class) }
                match("any HealthPlanTaskGroupTemplate") { resourceIs(HealthPlanTaskGroupTemplate::class) }
                match("any AppointmentTemplate") { resourceIs(AppointmentTemplate::class) }
                match("any HealthMeasurementTypeModel") { resourceIs(HealthMeasurementTypeModel::class) }
                match("any HealthMeasurementCategory") { resourceIs(HealthMeasurementCategoryModel::class) }
                match("any DraftCommand") { resourceIs(DraftCommandModel::class) }
            }
        }

        match("Health Ops Subject", { subject is HealthOpsSubject }) {
            match("can view", { action is View || action is Count }) {
                match("any AppointmentProcedureExecuted") { resourceIs(AppointmentProcedureExecuted::class) }
                match("any AppointmentTemplate") { resourceIs(AppointmentTemplate::class) }
                match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
                match("any Curiosity Note") { resourceIs(CuriosityNoteModel::class) }
                match("any DraftCommand") { resourceIs(DraftCommandModel::class) }
                match("any ExternalHealthInformation") { resourceIs(ExternalHealthInformation::class) }
                match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
                match("any HealthInformation") { resourceIs(HealthInformation::class) }
                match("any HealthMeasurementCategory") { resourceIs(HealthMeasurementCategoryModel::class) }
                match("any HealthMeasurementTypeModel") { resourceIs(HealthMeasurementTypeModel::class) }
                match("any HealthPlanTaskGroupTemplate") { resourceIs(HealthPlanTaskGroupTemplate::class) }
                match("any HealthPlanTaskTemplate") { resourceIs(HealthPlanTaskTemplate::class) }
                match("any HealthSpecialistResourceBundle") { resourceIs(HealthSpecialistResourceBundleModel::class) }
                match("any HealthcareResource") { resourceIs(HealthcareResourceModel::class) }
                match("any InsurancePortabilityRequest") { resourceIs(InsurancePortabilityRequestModel::class) }
                match("any MemberModel") { resourceIs(MemberModel::class) }
                match("any MemberProductPriceModel") { resourceIs(MemberProductPriceModel::class) }
                match("any Onboarding") { resourceIs(PersonOnboardingModel::class) }
                match("any PersonModel") { resourceIs(PersonModel::class) }
                match("any PersonAdditionalInfoModel") { resourceIs(PersonAdditionalInfoModel::class) }
                match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
                match("any PersonGracePeriod") { resourceIs(PersonGracePeriod::class) }
                match("any PersonInternalReference") { resourceIs(PersonInternalReference::class) }
                match("any PersonTaskModel") { resourceIs(PersonTaskModel::class) }
                match("any StaffModel") { resourceIs(StaffModel::class) }
            }
        }

        match(
            "Alice Health Professional or Navigator",
            { subject is StaffModel && subject.isAliceHealthProfessionalOrNavigator() }) {
            includes(viewAndCreateTestResultFeedback)
        }
    }

    match("Backfills at ehr-domain-service", { rootService.name == EHR_DOMAIN_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(CounterReferral::class, View, Update)
            allows(HealthCommunitySpecialistModel::class, View, Update, Create)
            allows(PersonInternalReference::class, View, Update, Create)
            allows(PersonModel::class, View)
            allows(HealthCommunityUnreferencedAccessModel::class, View, Update)
            allows(StaffModel::class, View)
        }
    }

    match("at ehr-person-health-event-notifiers", { rootService.name == EHR_DOMAIN_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any ExecutionGroup") { resource is ExecutionGroupModel }
                match("any ExecIndicatorAuthorizer") { resource is ExecIndicatorAuthorizerModel }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
            }
            match("can view and update", { action is Update || action is View }) {
                match("any HealthPlanTask") { resource is HealthPlanTask }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
            }
            match("can view and update and create", { action is Update || action is View || action is Create }) {
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
            }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any PersonHealthEvent") { resourceIs(PersonHealthEvent::class) }
        }
        match("can view", { action is View }) {
            match("any DbLaboratoryTestResult") { resourceIs(DbLaboratoryTestResult::class) }
            match("any DbLaboratoryTestResultProcess") { resourceIs(DbLaboratoryTestResultProcess::class) }
            match("any FleuryTestResultFile") { resourceIs(FleuryTestResultFile::class) }
            match("any Health Plan") { resourceIs(HealthPlan::class) }
            match("any MedicalSpecialty") { resourceIs(MedicalSpecialtyModel::class) }
            match("any TertiaryIntention") { resourceIs(TertiaryIntentionTouchPoint::class) }
        }
    }

    match("at ehr-background-processes", { rootService.name == EHR_DOMAIN_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view and create", { action is View || action is Create }) {
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
            }

            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any ClinicalBackground") { resource is ClinicalBackground && (subject as Unauthenticated).key == resource.personId.toString() }
                match("any HealthMeasurementModel") { resource is HealthMeasurementModel && (subject as Unauthenticated).key == resource.personId.toString() }
            }
        }


        describe("notifications") {
            includes(createPersonInternalReference)
            includes(disassociatePersonFromHealthcareTeam)
            includes(addStaffOnPersonClinicalAccount)
            includes(updateMemberHealthMetric)
            includes(updateHealthPlanChannels)
            includes(archivePersonChannels)
            includes(calendlyScheduleOrCancel)
            includes(sendReferralReferenceLetter)
            includes(sendToAutomaticTaskEngine)
        }

        describe("recurring") {
            includes(completeAppointments)
            includes(sendReferralReferenceLetter)
            includes(sendOverdueTasks)
        }

        includes(viewBeneficiary)
        includes(updatePersonHealthCareTeam)
        includes(completedHealthForm)
        includes(publishHealthFormToHealthLogic)
        includes(createHealthPlanFromHealthLogics)
        includes(autoCompleteHealthPlanTask)
        includes(archiveChannelsWhenMemberCancelled)
        includes(createCaseRecordForHealthDeclaration)
        includes(backfillHealthFormQuestionAnswer)
        includes(backfillStaffCouncilNumber)
        includes(backfillHealthCondition)
        includes(backfillHealthMeasurements)
        includes(clinicalOutcomeRecordCreated)
        includes(assistanceSummaryUpsert)
        includes(viewRiskCalculationConf)

        describe("recreate-person-clinical-account") {
            match("can view or create", { action is View || action is Create }) {
                match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
            }
            match("can view", { action is View }) {
                match("any Channel") { resourceIs(Channel::class) }
                match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
                match("any Appointment Procedure Executed") { resourceIs(AppointmentProcedureExecuted::class) }
                match("any Counter Referral") { resourceIs(CounterReferral::class) }
                match("any Health Community Unreferenced Access") { resourceIs(HealthCommunityUnreferencedAccessModel::class) }
            }
        }

        describe("drafts publishing") {
            match("can view or update", { action is View || action is Update }) {
                match("any Draft Command") { resource is DraftCommandModel }
                match("any PregnancyModel") { resource is PregnancyModel }
                match("any Clinical Background") { resource is ClinicalBackground }
                match("any Health Measurement") { resource is HealthMeasurementModel }
            }
            match("can create", { action is Create }) {
                match("any PregnancyModel") { resource is PregnancyModel }
                match("any Clinical Background") { resource is ClinicalBackground }
                match("any Health Measurement") { resource is HealthMeasurementModel }
                match("any Counter Referral") { resourceIs(CounterReferral::class) }
            }
            allows(HealthSpecialistResourceBundleModel::class, View)
            allows(HealthcareResourceModel::class, View)
        }


        describe("tertiary intention touch point auto creation and update") {
            allows(TotvsGuiaModel::class, View)
            allows(MvAuthorizedProcedureModel::class, View)
            allows(ExecIndicatorAuthorizerModel::class, View)
            allows(TertiaryIntentionTouchPoint::class, View, Update, Create)
            allows(HospitalizationInfoModel::class, View)
            allows(HealthcareResourceModel::class, View)
        }
        allows(ProviderUnitModel::class, View, Count)
        allows(ConsolidatedRewardsModel::class, View, Update, Create)
        allows(HealthFormAnswerGroup::class, View)
    }

}
