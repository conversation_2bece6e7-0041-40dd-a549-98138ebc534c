package br.com.alice.ehr.consumers

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.common.asyncLayer
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.SCHEDULE
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DONE
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthPlanTaskType.EMERGENCY
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.HealthPlanTaskType.REFERRAL
import br.com.alice.data.layer.models.HealthPlanTaskType.TEST_REQUEST
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.TaskSourceType
import br.com.alice.ehr.event.EmergencyExecutedEvent
import br.com.alice.ehr.event.TestRequestExecutedEvent
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.data.layer.models.NotOccurredReason
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class AutoCompleteTaskConsumer(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val counterReferralService: CounterReferralService,
) : Consumer() {

    suspend fun handleTestRequest(event: TestRequestExecutedEvent): Result<Any, Throwable> = asyncLayer {
        genericHandle(
            "can_auto_complete_test_request",
            "handleTestRequest",
            DONE,
            event.payload.taskId,
            event.payload.personId,
            TEST_REQUEST
        )
    }

    suspend fun handlePs(event: EmergencyExecutedEvent): Result<Any, Throwable> = asyncLayer {
        genericHandle(
            "can_auto_complete_ps",
            "handlePs",
            DONE,
            event.payload.taskId,
            event.payload.personId,
            EMERGENCY
        )
    }

    suspend fun handleScheduleCreatedEvent(event: AppointmentScheduleCreatedEvent): Result<Any, Throwable> =
        asyncLayer {
            handleStaffReferral(
                "handleScheduleCreatedEvent",
                DONE,
                event.payload.appointmentSchedule.healthPlanTaskId,
                event.payload.appointmentSchedule.staffId,
                event.payload.person.personId,
            )
        }

    suspend fun handleScheduleCanceledEvent(event: AppointmentScheduleCancelledEvent): Result<Any, Throwable> =
        asyncLayer {
            genericHandle(
                "can_auto_complete_schedule",
                "handleScheduleCanceledEvent",
                ACTIVE,
                event.payload.healthPlanTaskId,
                event.payload.personId,
                REFERRAL,
            )
        }

    suspend fun handleAppointmentCompletedEvent(event: AppointmentCompletedEvent): Result<Any, Throwable> = asyncLayer {
        if (event.payload.appointment.type == AppointmentType.COUNTER_REFERRAL)
            return@asyncLayer false.success()

        coResultOf {
            event.payload.appointment.referencedLinks
                .pmap {
                    when (it.model) {
                        HEALTH_PLAN_TASK -> autoCompleteTask(
                            taskId = it.id,
                            status = DONE,
                            taskType = null
                        )

                        SCHEDULE -> autoCompleteFromSchedule(it.id)
                        else -> true
                    }
                }
        }
    }

    suspend fun handlePersonHealthEventUpdate(event: PersonHealthEventUpdatedEvent) = withSubscribersEnvironment {
        coResultOf {
            logger.info(
                "AutoCompleteTaskConsumer#handlePersonHealthEventUpdate: received event",
                "person_health_event_id" to event.payload.personHealthEvent.id
            )
            val personHealthEvent = event.payload.personHealthEvent
            if (personHealthEvent.referencedModelClass == PersonHealthEventReferenceModel.PRESCRIPTION &&
                personHealthEvent.staffId != null &&
                (
                        personHealthEvent.status == PersonHealthEventStatus.FINISHED ||
                                personHealthEvent.status == PersonHealthEventStatus.CANCELLED
                        )
            ) {
                autoCompleteTask(
                    personHealthEvent.referencedModelId!!.toUUID(),
                    DONE,
                    PRESCRIPTION
                )
            }

            true
        }
    }

    suspend fun handleReferralEvent(event: CounterReferralCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val task = completeFromCounterReferral(event.payload.counterReferral)
                ?: return@withSubscribersEnvironment false.success()

            autoCompleteTask(
                taskId = task.id,
                status = DONE,
                taskType = task.type,
                healthPlanTask = task
            )
        }

    private suspend fun handleStaffReferral(
        methodName: String,
        status: HealthPlanTaskStatus,
        taskId: UUID?,
        staffId: UUID?,
        personId: PersonId,
    ): Result<Any, Throwable> {
        return if (staffId == null)
            genericHandle(
                "can_auto_complete_schedule",
                methodName,
                status,
                taskId,
                personId,
                REFERRAL
            )
        else {
            logger.info(
                "AutoCompleteTaskConsumer.$methodName REFERRAL not updated: already have a Staff",
                "taskId" to taskId,
                "staffId" to staffId,
            )

            false.success()
        }
    }

    private suspend fun genericHandle(
        flagName: String,
        methodName: String,
        status: HealthPlanTaskStatus,
        taskId: UUID?,
        personId: PersonId,
        taskType: HealthPlanTaskType
    ): Result<Any, Throwable> {
        logger.info(
            "AutoCompleteTaskConsumer.$methodName: Received event",
            "task_id" to taskId,
            "person_id" to personId,
            "task_type" to taskType,
            "flag_name" to flagName
        )

        return taskId?.let {
            autoCompleteTask(it, status, taskType)
        } ?: run {
            logger.info("AutoCompleteTaskConsumer.$methodName: id null")

            false.success()
        }
    }

    private suspend fun autoCompleteTask(
        taskId: UUID,
        status: HealthPlanTaskStatus,
        taskType: HealthPlanTaskType?,
        healthPlanTask: HealthPlanTask? = null
    ) =
        withSubscribersEnvironment {
            val task = healthPlanTask ?: healthPlanTaskService.get(taskId).fold(
                { it },
                {
                    return@withSubscribersEnvironment if (it is NotFoundException) true.success()
                    else {
                        logger.error(
                            "AutoCompleteTaskConsumer.autoCompleteTask: error to process",
                            "task_id" to taskId,
                            it
                        )
                        it.failure()
                    }
                }
            )

            if (taskType != null && task.type != taskType) {
                logger.info(
                    "AutoCompleteTaskConsumer.autoCompleteTask: discard task type not equals",
                    "task_id" to taskId,
                    "original_type" to task.type,
                    "processed_type" to taskType
                )
                return@withSubscribersEnvironment task.success()
            }

            if (status == DONE && task.status == status && task.finishedBy?.source == TaskSourceType.SYSTEM) {
                logger.info(
                    "AutoCompleteTaskConsumer.autoCompleteTask: discard task already DONE by System",
                    "task_id" to taskId,
                    "person_id" to task.personId,
                    "task_type" to task.type,
                )
                return@withSubscribersEnvironment task.success()
            }

            // active cases
            if (task.status == status) {
                logger.info(
                    "AutoCompleteTaskConsumer.autoCompleteTask: discard task already $status by System",
                    "task_id" to taskId,
                    "person_id" to task.personId,
                    "task_type" to task.type,
                )
                return@withSubscribersEnvironment task.success()
            }

            if (status != DONE && status != ACTIVE) {
                logger.warn(
                    "AutoCompleteTaskConsumer.autoCompleteTask: invalid task status to update",
                    "task_id" to taskId,
                    "person_id" to task.personId,
                    "task_type" to task.type,
                    "status" to status
                )

                return@withSubscribersEnvironment task.success()
            }

            return@withSubscribersEnvironment if (status == DONE) {
                healthPlanTaskService.completeTask(task)
            } else {
                healthPlanTaskService.activateTask(task)
            }.then {
                logger.info(
                    "AutoCompleteTaskConsumer.autoCompleteTask: task updated to $status",
                    "task_id" to taskId,
                    "person_id" to task.personId,
                    "task_type" to task.type
                )
            }.thenError {
                logger.error(
                    "AutoCompleteTaskConsumer.autoCompleteTask: error updating task to $status or notifying the event",
                    "task_id" to taskId,
                    "person_id" to task.personId,
                    "task_type" to task.type
                )
            }
        }

    private suspend fun autoCompleteFromSchedule(id: UUID) =
        withSubscribersEnvironment {
            appointmentScheduleService.get(id).map {
                if (it.healthPlanTaskId != null) {
                    autoCompleteTask(it.healthPlanTaskId!!, DONE, null)
                }
            }
        }

    private suspend fun completeFromCounterReferral(counterReferral: CounterReferral): HealthPlanTask? =
        counterReferral.referralId?.let {
            val task = healthPlanTaskService.get(it).getOrNullIfNotFound() ?: return@let null

            when {
                task.isReferral() -> completeReferralFromCounterReferral(counterReferral, task.specialize())
                task.isFollowUpRequest() -> task
                else -> null
            }
        }

    private suspend fun completeReferralFromCounterReferral(counterReferral: CounterReferral, referral: Referral): HealthPlanTask? {
        logger.info(
            "AutoCompleteTaskConsumer.autoCompleteTask: checking referral with multiple sessions",
            "task_id" to referral.id,
            "sessions_quantity" to referral.sessionsQuantity,
            "follow_up_max_quantity" to referral.followUpMaxQuantity,
            "next_step_specialist_info" to counterReferral.nextStepSpecialistInfo,
        )

        return if (canComplete(referral))
            referral.generalize()
        else {
            logger.info(
                "AutoCompleteTaskConsumer.autoCompleteTask: referral with none or one session available, go on completing the task",
                "task_id" to referral.id,
                "sessions_quantity" to referral.sessionsQuantity,
                "follow_up_max_quantity" to referral.followUpMaxQuantity,
                "next_step_specialist_info" to counterReferral.nextStepSpecialistInfo,
            )
            null
        }
    }

    private suspend fun canComplete(referral: Referral): Boolean =
        if (referral.getSessions() > 1) {
            val counterrefCompleted =
                counterReferralService.listByReferral(referral.id).getOrElse { emptyList() }

            // Filtra os CounterReferral que são NO_SHOW ou que não ocorreram
            val validCounterReferrals = counterrefCompleted.filter { counterRef ->
                counterRef.notOccurredReason != NotOccurredReason.NO_SHOW
            }

            validCounterReferrals.size >= referral.getSessions()
        } else true
}
