package br.com.alice.exec.indicator.routes


import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.exec.indicator.controllers.BackFillController
import br.com.alice.exec.indicator.controllers.GlossAuthorizationInfoController
import br.com.alice.exec.indicator.controllers.GuiaWithProceduresBackfillController
import br.com.alice.exec.indicator.controllers.HealthSpecialistResourceBundleBackfillController
import br.com.alice.exec.indicator.controllers.MvAuthorizedProcedureExecutorController
import br.com.alice.exec.indicator.controllers.TotvsGuiaBackFillController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route


fun Routing.backFillRoutes() {
    route("/backfill") {
        val guiaWithProceduresBackFillController by inject<GuiaWithProceduresBackfillController>()
        post("/guia_with_procedures") { coHandler(guiaWithProceduresBackFillController::doBackfill) }

        val backFillController by inject<BackFillController>()
        post("/find_un_sync_procedures_by_range") { coHandler(backFillController::backFillUnSyncHealthEventsByRange) }
        post("/authorize_procedures") { coHandler(backFillController::backFillAuthorizeProcedures) }
        post("/resource_group_associations") { coHandler(backFillController::backfillResourceGroupAssociations) }
        post("/tuss_code_associations") { coHandler(backFillController::backfillTussCodeAssociations) }
        post("/sync_uncoordinated_procedures") { coHandler(backFillController::syncUncoordinatedProcedures) }
        post("/execute_pre_executed_procedures") { coHandler(backFillController::backfillExecuteOldPreExecutedProcedures) }
        post("/populate_status_and_procedures_id") { coHandler(backFillController::backfillGuiaWithProceduresFillStatusAddIdProcedure) }
        post("/remove_duplicated_procedures") { coHandler(backFillController::backfillRemoveDuplicatedPendingProceduresFromGuia) }
        post("/backfill_bundle_nullable_code") { coHandler(backFillController::backfillNullCodesOfBundles) }
        post("/populate_authorized_at") { coHandler(backFillController::populateAuthorizedAt) }
        post("/populate_executed_at") { coHandler(backFillController::populateExecutedAt) }
        post("/clean_resources_composition_hash") { coHandler(backFillController::cleanCompositionHashOfHealthcareResources) }
        post("/populate_bundle_primary_tuss_resource_id") { coHandler(backFillController::populateBundlePrimaryTussResourceId) }
        post("/set_end_date_of_tuss_procedure_specialty") { coHandler(backFillController::setEndDateOfTussProcedureSpecialty) }
        post("/reset_end_date_of_tuss_procedure_specialty") { coHandler(backFillController::resetEndDateOfTussProcedureSpecialty) }
        post("/reset_health_specialist_resource_bundle_id_of_tuss_procedure_specialty") { coHandler(backFillController::resetHealthSpecialistResourceBundleIdOfTussProcedureSpecialty) }

        val totvsGuiaBackFill by inject<TotvsGuiaBackFillController>()
        post("/remove_execution_group") { coHandler(totvsGuiaBackFill::removeExecutionGroup) }
        post("/populate_new_fields_on_totvs_guia") { coHandler(totvsGuiaBackFill::populateNewFieldsOnTotvsGuia) }
        post("/populate_person_id") { coHandler(totvsGuiaBackFill::populatePersonIdField) }
        post("/populate_requested_at") { coHandler(totvsGuiaBackFill::populateRequestedAt) }
        post("/populate_cnpj") { coHandler(totvsGuiaBackFill::populateCnpj) }

        val glossAuthorizationInfoController by inject<GlossAuthorizationInfoController>()
        post("/gloss") { asyncLayer { coHandler(glossAuthorizationInfoController::upsert) } }

        val mvAuthorizedProcedureExecutorController by inject<MvAuthorizedProcedureExecutorController>()
        post("/populate_executed_by_provider_unit") { coHandler(mvAuthorizedProcedureExecutorController::populateExecutedByProviderUnit) }

        val healthSpecialistResourceBundleBackfillController by inject<HealthSpecialistResourceBundleBackfillController>()
        post("/health_specialist_resource_bundle") { coHandler(healthSpecialistResourceBundleBackfillController::updateBatch) }
        post("/health_specialist_resource_bundle/delete") { coHandler(healthSpecialistResourceBundleBackfillController::deleteBatch) }
    }
}
