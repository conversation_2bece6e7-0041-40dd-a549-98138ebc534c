package br.com.alice.member.api.converters

import br.com.alice.common.extensions.money
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.PaymentDetail
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.withInvoicePayments
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.CTA
import br.com.alice.member.api.models.InvoiceBreakdownItemResponse
import br.com.alice.member.api.models.InvoiceResponse
import br.com.alice.member.api.models.InvoiceStatusResponse
import br.com.alice.member.api.models.PaymentInfoResponse
import br.com.alice.member.api.models.PaymentOptionResponse
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.Test

class InvoiceResponseConverterTest {

    private val BASE_URL = ServiceConfig.baseUrl

    @Test
    fun `#convert should return as expected when has no InvoicePayment`() {
        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 3, 3),
            dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
            status = InvoiceStatus.PAID,
            totalAmount = 123.45.money,
        )

        val expected = InvoiceResponse(
            id = invoice.id,
            referenceDate = "Março, 1994",
            dueDate = "2021-03-03",
            status = InvoiceStatusResponse.PAID,
            totalAmount = 123.45.money,
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            )
        )

        val actual = InvoiceResponseConverter.convert(invoice)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return as expected when has no InvoicePayment when has minimum app version`() = runBlocking {
        withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
            val invoice = TestModelFactory.buildMemberInvoice(
                referenceDate = LocalDate.of(1994, 4, 3),
                dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                status = InvoiceStatus.PAID,
                totalAmount = 123.45.money,
            )

            val expected = InvoiceResponse(
                id = invoice.id,
                referenceDate = "Abril, 1994",
                dueDate = "2021-03-03",
                status = InvoiceStatusResponse.PAID,
                totalAmount = 123.45.money,
                breakdown = listOf(
                    InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                    InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                    InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                )
            )

            val actual = InvoiceResponseConverter.convert(invoice, appVersion = SemanticVersion("1.0.0"))
            assertThat(actual).isEqualTo(expected)
        }
    }


    companion object {
        @JvmStatic
        fun methods() = listOf(
            arrayOf(PaymentMethod.PIX, "external-id", TestModelFactory.buildPixPaymentDetail()),
            arrayOf(
                PaymentMethod.BOLEPIX,
                "pix-code",
                TestModelFactory.buildBolepixPaymentDetail(paymentCodePix = "pix-code")
            ),
        )
    }

    @ParameterizedTest(name = "should use code {1} when payment method is {0}")
    @MethodSource("methods")
    fun `#convert should return as expected when has an InvoicePayment other than Boleto`(
        method: PaymentMethod,
        code: String,
        detail: PaymentDetail,
    ) {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            method = method,
            paymentDetail = detail,
            externalId = "external-id"
        )

        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 5, 3),
            dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
            status = InvoiceStatus.PAID,
            totalAmount = 123.45.money,
        ).withInvoicePayments(listOf(invoicePayment))

        val expected = InvoiceResponse(
            id = invoice.id,
            referenceDate = "Maio, 1994",
            dueDate = "2021-03-03",
            status = InvoiceStatusResponse.PAID,
            totalAmount = 123.45.money,
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
            paymentInfo = PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = "Pagamento via PIX",
                code = code
            )
        )

        val actual = InvoiceResponseConverter.convert(invoice, invoicePayment)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Boleto`() {
        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(30)
            )
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 6, 3),
            dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
            status = InvoiceStatus.PAID,
            totalAmount = 123.45.money,
        ).withInvoicePayments(listOf(invoicePayment))

        val expected = InvoiceResponse(
            id = invoice.id,
            referenceDate = "Junho, 1994",
            dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.PAID,
            totalAmount = 123.45.money,
            barcode = "123456",
            boletoUrl = "https://alice.com.br",
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
            paymentInfo = PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = "Código de barra",
                code = "123456",
                documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}"
            )
        )

        val actual = InvoiceResponseConverter.convert(invoice, invoicePayment, billingAccountableParty)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Boleto when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildBoletoPaymentDetail(
                        barcode = "123456",
                        paymentUrl = "https://alice.com.br",
                        dueDate = LocalDateTime.now().minusDays(30)
                    )
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 7, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Julho, 1994",
                    dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    barcode = "123456",
                    boletoUrl = "https://alice.com.br",
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Código de barra",
                            actionCta = CTA(label = "Pagar com boleto", icon = "barcode"),
                            copyCta = CTA(label = "Copiar código do boleto", icon = "copy"),
                            code = "123456",
                            documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment,
                    billingAccountableParty,
                    SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Boleto and it is approved when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildBoletoPaymentDetail(
                        barcode = "123456",
                        paymentUrl = "https://alice.com.br",
                        dueDate = LocalDateTime.now().minusDays(30)
                    )
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(
                        method = PaymentMethod.BOLETO,
                        status = InvoicePaymentStatus.APPROVED,
                        approvedAt = LocalDateTime.now(),
                    ).withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 8, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Agosto, 1994",
                    dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    barcode = "123456",
                    boletoUrl = "https://alice.com.br",
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Pagamento feito via Boleto",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment, billingAccountableParty, SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Simple Credit Card when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildSimpleCreditCardPaymentDetail()
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(method = PaymentMethod.SIMPLE_CREDIT_CARD)
                        .withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 9, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Setembro, 1994",
                    dueDate = "2021-03-03",
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Cartão de Crédito",
                            actionCta = CTA(label = "Pagar com Cartão de Crédito", icon = "credit_card"),
                            documentUrl = paymentDetail.paymentUrl,
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment,
                    billingAccountableParty,
                    SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Simple Credit Card and it is approved when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildSimpleCreditCardPaymentDetail()
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(
                        method = PaymentMethod.SIMPLE_CREDIT_CARD,
                        status = InvoicePaymentStatus.APPROVED,
                        approvedAt = LocalDateTime.now(),
                    ).withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 10, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Outubro, 1994",
                    dueDate = "2021-03-03",
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Pagamento feito via Cartão de Crédito",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment, billingAccountableParty, SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Bolepix when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildBolepixPaymentDetail(dueDate = LocalDateTime.now().minusDays(30))
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLEPIX)
                        .withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 11, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.OPEN,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    canGenerateSecondCopy = true,
                    referenceDate = "Novembro, 1994",
                    dueDate = "2021-03-03",
                    status = InvoiceStatusResponse.OVERDUE,
                    totalAmount = 123.45.money,
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Pagamento via PIX",
                            actionCta = CTA(label = "Pagar com PIX", icon = "pix"),
                            copyCta = CTA(label = "Copiar código PIX", icon = "copy"),
                            code = paymentDetail.paymentCodePix,
                        ),
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Código de barra",
                            actionCta = CTA(label = "Pagar com boleto", icon = "barcode"),
                            copyCta = CTA(label = "Copiar código do boleto", icon = "copy"),
                            code = paymentDetail.barcodeBoleto,
                            documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment,
                    billingAccountableParty,
                    SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Bolepix and it is approved and paid with BOLETO when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildBolepixPaymentDetail(dueDate = LocalDateTime.now().minusDays(30))
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(
                        method = PaymentMethod.BOLETO,
                        status = InvoicePaymentStatus.APPROVED,
                        approvedAt = LocalDateTime.now(),
                    ).withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 12, 3),
                    dueDate = LocalDateTime.of(2021, 3, 20, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Dezembro, 1994",
                    dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
                    barcode = null,
                    boletoUrl = paymentDetail.paymentUrl,
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Pagamento feito via Boleto",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment, billingAccountableParty, SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }

    @Test
    fun `#convert should return as expected when has an InvoicePayment as Bolepix and it is approved and paid with PIX when has minimum app version`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "minimum_app_version_for_use_bolepix", "0.0.0") {
                val paymentDetail =
                    TestModelFactory.buildBolepixPaymentDetail()
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(
                        method = PaymentMethod.PIX,
                        status = InvoicePaymentStatus.APPROVED,
                        approvedAt = LocalDateTime.now(),
                    ).withPaymentDetail(paymentDetail)
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

                val invoice = TestModelFactory.buildMemberInvoice(
                    referenceDate = LocalDate.of(1994, 1, 3),
                    dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
                    status = InvoiceStatus.PAID,
                    totalAmount = 123.45.money,
                ).withInvoicePayments(listOf(invoicePayment))

                val expected = InvoiceResponse(
                    id = invoice.id,
                    referenceDate = "Janeiro, 1994",
                    dueDate = "2021-03-03",
                    status = InvoiceStatusResponse.PAID,
                    totalAmount = 123.45.money,
                    breakdown = listOf(
                        InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                        InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                        InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
                    ),
                    paymentOptions = listOf(
                        PaymentOptionResponse(
                            id = invoicePayment.id.toString(),
                            header = "Pagamento feito via PIX",
                        )
                    )
                )

                val actual = InvoiceResponseConverter.convert(
                    invoice,
                    invoicePayment, billingAccountableParty, SemanticVersion("1.0.0")
                )
                assertThat(actual).isEqualTo(expected)
            }
        }


    @Test
    fun `#convert should not return payment details if billing accountable party is a legal person`() {
        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(30)
            )
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)

        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 2, 3),
            dueDate = LocalDateTime.of(2021, 3, 3, 0, 0, 0),
            status = InvoiceStatus.PAID,
            totalAmount = 123.45.money,
        ).withInvoicePayments(listOf(invoicePayment))

        val expected = InvoiceResponse(
            id = invoice.id,
            referenceDate = "Fevereiro, 1994",
            dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.PAID,
            totalAmount = 123.45.money,
            barcode = "123456",
            boletoUrl = "https://alice.com.br",
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
        )

        val actual = InvoiceResponseConverter.convert(invoice, invoicePayment, billingAccountableParty)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return canGenerateSecondCopy as true if status is OPEN`() {
        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(30)
            )
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)

        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 3, 3),
            dueDate = LocalDateTime.of(2021, 3, 20, 0, 0, 0),
            status = InvoiceStatus.OPEN,
            totalAmount = 123.45.money,
        ).withInvoicePayments(listOf(invoicePayment))

        val expected = InvoiceResponse(
            id = invoice.id,
            canGenerateSecondCopy = true,
            referenceDate = "Março, 1994",
            dueDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.OVERDUE,
            totalAmount = 123.45.money,
            barcode = "123456",
            boletoUrl = "https://alice.com.br",
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
        )

        val actual = InvoiceResponseConverter.convert(invoice, invoicePayment, billingAccountableParty)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return canGenerateSecondCopy as false if dueDate is less than 30 days from now`() {
        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(10)
            )
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)


        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 3, 3),
            dueDate = LocalDateTime.now().minusDays(40),
            status = InvoiceStatus.OPEN,
            totalAmount = 123.45.money,
        ).withInvoicePayments(listOf(invoicePayment))

        val expected = InvoiceResponse(
            id = invoice.id,
            canGenerateSecondCopy = false,
            referenceDate = "Março, 1994",
            dueDate = LocalDateTime.now().minusDays(10).format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.OPEN,
            totalAmount = 123.45.money,
            barcode = "123456",
            boletoUrl = "https://alice.com.br",
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
        )

        val actual = InvoiceResponseConverter.convert(invoice, invoicePayment, billingAccountableParty)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return canGenerateSecondCopy as false if payment is null`() {
        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(30)
            )
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO).withPaymentDetail(paymentDetail)
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)

        val invoice = TestModelFactory.buildMemberInvoice(
            referenceDate = LocalDate.of(1994, 3, 3),
            dueDate = LocalDateTime.of(2021, 3, 20, 0, 0, 0),
            status = InvoiceStatus.OPEN,
            totalAmount = 123.45.money,
        )

        val expected = InvoiceResponse(
            id = invoice.id,
            canGenerateSecondCopy = false,
            referenceDate = "Março, 1994",
            dueDate = LocalDateTime.of(2021, 3, 20, 0, 0, 0).format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.OVERDUE,
            totalAmount = 123.45.money,
            breakdown = listOf(
                InvoiceBreakdownItemResponse(title = "Coparticipação", amount = 4.80.money),
                InvoiceBreakdownItemResponse(title = "Pró-rata", amount = 56.78.money),
                InvoiceBreakdownItemResponse(title = "Preço do Produto", amount = 15.16.money),
            ),
        )

        val actual = InvoiceResponseConverter.convert(invoice, null, billingAccountableParty)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return an invoice liquidation as InvoiceResponse`() {
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.NATURAL_PERSON)

        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
            dueDate = LocalDate.of(2024, 10, 1),
            amount = BigDecimal("100.00"),
        )

        val paymentDetail =
            TestModelFactory.buildBoletoPaymentDetail(
                barcode = "123456",
                paymentUrl = "https://alice.com.br",
                dueDate = LocalDateTime.now().minusDays(30)
            )

        val invoicePayment =
            TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLETO,
                invoiceLiquidationId = invoiceLiquidation.id,
                billingAccountablePartyId = billingAccountableParty.id,
            )
                .withPaymentDetail(paymentDetail)

        val actual = InvoiceResponseConverter.convert(
            invoiceLiquidation,
            billingAccountableParty,
            invoicePayment,
        )

        val expected = InvoiceResponse(
            id = invoiceLiquidation.id,
            canGenerateSecondCopy = false,
            referenceDate = "Outubro, 2024",
            dueDate = invoiceLiquidation.dueDate.format(DateTimeFormatter.ISO_DATE),
            status = InvoiceStatusResponse.OPEN,
            totalAmount = 100.00.money,
            breakdown = null,
            paymentInfo = PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = "Código de barra",
                documentUrl = "${BASE_URL}/invoices/bank_slip/${invoicePayment.id}",
                code = paymentDetail.barcode,
            )
        )

        assertThat(actual).isEqualTo(expected)
    }
}
