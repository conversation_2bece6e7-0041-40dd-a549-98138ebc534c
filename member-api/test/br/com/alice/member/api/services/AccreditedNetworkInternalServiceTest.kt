package br.com.alice.member.api.services

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CalloutAction
import br.com.alice.app.content.model.CalloutType
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.Caption
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.toBrazilianTimeFormat
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.coverage.br.com.alice.coverage.converter.HealthProfessionalConsolidatedLightConverter.toConsolidatedType
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedAccreditedNetworkService
import br.com.alice.coverage.client.ConsolidatedAccreditedProvidersNetworkService
import br.com.alice.coverage.client.ConsolidatedAccreditedSpecialistNetworkService
import br.com.alice.coverage.converters.toConsolidatedSpecialistTransport
import br.com.alice.coverage.model.accredited_network.request.SpecialistRequestByProduct
import br.com.alice.coverage.model.accredited_network.request.UnitsRequest
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedProviderLightTransport
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedSpecialistLightTransport
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildAccreditedNetworkFavorite
import br.com.alice.data.layer.helpers.TestModelFactory.buildConsolidatedAccreditedNetwork
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthProfessional
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthcareTeam
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthcareTeamPhysician
import br.com.alice.data.layer.helpers.TestModelFactory.buildMedicalSpecialty
import br.com.alice.data.layer.helpers.TestModelFactory.buildMember
import br.com.alice.data.layer.helpers.TestModelFactory.buildPerson
import br.com.alice.data.layer.helpers.TestModelFactory.buildProviderUnit
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaffSchedule
import br.com.alice.data.layer.helpers.TestModelFactory.buildStructuredAddress
import br.com.alice.data.layer.models.AccreditedNetworkFavorite
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkAppointmentType
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY_CHILDREN
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.HOSPITAL
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.LABORATORY
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.MATERNITY
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyProfile
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.ReferralSpecialty
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.data.layer.models.SuggestedSpecialist
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.ProviderDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderResponse
import br.com.alice.member.api.models.accreditedNetwork.ProviderSection
import br.com.alice.member.api.models.accreditedNetwork.ProviderSectionType
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderUnitRequestType
import br.com.alice.member.api.models.accreditedNetwork.SubSpecialtyTransport
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.membership.model.events.MemberAccreditedNetworkTrackerEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.MemberWithProduct
import br.com.alice.provider.client.Filter
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.staff.client.CassiSpecialistService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

const val SPECIALIST_EMPTY_LIST_TITLE = "Nenhum resultado próximo a você"
const val SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE = "Conte com nosso time do Alice Agora para te ajudar"
const val SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA = "Conte com nosso time de Atendimento para te ajudar"
const val ADVANCED_ACCESS_TITLE = "Agendar Consulta"
const val ADVANCED_ACCESS_EMPTY_LIST_TITLE = "Não foi possível carregar as informações."

class AccreditedNetworkInternalServiceTest {
    private val memberService: MemberService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val personService: PersonService = mockk()
    private val cassiSpecialistService: CassiSpecialistService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkService = mockk()
    private val consolidatedAccreditedProvidersNetworkService: ConsolidatedAccreditedProvidersNetworkService = mockk()
    private val consolidatedAccreditedSpecialistNetworkService: ConsolidatedAccreditedSpecialistNetworkService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val healthCareTeamService: HealthcareTeamService = mockk()
    private val schedulingUrlBuilder: SchedulingUrlBuilder = mockk()
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService = mockk()
    private val labiIntegrationService: LabiIntegrationService = mockk()
    private val providerService: ProviderService = mockk()
    private val specialistSchedulingInternalService: SpecialistSchedulingInternalService = mockk()
    private val staffScheduleService: StaffScheduleService = mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitService = mockk()

    private val service = AccreditedNetworkInternalService(
        consolidatedAccreditedNetworkService,
        consolidatedAccreditedProvidersNetworkService,
        consolidatedAccreditedSpecialistNetworkService,
        appointmentScheduleEventTypeService,
        staffScheduleService,
        medicalSpecialtyService,
        personService,
        memberService,
        cassiSpecialistService,
        providerUnitService,
        eventTypeProviderUnitService,
        healthPlanTaskService,
        healthCareTeamService,
        healthProfessionalService,
        schedulingUrlBuilder,
        kafkaProducerService,
        accreditedNetworkFavoriteService,
        labiIntegrationService,
        providerService,
        specialistSchedulingInternalService,
    )
    private val person = buildPerson(nickName = null)
    private val member = buildMember(personId = person.id)
    private val duquesaMember = buildMember(personId = person.id, brand = Brand.DUQUESA)
    private val lat = "-23.5718116"
    private val lng = "-46.**************"
    private val structuredAddress = buildStructuredAddress()
    val hasHospitalHealthTeamTag = Tag(
        text = "Time de Saúde no Hospital",
        colorScheme = TagColorScheme.MAGENTA,
        icon = "diamond"
    )
    private val deAccreditationDate = LocalDate.of(2025, 1, 1)
    private val deAccreditationTag = Tag(
        text = "Disponível até 01/01",
        colorScheme = TagColorScheme.BLUE
    )
    private val recommendedTag = Tag(
        text = "Indicado para você",
        colorScheme = TagColorScheme.MAGENTA,
        icon = "pin"
    )

    private val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral()

    private val pediatricSpecialty = buildMedicalSpecialty(name = "Pediatria")
    private val mfcSpecialty = buildMedicalSpecialty(name = "Medicina de Família e Comunidade")

    private val distance = 1000.0

    private val duquesaEmptyProvidersListAction = ProviderResponse.ActionNavigation(
        label = "Falar com Atendimento",
        navigation = NavigationResponse(mobileRoute = MobileRouting.DUQUESA_SERVICE)
    )

    private val talkToAliceAgoraAction = ProviderResponse.ActionNavigation(
        label = "Falar com Alice Agora",
        navigation = NavigationResponse(mobileRoute = MobileRouting.REDESIGN_ALICE_AGORA)
    )

    @AfterTest
    fun after() {
        clearAllMocks()
    }

    @Nested
    inner class GetUnitsByType {
        private val type = HOSPITAL

        @Test
        fun `#Should return ProviderResponse for Duquesa`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                providerId = RangeUUID.generate(),
                distance = distance,
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CLINICAL_COMMUNITY,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL
                )
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Hospital 3",
                id = RangeUUID.generate(),
                type = MATERNITY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on"),
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = providerUnit3.imageUrl,
                    type = MATERNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${MATERNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = duquesaEmptyProvidersListAction,
                ),
                action = duquesaEmptyProvidersListAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = true,
            )
            coEvery { memberService.getCurrent(duquesaMember.personId) } returns duquesaMember.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = duquesaMember.productId,
                        types = listOf(type),
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                duquesaMember.personId,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                SemanticVersion("4.0.0")
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CLINICAL_COMMUNITY,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL
                )
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Hospital 3",
                id = RangeUUID.generate(),
                type = MATERNITY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on"),
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = providerUnit3.imageUrl,
                    type = MATERNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${MATERNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with mobile routing AA`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CLINICAL_COMMUNITY,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL
                )
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Hospital 3",
                id = RangeUUID.generate(),
                type = MATERNITY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on"),
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = providerUnit3.imageUrl,
                    type = MATERNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${MATERNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with mobile routing to RedesignAA`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CLINICAL_COMMUNITY,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL
                )
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Hospital 3",
                id = RangeUUID.generate(),
                type = MATERNITY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on"),
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = providerUnit3.imageUrl,
                    type = MATERNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${MATERNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }


        @Test
        fun `#Should return ProviderResponse with empty imageUrl`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = null,
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CASSI_SPECIALIST,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL
                )
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Lab 1",
                id = RangeUUID.generate(),
                type = LABORATORY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = AccreditedNetworkInternalService.HOSPITAL_PLACEHOLDER_IMAGE,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = AccreditedNetworkInternalService.PROFESSIONAL_PLACEHOLDER_IMAGE,
                    type = CASSI_SPECIALIST,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on"),
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = AccreditedNetworkInternalService.LAB_PLACEHOLDER_IMAGE,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse without proximity tag`() = runBlocking {
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Hospital 2",
                id = RangeUUID.generate(),
                type = CLINICAL_COMMUNITY
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Hospital 3",
                id = RangeUUID.generate(),
                type = MATERNITY,
                deAccreditationDate = deAccreditationDate
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = deAccreditationTag,
                    imageUrl = providerUnit3.imageUrl,
                    type = MATERNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${MATERNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = deAccreditationDate,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("3.60.0")

            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with hospital health team tag`() = runBlocking {
            val appVersion = SemanticVersion("3.60.0")
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Hospital 1",
                type = HOSPITAL,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                hasHospitalHealthTeam = true,
                providerId = RangeUUID.generate()
            )
            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = hasHospitalHealthTeamTag,
                    imageUrl = providerUnit1.imageUrl,
                    type = HOSPITAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${HOSPITAL}/${providerUnit1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    hasHospitalHealthTeam = true,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin"))
                )
            )
            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = ProviderUnitRequestType.HOSPITAL.title,
                providersType = "HOSPITAL",
                otherSectionTitle = "Outros Hospitais",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with empty providers`() = runBlocking {
            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns emptyList<ConsolidatedProviderLightTransport>().success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                appVersion
            )
            assertThat(result).isSuccessWithData(
                ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        action = talkToAliceAgoraAction,
                    ),
                    title = "Hospitais",
                    providersType = "HOSPITAL",
                    otherSectionTitle = "Outros Hospitais",
                    action = talkToAliceAgoraAction,
                    isDuquesa = false,
                )
            )

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#Should return Exception try get provers unit`() = runBlocking {
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId, types = listOf(type), lat = lat, lng = lng, rangeInMeters = 50_000
                    )
                )
            } returns Exception().failure()

            val result = service.getUnitsByTypes(
                person.id,
                listOf(ProviderUnitRequestType.HOSPITAL),
                lat,
                lng,
                SemanticVersion("4.0.0")
            )
            assertThat(result).isFailure()

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }
    }

    @Nested
    inner class GetSpecialists {
        private val specialtyId = RangeUUID.generate()
        private val subSpecialtyId = RangeUUID.generate()

        @Test
        fun `#Should return ProviderResponse`() = runBlocking {
            val specialist1 = ConsolidatedSpecialistLightTransport(
                id = RangeUUID.generate(),
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                name = "Specialist 1",
                address = buildStructuredAddress(),
                imageUrl = "http://especialista.com.br/logo.png",
                distance = distance,
                appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                gender = null,
                subSpecialtyIds = listOf(subSpecialtyId),
            )
            val specialist2 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 2",
                type = CASSI_SPECIALIST,
            )
            val specialist3 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 3",
                type = CLINICAL_COMMUNITY
            )
            val specialist4 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 4",
                type = CLINICAL_COMMUNITY,
                address = null,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                )
            )
            val specialist5 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 5",
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                address = null,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                ),
                subSpecialtyIds = emptyList(),
                scheduleAvailabilityDays = 10,
            )
            val specialist6 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 6",
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                address = null,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                ),
                subSpecialtyIds = emptyList(),
                scheduleAvailabilityDays = 1,
            )
            val specialist7 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 7",
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                address = null,
                appointmentTypes = listOf(
                    ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                ),
                subSpecialtyIds = emptyList(),
                scheduleAvailabilityDays = 0,
            )

            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = specialist1.id,
                    staffId = specialist1.staffId,
                    name = specialist1.name,
                    title = specialist1.name,
                    subtitle = null,
                    description = specialist1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist1.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                    subSpecialtyIds = specialist1.subSpecialtyIds,
                ),
                ProviderTransport(
                    id = specialist2.id,
                    staffId = specialist2.staffId,
                    name = specialist2.name,
                    title = specialist2.name,
                    subtitle = null,
                    description = specialist2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist2.imageUrl,
                    type = CASSI_SPECIALIST,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                    subSpecialtyIds = specialist2.subSpecialtyIds,
                ), ProviderTransport(
                    id = specialist3.id,
                    staffId = specialist3.staffId,
                    name = specialist3.name,
                    title = specialist3.name,
                    subtitle = null,
                    description = specialist3.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist3.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                    ),
                    subSpecialtyIds = specialist3.subSpecialtyIds,
                ), ProviderTransport(
                    id = specialist4.id,
                    staffId = specialist4.staffId,
                    name = specialist4.name,
                    title = specialist4.name,
                    subtitle = null,
                    description = null,
                    tag = null,
                    imageUrl = specialist4.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist4.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on")
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist4.subSpecialtyIds,
                )
            )

            val expectedOtherProviderItems = listOf(
                ProviderTransport(
                    id = specialist5.id,
                    staffId = specialist5.staffId,
                    name = specialist5.name,
                    title = specialist5.name,
                    subtitle = null,
                    description = null,
                    tag = Tag(
                        text = "Previsão de Agenda: 10 dias",
                        colorScheme = TagColorScheme.GRAY
                    ),
                    imageUrl = specialist5.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist5.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    scheduleAvailabilityDays = 10,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on")
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist5.subSpecialtyIds,
                ),
                ProviderTransport(
                    id = specialist6.id,
                    staffId = specialist6.staffId,
                    name = specialist6.name,
                    title = specialist6.name,
                    subtitle = null,
                    description = null,
                    tag = Tag(
                        text = "Previsão de Agenda: Amanhã",
                        colorScheme = TagColorScheme.GRAY
                    ),
                    imageUrl = specialist6.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist6.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    scheduleAvailabilityDays = 1,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on")
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist6.subSpecialtyIds,
                ),
                ProviderTransport(
                    id = specialist7.id,
                    staffId = specialist7.staffId,
                    name = specialist7.name,
                    title = specialist7.name,
                    subtitle = null,
                    description = null,
                    tag = null,
                    imageUrl = specialist7.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist7.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    scheduleAvailabilityDays = null,
                    captions = listOf(
                        Caption("1 km", "local_pin"),
                        Caption("Presencial", "meeting"),
                        Caption("Vídeo", "video_on")
                    ),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist7.subSpecialtyIds,
                ),
            )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    title = SPECIALIST_EMPTY_LIST_TITLE,
                    description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                providersType = "HEALTH_PROFESSIONAL",
                providersSubtypeId = specialtyId.toString(),
                emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                title = "Ortopedia",
                otherSectionTitle = "Outros Especialistas e Clínicas",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = "Especialistas em Ortopedia",
                        description = "Experientes na sua demanda, bem avaliados e currículos excepcionais",
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                    ProviderSection(
                        title = "Outros em Ortopedia",
                        description = null,
                        items = expectedOtherProviderItems,
                        type = ProviderSectionType.STANDARD,
                    ),
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )
                )
            } returns listOf(
                specialist1,
                specialist2,
                specialist3,
                specialist4,
                specialist5,
                specialist6,
                specialist7
            ).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                id = subSpecialtyId,
                type = MedicalSpecialtyType.SUBSPECIALTY
            ).success()
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                id = specialtyId,
                type = MedicalSpecialtyType.SPECIALTY
            ).success()
            coEvery {
                medicalSpecialtyService.findBy(
                    filter = Filter(
                        active = true,
                        internal = false,
                        parentSpecialtyId = specialtyId,
                    ),
                    range = null,
                )
            } returns emptyList<MedicalSpecialty>().success()
            coEvery {
                accreditedNetworkFavoriteService.findByPersonId(
                    person.id,
                    AccreditedNetworkFavoriteService.Filter(
                        specialtyId = specialtyId,
                    )
                )
            } returns emptyList<AccreditedNetworkFavorite>().success()

            val result =
                service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with MFC Specialty`() = runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP, mapOf(
                    "mfc_specialty_ids" to listOf(mfcSpecialty.id.toString()),
                    "ped_specialty_ids" to listOf(pediatricSpecialty.id.toString()),
                    "accredited_network_pediatric_schedule_event_type_id" to "123abc",
                    "accredited_network_mfc_schedule_event_type_id" to "123abc",
                    "app_version_with_providers_highlight" to "4.0.0",
                    "app_version_with_specialist_details_screen" to "4.0.0",
                    "app_version_with_specialist_details_screen_with_membershipcard" to "4.0.0",
                )
            ) {
                val scheduleBuilderResponse = "webview_url"
                val healthCareTeamPhysician = buildHealthcareTeamPhysician()
                val hp = buildHealthProfessional(staffId = healthCareTeamPhysician.id)
                val healthCareTeam = buildHealthcareTeam(physicianStaffId = healthCareTeamPhysician.id)
                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                    gender = null,
                    specialtyIds = listOf(mfcSpecialty.id),
                    subSpecialtyIds = listOf(subSpecialtyId),
                )
                val orthopedicSpecialist = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 2",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                    specialtyIds = listOf(RangeUUID.generate()),
                    subSpecialtyIds = listOf(subSpecialtyId),
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    type = CASSI_SPECIALIST
                )
                val specialist3 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    type = CLINICAL_COMMUNITY
                )
                val specialist4 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 4",
                    type = CLINICAL_COMMUNITY,
                    address = null,
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                    )
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        staffId = specialist1.staffId,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = specialist1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/healthcare_team/physician/${specialist1.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist1.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = orthopedicSpecialist.id,
                        staffId = orthopedicSpecialist.staffId,
                        name = orthopedicSpecialist.name,
                        title = orthopedicSpecialist.name,
                        subtitle = null,
                        description = orthopedicSpecialist.address?.formattedAddress(),
                        tag = null,
                        imageUrl = orthopedicSpecialist.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/specialists/${orthopedicSpecialist.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = orthopedicSpecialist.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        staffId = specialist2.staffId,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = specialist2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist2.imageUrl,
                        type = CASSI_SPECIALIST,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist2.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist3.id,
                        staffId = specialist3.staffId,
                        name = specialist3.name,
                        title = specialist3.name,
                        subtitle = null,
                        description = specialist3.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist3.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist3.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist4.id,
                        staffId = specialist4.staffId,
                        name = specialist4.name,
                        title = specialist4.name,
                        subtitle = null,
                        description = null,
                        tag = null,
                        imageUrl = specialist4.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist4.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(
                            Caption("1 km", "local_pin"),
                            Caption("Presencial", "meeting"),
                            Caption("Vídeo", "video_on")
                        ),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                            ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ),
                        subSpecialtyIds = specialist4.subSpecialtyIds,
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = pediatricSpecialty.id.toString(),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    title = pediatricSpecialty.name,
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    highlights = listOf(
                        ProviderTransport(
                            id = hp.id,
                            staffId = hp.staffId,
                            type = hp.type.toConsolidatedType(),
                            name = hp.name,
                            title = hp.name,
                            subtitle = "Pediatra de ${person.firstName}",
                            subtitleInfo = ProviderTransport.SubtitleInfo(
                                text = "Pediatra de ${person.firstName}",
                                colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.MAGENTA,
                            ),
                            description = null,
                            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg",
                            tag = Tag(
                                text = "Pediatra de ${person.firstName}",
                                colorScheme = TagColorScheme.MAGENTA,
                            ),
                            captions = emptyList(),
                            distance = null,
                            deAccreditationDate = null,
                            hasHospitalHealthTeam = false,
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                endpoint = "/accredited_network/healthcare_team/physician/${hp.id}?lat=-23.5718116&lng=-46.**************",
                                mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            ),
                            action = ProviderTransport.ActionNavigation(
                                label = "Agendar consulta",
                                navigation = RemoteAction(
                                    mobileRoute = MobileRouting.WEBVIEW,
                                    removeUntilRouteName = "LOGGED_IN",
                                    params = mapOf(
                                        "link" to scheduleBuilderResponse,
                                        "pop_on_complete" to true,
                                        "feedback_message" to "Agendamento efetuado com sucesso!",
                                        "token" to "true"
                                    )
                                )
                            ),
                            subSpecialtyIds = hp.subSpecialtyIds,
                        )
                    ),
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = "Especialistas em Ortopedia",
                            description = "Experientes na sua demanda, bem avaliados e currículos excepcionais",
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                )
                val appVersion = SemanticVersion("4.0.0")

                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = pediatricSpecialty.id,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000,
                            subSpecialtyId = null,
                        )
                    )
                } returns listOf(
                    specialist1,
                    orthopedicSpecialist,
                    specialist2,
                    specialist3,
                    specialist4,
                    hp.toConsolidatedSpecialistTransport(lat.toDouble(), lng.toDouble())
                ).success()
                coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
                coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                    id = subSpecialtyId,
                    type = MedicalSpecialtyType.SUBSPECIALTY
                ).success()
                coEvery { medicalSpecialtyService.getById(pediatricSpecialty.id) } returns pediatricSpecialty.success()
                coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
                coEvery {
                    healthProfessionalService.findByStaffId(
                        healthCareTeamPhysician.id,
                        FindOptions(withStaff = false, withContact = true)
                    )
                } returns hp.success()
                coEvery { personService.get(member.personId) } returns person.success()
                coEvery {
                    schedulingUrlBuilder.buildScheduleUrlLight(
                        calendarUrl = null,
                        extras = mapOf(
                            "staff_id" to hp.staffId.toString(),
                            "appointment_schedule_event_type_id" to "123abc",
                        ),
                        shouldUseInternalScheduler = true
                    )
                } returns scheduleBuilderResponse
                coEvery {
                    medicalSpecialtyService.findBy(
                        filter = Filter(
                            active = true,
                            internal = false,
                            parentSpecialtyId = pediatricSpecialty.id,
                        ),
                        range = null,
                    )
                } returns emptyList<MedicalSpecialty>().success()
                coEvery {
                    accreditedNetworkFavoriteService.findByPersonId(
                        person.id,
                        AccreditedNetworkFavoriteService.Filter(
                            specialtyId = pediatricSpecialty.id,
                        )
                    )
                } returns emptyList<AccreditedNetworkFavorite>().success()

                val result =
                    service.getSpecialists(
                        person.id,
                        pediatricSpecialty.id,
                        subSpecialtyId,
                        null,
                        lat,
                        lng,
                        appVersion
                    )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(any()) }
                coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { schedulingUrlBuilder.buildScheduleUrlLight(any(), any(), any()) }
                coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
            }
        }

        @Test
        fun `#Should return ProviderResponse without MFC if member doesn't have HealthcareTeam`() = runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP, mapOf(
                    "mfc_specialty_ids" to listOf(mfcSpecialty.id.toString()),
                    "ped_specialty_ids" to listOf(pediatricSpecialty.id.toString()),
                    "accredited_network_pediatric_schedule_event_type_id" to "123abc",
                    "accredited_network_mfc_schedule_event_type_id" to "123abc",
                )
            ) {
                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                    gender = null,
                    specialtyIds = listOf(mfcSpecialty.id),
                    subSpecialtyIds = listOf(subSpecialtyId),
                )
                val specialistHP = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist Health Professional",
                    type = HEALTH_PROFESSIONAL
                )
                val specialistPartnerHP = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist Health Professional",
                    type = PARTNER_HEALTH_PROFESSIONAL
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    type = CASSI_SPECIALIST
                )
                val specialist3 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    type = CLINICAL_COMMUNITY
                )
                val specialist4 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 4",
                    type = CLINICAL_COMMUNITY,
                    address = null,
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                    )
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = specialist1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/healthcare_team/physician/${specialist1.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist1.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = specialistHP.id,
                        name = specialistHP.name,
                        title = specialistHP.name,
                        subtitle = null,
                        description = specialistHP.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialistHP.imageUrl,
                        type = HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/healthcare_team/physician/${specialistHP.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialistHP.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = specialistPartnerHP.id,
                        name = specialistPartnerHP.name,
                        title = specialistPartnerHP.name,
                        subtitle = null,
                        description = specialistPartnerHP.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialistPartnerHP.imageUrl,
                        type = PARTNER_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/healthcare_team/physician/${specialistPartnerHP.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialistPartnerHP.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = specialist2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist2.imageUrl,
                        type = CASSI_SPECIALIST,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist2.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist3.id,
                        name = specialist3.name,
                        title = specialist3.name,
                        subtitle = null,
                        description = specialist3.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist3.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist3.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist4.id,
                        name = specialist4.name,
                        title = specialist4.name,
                        subtitle = null,
                        description = null,
                        tag = null,
                        imageUrl = specialist4.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist4.id}?lat=${lat}&lng=${lng}&specialty_id=${pediatricSpecialty.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(
                            Caption("1 km", "local_pin"),
                            Caption("Presencial", "meeting"),
                            Caption("Vídeo", "video_on")
                        ),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                            ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ),
                        subSpecialtyIds = specialist4.subSpecialtyIds,
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = pediatricSpecialty.id.toString(),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    title = pediatricSpecialty.name,
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = "Especialistas em Ortopedia",
                            description = "Experientes na sua demanda, bem avaliados e currículos excepcionais",
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                )
                val appVersion = SemanticVersion("4.0.0")
                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = pediatricSpecialty.id,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000,
                            subSpecialtyId = null,
                        )
                    )
                } returns listOf(
                    specialist1,
                    specialistHP,
                    specialistPartnerHP,
                    specialist2,
                    specialist3,
                    specialist4,
                ).success()
                coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
                coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                    id = subSpecialtyId,
                    type = MedicalSpecialtyType.SUBSPECIALTY
                ).success()
                coEvery { medicalSpecialtyService.getById(pediatricSpecialty.id) } returns pediatricSpecialty.success()
                coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns NotFoundException().failure()
                coEvery {
                    medicalSpecialtyService.findBy(
                        filter = Filter(
                            active = true,
                            internal = false,
                            parentSpecialtyId = pediatricSpecialty.id,
                        ),
                        range = null,
                    )
                } returns emptyList<MedicalSpecialty>().success()
                coEvery {
                    accreditedNetworkFavoriteService.findByPersonId(
                        person.id,
                        AccreditedNetworkFavoriteService.Filter(
                            specialtyId = pediatricSpecialty.id,
                        )
                    )
                } returns emptyList<AccreditedNetworkFavorite>().success()

                val result =
                    service.getSpecialists(
                        person.id,
                        pediatricSpecialty.id,
                        subSpecialtyId,
                        null,
                        lat,
                        lng,
                        appVersion
                    )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(any()) }
                coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
            }
        }

        @Test
        fun `#Should return ProviderResponse with MFC Specialty without highlight`() = runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP, mapOf(
                    "mfc_specialty_ids" to listOf(mfcSpecialty.id.toString()),
                    "ped_specialty_ids" to listOf(pediatricSpecialty.id.toString()),
                    "accredited_network_pediatric_schedule_event_type_id" to "123abc",
                    "accredited_network_mfc_schedule_event_type_id" to "123abc",
                    "app_version_with_providers_highlight" to "2.0.0",
                    "app_version_with_specialist_details_screen" to "4.0.0"
                )
            ) {
                val healthCareTeamPhysician = buildHealthcareTeamPhysician()
                val hp = buildHealthProfessional(
                    staffId = healthCareTeamPhysician.id,
                    type = StaffType.HEALTH_PROFESSIONAL,
                    subSpecialtyIds = listOf(subSpecialtyId),
                )
                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                    gender = null,
                    specialtyIds = listOf(mfcSpecialty.id),
                    subSpecialtyIds = listOf(subSpecialtyId),
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    type = CASSI_SPECIALIST
                )
                val specialist3 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    type = CLINICAL_COMMUNITY
                )
                val specialist4 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 4",
                    type = CLINICAL_COMMUNITY,
                    address = null,
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE
                    )
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        staffId = specialist1.staffId,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = specialist1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/healthcare_team/physician/${specialist1.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist1.subSpecialtyIds,
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        staffId = specialist2.staffId,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = specialist2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist2.imageUrl,
                        type = CASSI_SPECIALIST,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist2.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist3.id,
                        staffId = specialist3.staffId,
                        name = specialist3.name,
                        title = specialist3.name,
                        subtitle = null,
                        description = specialist3.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist3.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin"), Caption("Presencial", "meeting")),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist3.subSpecialtyIds,
                    ), ProviderTransport(
                        id = specialist4.id,
                        staffId = specialist4.staffId,
                        name = specialist4.name,
                        title = specialist4.name,
                        subtitle = null,
                        description = null,
                        tag = null,
                        imageUrl = specialist4.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist4.id}?lat=${lat}&lng=${lng}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(
                            Caption("1 km", "local_pin"),
                            Caption("Presencial", "meeting"),
                            Caption("Vídeo", "video_on")
                        ),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                            ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                        ),
                        subSpecialtyIds = specialist4.subSpecialtyIds,
                    ), ProviderTransport(
                        id = hp.id,
                        staffId = hp.staffId,
                        name = hp.name,
                        title = hp.name,
                        subtitle = null,
                        description = null,
                        tag = null,
                        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg",
                        type = hp.type.toConsolidatedType(),
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/specialists/${hp.id}?lat=${lat}&lng=${lng}"
                        ),
                        distance = null,
                        deAccreditationDate = null,
                        captions = emptyList(),
                        subSpecialtyIds = hp.subSpecialtyIds,
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialtyId.toString(),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    title = "Ortopedia",
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    highlights = emptyList(),
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = "Especialistas em Ortopedia",
                            description = "Experientes na sua demanda, bem avaliados e currículos excepcionais",
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                )
                val appVersion = SemanticVersion("4.0.0")

                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = specialtyId,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000,
                            subSpecialtyId = null,
                        )
                    )
                } returns listOf(
                    specialist1,
                    specialist2,
                    specialist3,
                    specialist4,
                    hp.toConsolidatedSpecialistTransport(lat.toDouble(), lng.toDouble())
                ).success()
                coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
                coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                    id = subSpecialtyId,
                    type = MedicalSpecialtyType.SUBSPECIALTY
                ).success()
                coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                    id = specialtyId,
                    type = MedicalSpecialtyType.SPECIALTY
                ).success()
                coEvery {
                    medicalSpecialtyService.findBy(
                        filter = Filter(
                            active = true,
                            internal = false,
                            parentSpecialtyId = specialtyId,
                        ),
                        range = null,
                    )
                } returns emptyList<MedicalSpecialty>().success()
                coEvery {
                    accreditedNetworkFavoriteService.findByPersonId(
                        person.id,
                        AccreditedNetworkFavoriteService.Filter(
                            specialtyId = specialtyId,
                        )
                    )
                } returns emptyList<AccreditedNetworkFavorite>().success()

                val result =
                    service.getSpecialists(
                        person.id,
                        specialtyId,
                        subSpecialtyId,
                        null,
                        lat,
                        lng,
                        appVersion
                    )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
            }
        }

        @Test
        fun `#Should return ProviderResponse with only remote attendance`() = runBlocking {
            val specialist1 = ConsolidatedSpecialistLightTransport(
                id = RangeUUID.generate(),
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                name = "Specialist 1",
                address = null,
                distance = null,
                imageUrl = "http://especialista.com.br/logo.png",
                appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.REMOTE),
                gender = null,
                subSpecialtyIds = listOf(subSpecialtyId),
            )
            val specialist2 = specialist1.copy(id = RangeUUID.generate(), name = "Specialist 4")

            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = specialist1.id,
                    staffId = specialist1.staffId,
                    name = specialist1.name,
                    title = specialist1.name,
                    subtitle = null,
                    description = specialist1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist1.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = null,
                    deAccreditationDate = null,
                    captions = listOf(Caption("Atendimento por Vídeo", "video_on")),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist1.subSpecialtyIds,
                ), ProviderTransport(
                    id = specialist2.id,
                    staffId = specialist2.staffId,
                    name = specialist2.name,
                    title = specialist2.name,
                    subtitle = null,
                    description = null,
                    tag = null,
                    imageUrl = specialist2.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist2.id}?lat=${lat}&lng=${lng}"
                    ),
                    distance = null,
                    deAccreditationDate = null,
                    captions = listOf(Caption("Atendimento por Vídeo", "video_on")),
                    appointmentTypes = listOf(
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE,
                    ),
                    subSpecialtyIds = specialist2.subSpecialtyIds,
                )
            )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    title = SPECIALIST_EMPTY_LIST_TITLE,
                    description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                providersType = "HEALTH_PROFESSIONAL",
                providersSubtypeId = specialtyId.toString(),
                emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                title = "Ortopedia",
                otherSectionTitle = "Outros Especialistas e Clínicas",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = "Especialistas em Ortopedia",
                        description = "Experientes na sua demanda, bem avaliados e currículos excepcionais",
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    )
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )
                )
            } returns listOf(specialist1, specialist2).success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                id = subSpecialtyId,
                type = MedicalSpecialtyType.SUBSPECIALTY
            ).success()
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                id = specialtyId,
                type = MedicalSpecialtyType.SPECIALTY
            ).success()
            coEvery {
                medicalSpecialtyService.findBy(
                    filter = Filter(
                        active = true,
                        internal = false,
                        parentSpecialtyId = specialtyId,
                    ),
                    range = null,
                )
            } returns emptyList<MedicalSpecialty>().success()
            coEvery {
                accreditedNetworkFavoriteService.findByPersonId(
                    person.id,
                    AccreditedNetworkFavoriteService.Filter(
                        specialtyId = specialtyId,
                    )
                )
            } returns emptyList<AccreditedNetworkFavorite>().success()

            val result =
                service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with empty providers for Alice`() = runBlocking {
            val subSpecialty = buildMedicalSpecialty(name = "Pes")

            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )
                )
            } returns emptyList<ConsolidatedSpecialistLightTransport>().success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                id = subSpecialtyId,
                type = MedicalSpecialtyType.SUBSPECIALTY
            ).success()
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                id = specialtyId,
                type = MedicalSpecialtyType.SPECIALTY
            ).success()
            coEvery {
                medicalSpecialtyService.findBy(
                    filter = Filter(
                        active = true,
                        internal = false,
                        parentSpecialtyId = specialtyId,
                    ),
                    range = null,
                )
            } returns listOf(subSpecialty).success()
            coEvery {
                accreditedNetworkFavoriteService.findByPersonId(
                    person.id,
                    AccreditedNetworkFavoriteService.Filter(
                        specialtyId = specialtyId,
                    )
                )
            } returns emptyList<AccreditedNetworkFavorite>().success()

            val result =
                service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(
                ProviderResponse(
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialtyId.toString(),
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction
                    ),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    title = "Ortopedia",
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    action = talkToAliceAgoraAction,
                    isDuquesa = false,
                    subSpecialties = listOf(
                        SubSpecialtyTransport(
                            id = subSpecialty.id,
                            name = subSpecialty.name,
                        )
                    )
                )
            )

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
        }

        @Test
        fun `#Should return ProviderResponse with empty providers for Duquesa`() = runBlocking {
            val subSpecialty = buildMedicalSpecialty(name = "Pes")
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                specialty = ReferralSpecialty(name = "Ortopedia", id = specialtyId)
            )

            val appVersion = SemanticVersion("4.0.0")
            coEvery { memberService.getCurrent(duquesaMember.personId) } returns duquesaMember.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = duquesaMember.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )
                )
            } returns emptyList<ConsolidatedSpecialistLightTransport>().success()
            coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                id = subSpecialtyId,
                type = MedicalSpecialtyType.SUBSPECIALTY
            ).success()
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                id = specialtyId,
                type = MedicalSpecialtyType.SPECIALTY
            ).success()
            coEvery {
                medicalSpecialtyService.findBy(
                    filter = Filter(
                        active = true,
                        internal = false,
                        parentSpecialtyId = specialtyId,
                    ),
                    range = null,
                )
            } returns listOf(subSpecialty).success()
            coEvery {
                accreditedNetworkFavoriteService.findByPersonId(
                    person.id,
                    AccreditedNetworkFavoriteService.Filter(
                        specialtyId = specialtyId,
                    )
                )
            } returns emptyList<AccreditedNetworkFavorite>().success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                    duquesaMember.personId,
                    HealthPlanTaskType.REFERRAL
                )
            } returns listOf(healthPlanTask).success()

            val result =
                service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(
                ProviderResponse(
                    title = "Ortopedia",
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialtyId.toString(),
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                        action = duquesaEmptyProvidersListAction,
                    ),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                    items = emptyList(),
                    action = duquesaEmptyProvidersListAction,
                    isDuquesa = true,
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    subSpecialties = listOf(
                        SubSpecialtyTransport(
                            id = subSpecialty.id,
                            name = subSpecialty.name,
                        )
                    )
                )
            )

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
        }

        @Test
        fun `#Should return Exception try get provers unit`() = runBlocking {
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery { medicalSpecialtyService.getById(any()) } returns mfcSpecialty.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = subSpecialtyId,
                    )
                )
            } returns Exception().failure()

            val result =
                service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, SemanticVersion("4.0.0"))
            assertThat(result).isFailure()

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#getFavorites should return list of member favorites`() = runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP, mapOf(
                    "app_version_with_highlight_on_providers_list" to "4.0.0",
                )
            ) {
                val personId = member.personId
                val healthCareTeamPhysician = buildHealthcareTeamPhysician()
                val healthCareTeam = buildHealthcareTeam(physicianStaffId = healthCareTeamPhysician.id)
                val hp = buildHealthProfessional(staffId = healthCareTeamPhysician.id)
                val specialty = buildMedicalSpecialty(name = "Acupuntura")
                val favorite = buildAccreditedNetworkFavorite(specialtyIds = listOf(specialty.id))
                val consolidatedSpecialist = buildConsolidatedAccreditedNetwork(
                    id = favorite.referenceId,
                    type = HEALTH_PROFESSIONAL,
                    specialtyIds = listOf(specialty.id)
                )

                coEvery { memberService.getCurrent(personId) } returns member.success()

                coEvery {
                    healthCareTeamService.getHealthcareTeamByPerson(personId)
                } returns healthCareTeam.success()

                coEvery {
                    healthProfessionalService.findByStaffId(
                        healthCareTeam.physicianStaffId,
                        FindOptions(withStaff = false, withContact = true)
                    )
                } returns hp.success()

                coEvery { personService.get(personId) } returns person.success()
                coEvery { accreditedNetworkFavoriteService.findByPersonId(personId) } returns listOf(favorite).success()

                coEvery {
                    consolidatedAccreditedNetworkService.findBy(
                        ConsolidatedAccreditedNetworkService.Filter(
                            referencedIds = listOf(favorite.referenceId)
                        )
                    )
                } returns listOf(consolidatedSpecialist).success()

                coEvery { medicalSpecialtyService.getByIds(favorite.specialtyIds) } returns listOf(specialty).success()

                val expectedHighlight = ProviderTransport(
                    id = hp.id,
                    staffId = hp.staffId,
                    type = hp.type.toConsolidatedType(),
                    name = hp.name,
                    title = hp.name,
                    subtitle = "Pediatra de ${person.firstName}",
                    subtitleInfo = ProviderTransport.SubtitleInfo(
                        text = "Pediatra de ${person.firstName}",
                        colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.MAGENTA,
                    ),
                    description = null,
                    imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg",
                    tag = Tag(
                        text = "Pediatra de ${person.firstName}",
                        colorScheme = TagColorScheme.MAGENTA,
                    ),
                    captions = emptyList(),
                    distance = null,
                    deAccreditationDate = null,
                    hasHospitalHealthTeam = false,
                    navigation = Navigation(
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/healthcare_team/physician/${hp.id}?lat=-23.5718116&lng=-46.**************",
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                    ),
                    action = null,
                    subSpecialtyIds = hp.subSpecialtyIds,
                )


                val consolidatedSpecialistTransport = consolidatedSpecialist.toConsolidatedSpecialistTransport()
                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = consolidatedSpecialistTransport.id,
                        staffId = consolidatedSpecialistTransport.staffId,
                        type = consolidatedSpecialistTransport.type,
                        name = consolidatedSpecialistTransport.name,
                        title = consolidatedSpecialistTransport.name,
                        subtitle = specialty.name,
                        subtitleInfo = ProviderTransport.SubtitleInfo(
                            text = specialty.name,
                            colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.GRAY,
                        ),
                        description = null,
                        imageUrl = consolidatedSpecialistTransport.imageUrl,
                        tag = Tag(
                            text = "Previsão de Agenda: 3 dias",
                            colorScheme = TagColorScheme.GRAY,
                        ),
                        captions = listOf(Caption(text = "0 m", icon = "local_pin")),
                        distance = 0.0,
                        deAccreditationDate = null,
                        hasHospitalHealthTeam = false,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/specialists/${consolidatedSpecialistTransport.id}?lat=-23.5718116&lng=-46.**************",
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        ),
                        action = null,
                        subSpecialtyIds = consolidatedSpecialistTransport.subSpecialtyIds,
                        gender = consolidatedSpecialistTransport.gender,
                        appointmentTypes = consolidatedSpecialistTransport.appointmentTypes,
                        scheduleAvailabilityDays = consolidatedSpecialistTransport.scheduleAvailabilityDays,
                        isFavorite = true,
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_TITLE,
                        description = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_DESCRIPTION,
                        action = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_ACTION,
                    ),
                    emptyListTitle = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_TITLE,
                    emptyListDescription = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_DESCRIPTION,
                    action = AccreditedNetworkInternalService.FAVORITES_EMPTY_LIST_ACTION,
                    title = "Favoritos",
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = null,
                    otherSectionTitle = "Favoritos",
                    highlights = listOf(expectedHighlight),
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            type = ProviderSectionType.HIGHLIGHTS,
                            title = "Pediatra de ${person.firstName}",
                            description = null,
                            items = listOf(expectedHighlight),
                        ),
                        ProviderSection(
                            title = "Favoritos",
                            description = null,
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                )

                val result = service.getFavorites(
                    personId,
                    consolidatedSpecialist.latitude!!,
                    consolidatedSpecialist.longitude!!,
                    SemanticVersion("4.0.0")
                )

                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(personId) }
                coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(personId) }
                coVerifyOnce {
                    healthProfessionalService.findByStaffId(
                        healthCareTeam.physicianStaffId,
                        FindOptions(withStaff = false, withContact = true)
                    )
                }
                coVerifyOnce { accreditedNetworkFavoriteService.findByPersonId(personId) }
                coVerifyOnce {
                    consolidatedAccreditedNetworkService.findBy(
                        ConsolidatedAccreditedNetworkService.Filter(
                            referencedIds = listOf(favorite.referenceId)
                        )
                    )
                }
                coVerifyOnce { medicalSpecialtyService.getByIds(favorite.specialtyIds) }
            }
        }

        @TestInstance(TestInstance.Lifecycle.PER_CLASS)
        @Nested
        inner class CallOutTests {
            private val subSpecialty = buildMedicalSpecialty(name = "Pes")
            val appVersion = SemanticVersion("4.0.0")
            private val callout = CalloutSection(
                title = "Quer agendar um atendimento?",
                calloutBody = "Para agendar com essa especialidade você precisa de indicação médica.",
                calloutVariant = CalloutVariant.INFORMATION,
                calloutAction = CalloutAction(
                    type = CalloutType.NAVIGATION,
                    label = "Falar com Alice Agora",
                    onClickAction = br.com.alice.app.content.model.RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
                )
            )

            private val aliceProviderResponse = ProviderResponse(
                providersType = "HEALTH_PROFESSIONAL",
                providersSubtypeId = specialtyId.toString(),
                emptyContent = ProviderResponse.EmptyContentTransport(
                    title = SPECIALIST_EMPTY_LIST_TITLE,
                    description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    action = talkToAliceAgoraAction
                ),
                emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                title = "Ortopedia",
                otherSectionTitle = "Outros Especialistas e Clínicas",
                action = talkToAliceAgoraAction,
                isDuquesa = false,
                subSpecialties = listOf(
                    SubSpecialtyTransport(
                        id = subSpecialty.id,
                        name = subSpecialty.name,
                    )
                )
            )
            private val duquesaProviderResponse = aliceProviderResponse.copy(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    title = SPECIALIST_EMPTY_LIST_TITLE,
                    description = SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                    action = talkToAliceAgoraAction,
                ),
                emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                action = talkToAliceAgoraAction,
                isDuquesa = true
            )

            private val medicalSubSpecialty = buildMedicalSpecialty(
                id = specialtyId,
                type = MedicalSpecialtyType.SPECIALTY
            )
            private val medicalSpecialty = buildMedicalSpecialty(
                id = subSpecialtyId,
                type = MedicalSpecialtyType.SUBSPECIALTY
            )
            val filter = Filter(
                active = true,
                internal = false,
                parentSpecialtyId = specialtyId,
            )
            private val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                specialty = ReferralSpecialty(name = medicalSpecialty.name, medicalSpecialty.id)
            )

            private fun testParameters() = listOf(
                arrayOf(
                    "return ProviderResponse with callout for Duquesa member when there is no referral task",
                    duquesaMember,
                    emptyList<HealthPlanTask>(),
                    duquesaProviderResponse.copy(callout = callout),
                    medicalSpecialty
                ),
                arrayOf(
                    "return ProviderResponse without callout for Duquesa member when there is a referral task",
                    duquesaMember,
                    listOf(healthPlanTask),
                    duquesaProviderResponse,
                    medicalSpecialty
                ),
                arrayOf(
                    "return ProviderResponse with callout for Alice member when there is target speciality and there is no referral task",
                    member,
                    emptyList<HealthPlanTask>(),
                    aliceProviderResponse.copy(callout = callout),
                    medicalSpecialty.copy(isTherapy = true)
                ),
                arrayOf(
                    "return ProviderResponse without callout for Alice member when there is target speciality and there is referral task",
                    member,
                    listOf(healthPlanTask),
                    aliceProviderResponse,
                    medicalSpecialty.copy(isTherapy = true)
                )
            )

            @ParameterizedTest(name = "{0}")
            @MethodSource("testParameters")
            fun `#getSpecialists - should return ProviderResponse with correct callout value`(
                testName: String,
                member: Member,
                healthPlanTasks: List<HealthPlanTask>,
                providerResponse: ProviderResponse,
                medicalSpecialty: MedicalSpecialty
            ) = runBlocking {
                withFeatureFlag(
                    FeatureNamespace.MEMBERSHIP,
                    "has_duquesa_member_migrate_to_alice_app",
                    true
                ) {
                    val specialistRequestByProduct = SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )

                    coEvery { memberService.getCurrent(member.personId) } returns member.success()
                    coEvery {
                        consolidatedAccreditedSpecialistNetworkService.getSpecialist(specialistRequestByProduct)
                    } returns emptyList<ConsolidatedSpecialistLightTransport>().success()
                    coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
                    coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns medicalSubSpecialty.success()
                    coEvery { medicalSpecialtyService.getById(specialtyId) } returns medicalSpecialty.success()
                    coEvery { medicalSpecialtyService.findBy(filter = filter, range = null) } returns listOf(
                        subSpecialty
                    ).success()
                    coEvery {
                        accreditedNetworkFavoriteService.findByPersonId(
                            person.id,
                            AccreditedNetworkFavoriteService.Filter(specialtyId = specialtyId)
                        )
                    } returns emptyList<AccreditedNetworkFavorite>().success()
                    coEvery {
                        healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                            member.personId,
                            HealthPlanTaskType.REFERRAL
                        )
                    } returns healthPlanTasks.success()

                    val result =
                        service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
                    assertThat(result).isSuccessWithData(providerResponse)

                    coVerifyOnce { memberService.getCurrent(any()) }
                    coVerifyOnce { kafkaProducerService.produce(any()) }
                    coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                    coVerifyOnce { healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(any(), any()) }
                    coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
                }
            }

            private fun testParametersWithoutCallout() = listOf(
                arrayOf(
                    "return ProviderResponse without callout for Duquesa member when hasDuquesaMemberMigrateToAliceApp flag is off",
                    duquesaMember,
                    duquesaProviderResponse.copy(
                        emptyContent = ProviderResponse.EmptyContentTransport(
                            title = SPECIALIST_EMPTY_LIST_TITLE,
                            description = SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                            action = duquesaEmptyProvidersListAction
                        ),
                        action = duquesaEmptyProvidersListAction
                    ),
                    false,
                    emptyList<String>(),
                    emptyList<String>()
                ),
                arrayOf(
                    "return ProviderResponse without callout for Alice member when there is no target speciality",
                    member,
                    aliceProviderResponse,
                    false,
                    emptyList<String>(),
                    emptyList<String>()
                ),
                arrayOf(
                    "return ProviderResponse without callout for Duquesa member when is MfcSpecialtyId",
                    duquesaMember,
                    duquesaProviderResponse,
                    true,
                    listOf(specialtyId.toString()),
                    emptyList<String>()
                ),
                arrayOf(
                    "return ProviderResponse without callout for Duquesa member when speciality is not gatekeeper",
                    duquesaMember,
                    duquesaProviderResponse,
                    true,
                    emptyList<String>(),
                    listOf(medicalSpecialty.id.toString())
                )
            )

            @ParameterizedTest(name = "{0}")
            @MethodSource("testParametersWithoutCallout")
            fun `#getSpecialists - should return ProviderResponse without callout`(
                testName: String,
                member: Member,
                providerResponse: ProviderResponse,
                hasDuquesaMemberMigrateToAliceApp: Boolean,
                mfcSpecialtyIds: List<String>,
                specialtiesWithoutDuquesaGatekeeper: List<String>
            ) = runBlocking {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP,
                    mapOf(
                        "has_duquesa_member_migrate_to_alice_app" to hasDuquesaMemberMigrateToAliceApp,
                        "mfc_specialty_ids" to mfcSpecialtyIds,
                        "specialties_without_duquesa_gatekeeper" to specialtiesWithoutDuquesaGatekeeper
                    )
                ) {
                    val specialistRequestByProduct = SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                    )
                    val healthCareTeamPhysician = buildHealthcareTeamPhysician()
                    val healthCareTeam = buildHealthcareTeam(physicianStaffId = healthCareTeamPhysician.id)

                    coEvery { memberService.getCurrent(member.personId) } returns member.success()
                    coEvery {
                        consolidatedAccreditedSpecialistNetworkService.getSpecialist(specialistRequestByProduct)
                    } returns emptyList<ConsolidatedSpecialistLightTransport>().success()
                    coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()
                    coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns medicalSubSpecialty.success()
                    coEvery { medicalSpecialtyService.getById(specialtyId) } returns medicalSpecialty.success()
                    coEvery { medicalSpecialtyService.findBy(filter = filter, range = null) } returns listOf(
                        subSpecialty
                    ).success()
                    coEvery {
                        accreditedNetworkFavoriteService.findByPersonId(
                            person.id,
                            AccreditedNetworkFavoriteService.Filter(specialtyId = specialtyId)
                        )
                    } returns emptyList<AccreditedNetworkFavorite>().success()
                    coEvery {
                        healthCareTeamService.getHealthcareTeamByPerson(member.personId)
                    } returns healthCareTeam.success()

                    val result =
                        service.getSpecialists(person.id, specialtyId, subSpecialtyId, null, lat, lng, appVersion)
                    assertThat(result).isSuccessWithData(providerResponse)

                    coVerifyOnce { memberService.getCurrent(any()) }
                    coVerifyOnce { kafkaProducerService.produce(any()) }
                    coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                    coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
                    coVerifyNone { healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(any(), any()) }
                }
            }
        }
    }

    @Nested
    inner class GetProvidersByHealthPlanTask {
        private val specialtyId = RangeUUID.generate()
        private val healthPlanTaskId = RangeUUID.generate()

        @Test
        fun `#Should return providers ProviderResponse for TEST_REQUEST HealthPlanTask`() = runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskTestRequest()
            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Lab 1",
                type = LABORATORY,
                address = structuredAddress,
                imageUrl = "provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Lab 2",
                id = RangeUUID.generate(),
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Lab 3",
                id = RangeUUID.generate(),
            )

            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnit1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnit2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit3.imageUrl,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnit3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = "Laboratórios",
                otherSectionTitle = "Outros Laboratórios",
                providersType = "LABS",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    )
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId,
                        types = listOf(LABORATORY),
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()

            val result =
                service.getProvidersByHealthPlanTask(person.id, healthPlanTaskId, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
        }

        @Test
        fun `given a labi provider when get provider by health task then return highlighted provider`() = runBlocking {
            val labiProvider = RangeUUID.generate()
            val procedureCode = "98041087"

            withFeatureFlags(
                FeatureNamespace.ALICE_APP to mapOf("labi_providers_ids" to listOf(labiProvider)),
                FeatureNamespace.ALICE_APP to mapOf("labi_procedure_codes" to listOf(procedureCode))
            ) {
                val healthPlanTask = TestModelFactory.buildHealthPlanTaskTestRequest(
                    content = mapOf("code" to "98041087")
                )

                val providerUnitLabi = ConsolidatedProviderLightTransport(
                    id = RangeUUID.generate(),
                    name = "Labi",
                    imageUrl = "provider.com.br/logo.png",
                    distance = distance,
                    address = structuredAddress,
                    type = LABORATORY,
                    providerId = labiProvider
                )

                val otherProviderUnit = ConsolidatedProviderLightTransport(
                    id = RangeUUID.generate(),
                    name = "Crazy Hat Lab",
                    imageUrl = "provider.com.br/logo.png",
                    distance = distance,
                    address = structuredAddress,
                    type = LABORATORY,
                    providerId = RangeUUID.generate()
                )

                val expectedProviderItems = mutableMapOf<String, ProviderTransport>()

                expectedProviderItems["labi"] = ProviderTransport(
                    id = providerUnitLabi.id,
                    name = providerUnitLabi.name,
                    title = providerUnitLabi.name,
                    subtitle = "Menor coparticipação",
                    subtitleInfo = ProviderTransport.SubtitleInfo(
                        text = "Menor coparticipação",
                        colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.GREEN
                    ),
                    description = providerUnitLabi.address?.formattedAddress(),
                    tag = Tag(text = "Indicado para você", colorScheme = TagColorScheme.MAGENTA),
                    imageUrl = providerUnitLabi.imageUrl,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnitLabi.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&is_exam_redirect=true"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption(text = "Coleta domiciliar ou presencial", icon = "home_outlined")),
                    isExamRedirect = true
                )

                expectedProviderItems["other"] = ProviderTransport(
                    id = otherProviderUnit.id,
                    name = otherProviderUnit.name,
                    title = otherProviderUnit.name,
                    subtitle = null,
                    description = otherProviderUnit.address?.formattedAddress(),
                    tag = null,
                    imageUrl = otherProviderUnit.imageUrl,
                    type = LABORATORY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${LABORATORY}/${otherProviderUnit.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        action = talkToAliceAgoraAction,
                    ),
                    highlights = listOf(expectedProviderItems["labi"]!!),
                    items = listOf(expectedProviderItems["other"]!!),
                    action = talkToAliceAgoraAction,
                    title = "Laboratórios",
                    otherSectionTitle = "Outros Laboratórios",
                    providersType = "LABS",
                    providers = listOf(
                        ProviderSection(
                            title = null,
                            description = null,
                            items = listOf(expectedProviderItems["labi"]!!),
                            type = ProviderSectionType.HIGHLIGHTS,
                        ),
                        ProviderSection(
                            title = "Todos os laboratórios",
                            type = ProviderSectionType.STANDARD,
                            items = listOf(expectedProviderItems["other"]!!),
                            description = null,
                        )
                    ),
                    isDuquesa = false,
                )

                coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                        UnitsRequest(
                            productId = member.productId,
                            types = listOf(LABORATORY),
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000
                        )
                    )
                } returns listOf(providerUnitLabi, otherProviderUnit).success()

                coEvery {
                    labiIntegrationService.shouldShowHighlight(
                        lat = lat.toDouble(),
                        lng = lng.toDouble(),
                        member = member
                    )
                } returns true

                val result =
                    service.getProvidersByHealthPlanTask(
                        person.id,
                        healthPlanTaskId,
                        lat,
                        lng,
                        SemanticVersion("5.0.0")
                    )

                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
            }
        }

        @Test
        fun `given a task with not supported code provider when get provider by health task then return standard providers`() =
            runBlocking {
                val labiProvider = RangeUUID.generate()

                withFeatureFlags(
                    FeatureNamespace.ALICE_APP to mapOf("labi_providers_ids" to listOf(labiProvider)),
                    FeatureNamespace.ALICE_APP to mapOf("labi_procedure_codes" to listOf("98041087"))
                ) {
                    val healthPlanTask = TestModelFactory.buildHealthPlanTaskTestRequest(
                        content = mapOf("code" to "98041088")
                    )

                    val providerUnitLabi = ConsolidatedProviderLightTransport(
                        id = RangeUUID.generate(),
                        name = "Labi",
                        imageUrl = "provider.com.br/logo.png",
                        distance = distance,
                        address = structuredAddress,
                        type = LABORATORY,
                        providerId = labiProvider
                    )

                    val otherProviderUnit = ConsolidatedProviderLightTransport(
                        id = RangeUUID.generate(),
                        name = "Crazy Hat Lab",
                        imageUrl = "provider.com.br/logo.png",
                        distance = distance,
                        address = structuredAddress,
                        type = LABORATORY,
                        providerId = RangeUUID.generate()
                    )

                    val expectedProviderItems = mutableMapOf<String, ProviderTransport>()

                    expectedProviderItems["labi"] = ProviderTransport(
                        id = providerUnitLabi.id,
                        name = providerUnitLabi.name,
                        title = providerUnitLabi.name,
                        subtitle = null,
                        description = providerUnitLabi.address?.formattedAddress(),
                        tag = null,
                        imageUrl = providerUnitLabi.imageUrl,
                        type = LABORATORY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${LABORATORY}/${providerUnitLabi.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption(text = "1 km", icon = "local_pin")),
                    )

                    expectedProviderItems["other"] = ProviderTransport(
                        id = otherProviderUnit.id,
                        name = otherProviderUnit.name,
                        title = otherProviderUnit.name,
                        subtitle = null,
                        description = otherProviderUnit.address?.formattedAddress(),
                        tag = null,
                        imageUrl = otherProviderUnit.imageUrl,
                        type = LABORATORY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${LABORATORY}/${otherProviderUnit.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    )

                    val expectedResponse = ProviderResponse(
                        emptyContent = ProviderResponse.EmptyContentTransport(
                            action = talkToAliceAgoraAction,
                        ),
                        highlights = emptyList(),
                        items = listOf(expectedProviderItems["labi"]!!, expectedProviderItems["other"]!!),
                        action = talkToAliceAgoraAction,
                        title = "Laboratórios",
                        otherSectionTitle = "Outros Laboratórios",
                        providersType = "LABS",
                        providers = listOf(
                            ProviderSection(
                                title = null,
                                type = ProviderSectionType.STANDARD,
                                items = listOf(expectedProviderItems["labi"]!!, expectedProviderItems["other"]!!),
                                description = null,
                            )
                        ),
                        isDuquesa = false,
                    )

                    coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                    coEvery { memberService.getCurrent(member.personId) } returns member.success()
                    coEvery {
                        consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                            UnitsRequest(
                                productId = member.productId,
                                types = listOf(LABORATORY),
                                lat = lat,
                                lng = lng,
                                rangeInMeters = 50_000
                            )
                        )
                    } returns listOf(providerUnitLabi, otherProviderUnit).success()

                    coEvery {
                        labiIntegrationService.shouldShowHighlight(
                            lat = lat.toDouble(),
                            lng = lng.toDouble(),
                            member = member
                        )
                    } returns true

                    val result =
                        service.getProvidersByHealthPlanTask(
                            person.id,
                            healthPlanTaskId,
                            lat,
                            lng,
                            SemanticVersion("5.0.0")
                        )

                    assertThat(result).isSuccessWithData(expectedResponse)

                    coVerifyOnce { memberService.getCurrent(any()) }
                    coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
                }
            }

        @Test
        fun `#Should return specialists ProviderResponse for REFERRAL HealthPlanTask`() = runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                specialty = ReferralSpecialty(
                    name = "specialty",
                    id = specialtyId
                )
            )

            val specialist1 = ConsolidatedSpecialistLightTransport(
                id = RangeUUID.generate(),
                name = "Specialist 1",
                address = buildStructuredAddress(),
                imageUrl = "http://especialista.com.br/logo.png",
                distance = distance,
                type = SPECIALIST_HEALTH_PROFESSIONAL,
                gender = null,
            )
            val specialist2 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 2",
                type = CASSI_SPECIALIST
            )
            val specialist3 = specialist1.copy(
                id = RangeUUID.generate(),
                name = "Specialist 3",
                type = CLINICAL_COMMUNITY
            )

            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = specialist1.id,
                    name = specialist1.name,
                    title = specialist1.name,
                    subtitle = null,
                    description = specialist1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist1.imageUrl,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = specialist2.id,
                    name = specialist2.name,
                    title = specialist2.name,
                    subtitle = null,
                    description = specialist2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist2.imageUrl,
                    type = CASSI_SPECIALIST,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ), ProviderTransport(
                    id = specialist3.id,
                    name = specialist3.name,
                    title = specialist3.name,
                    subtitle = null,
                    description = specialist3.address?.formattedAddress(),
                    tag = null,
                    imageUrl = specialist3.imageUrl,
                    type = CLINICAL_COMMUNITY,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    title = SPECIALIST_EMPTY_LIST_TITLE,
                    description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = "specialty",
                otherSectionTitle = "Outros Especialistas e Clínicas",
                providersType = "HEALTH_PROFESSIONAL",
                providersSubtypeId = specialtyId.toString(),
                emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    )
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")
            coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery {
                consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                    SpecialistRequestByProduct(
                        productId = member.productId,
                        specialtyId = specialtyId,
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000,
                        subSpecialtyId = null,
                        suggestedSpecialist = healthPlanTask.specialize<Referral>().suggestedSpecialist!!
                    )
                )
            } returns listOf(specialist1, specialist2, specialist3).success()
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                name = "specialty",
                id = specialtyId,
            ).success()
            coEvery {
                medicalSpecialtyService.findBy(
                    filter = Filter(
                        active = true,
                        internal = false,
                        parentSpecialtyId = specialtyId,
                    ),
                    range = null,
                )
            } returns emptyList<MedicalSpecialty>().success()
            coEvery {
                accreditedNetworkFavoriteService.findByPersonId(
                    person.id,
                    AccreditedNetworkFavoriteService.Filter(
                        specialtyId = specialtyId,
                    )
                )
            } returns emptyList<AccreditedNetworkFavorite>().success()

            val result =
                service.getProvidersByHealthPlanTask(person.id, healthPlanTaskId, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
        }

        @Test
        fun `#Should return specialists ProviderResponse for REFERRAL HealthPlanTask and sub specialty is Generalista`() =
            runBlocking {
                val subSpecialtyId = RangeUUID.generate()

                val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                    specialty = ReferralSpecialty(
                        name = "specialty",
                        id = specialtyId,
                    ),
                    subSpecialty = ReferralSpecialty(
                        id = subSpecialtyId,
                        name = "Generalista",
                    )
                )
                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    gender = null,
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    type = CASSI_SPECIALIST
                )
                val specialist3 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    type = CLINICAL_COMMUNITY
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = specialist1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = specialist2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist2.imageUrl,
                        type = CASSI_SPECIALIST,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                        isFavorite = true,
                    ), ProviderTransport(
                        id = specialist3.id,
                        name = specialist3.name,
                        title = specialist3.name,
                        subtitle = null,
                        description = specialist3.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist3.imageUrl,
                        type = CLINICAL_COMMUNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    title = "specialty",
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialtyId.toString(),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = null,
                            description = null,
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                )
                val appVersion = SemanticVersion("4.0.0")

                coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = specialtyId,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000,
                            subSpecialtyId = null,
                            suggestedSpecialist = healthPlanTask.specialize<Referral>().suggestedSpecialist!!
                        )
                    )
                } returns listOf(specialist1, specialist2, specialist3).success()
                coEvery { medicalSpecialtyService.getById(subSpecialtyId) } returns buildMedicalSpecialty(
                    id = subSpecialtyId,
                    name = "Generalista",
                    type = MedicalSpecialtyType.SUBSPECIALTY
                ).success()
                coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                    id = specialtyId,
                    name = "specialty",
                    type = MedicalSpecialtyType.SPECIALTY
                ).success()
                coEvery {
                    medicalSpecialtyService.findBy(
                        filter = Filter(
                            active = true,
                            internal = false,
                            parentSpecialtyId = specialtyId,
                        ),
                        range = null,
                    )
                } returns emptyList<MedicalSpecialty>().success()
                coEvery {
                    accreditedNetworkFavoriteService.findByPersonId(
                        person.id,
                        AccreditedNetworkFavoriteService.Filter(
                            specialtyId = specialtyId,
                        )
                    )
                } returns listOf(
                    buildAccreditedNetworkFavorite(
                        personId = person.id,
                        referenceId = specialist2.id,
                        specialtyIds = listOf(specialtyId)
                    )
                ).success()

                val result =
                    service.getProvidersByHealthPlanTask(
                        person.id,
                        healthPlanTaskId,
                        lat,
                        lng,
                        appVersion
                    )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerify(exactly = 4) { medicalSpecialtyService.getById(any()) }
            }

        @Test
        fun `#Should return specialists ProviderResponse for REFERRAL HealthPlanTask without specialty`() =
            runBlocking {
                val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral()

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        action = talkToAliceAgoraAction,
                    ),
                    title = "Especialistas e Clínicas",
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    action = talkToAliceAgoraAction,
                    items = emptyList(),
                    isDuquesa = false,
                )
                val appVersion = SemanticVersion("4.0.0")
                coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                coEvery { memberService.getCurrent(person.id) } returns member.success()

                val result = service.getProvidersByHealthPlanTask(
                    person.id,
                    healthPlanTaskId,
                    lat,
                    lng,
                    appVersion
                )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }

                coVerifyNone { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            }

        @Test
        fun `#Should return specialists ProviderResponse for REFERRAL HealthPlanTask with suggested specialist as highlight`() =
            runBlocking {
                val appVersion = SemanticVersion("4.0.0")
                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    gender = null,
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    type = CASSI_SPECIALIST
                )
                val specialist3 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    type = CLINICAL_COMMUNITY
                )

                val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                    specialty = ReferralSpecialty(
                        name = "specialty",
                        id = specialtyId
                    ),
                    suggestedSpecialist = SuggestedSpecialist(
                        id = specialist3.id,
                        name = specialist3.name,
                        type = SpecialistType.CLINICAL_COMMUNITY,
                    )
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = specialist1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = specialist2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = specialist2.imageUrl,
                        type = CASSI_SPECIALIST,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = SPECIALIST_EMPTY_LIST_TITLE,
                        description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialtyId.toString(),
                    emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                    emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                    title = "Oftalmologia",
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    highlights = listOf(
                        ProviderTransport(
                            id = specialist3.id,
                            name = specialist3.name,
                            title = specialist3.name,
                            subtitle = "Indicado para você",
                            subtitleInfo = ProviderTransport.SubtitleInfo(
                                text = "Indicado para você",
                                colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.MAGENTA
                            ),
                            description = specialist3.address?.formattedAddress(),
                            tag = recommendedTag,
                            imageUrl = specialist3.imageUrl,
                            type = CLINICAL_COMMUNITY,
                            navigation = Navigation(
                                mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                                method = RemoteActionMethod.GET,
                                endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                            ),
                            distance = distance,
                            deAccreditationDate = null,
                            captions = listOf(Caption("1 km", "local_pin")),
                        ),
                    ),
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = null,
                            description = null,
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    )
                )
                coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                coEvery { memberService.getCurrent(member.personId) } returns member.success()
                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = specialtyId,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 50_000,
                            subSpecialtyId = null,
                            suggestedSpecialist = healthPlanTask.specialize<Referral>().suggestedSpecialist!!,
                        )
                    )
                } returns listOf(specialist1, specialist2, specialist3).success()
                coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                    name = "Oftalmologia",
                    id = specialtyId,
                ).success()
                coEvery {
                    medicalSpecialtyService.findBy(
                        filter = Filter(
                            active = true,
                            internal = false,
                            parentSpecialtyId = specialtyId,
                        ),
                        range = null,
                    )
                } returns emptyList<MedicalSpecialty>().success()
                coEvery {
                    accreditedNetworkFavoriteService.findByPersonId(
                        person.id,
                        AccreditedNetworkFavoriteService.Filter(
                            specialtyId = specialtyId,
                        )
                    )
                } returns emptyList<AccreditedNetworkFavorite>().success()

                val result = service.getProvidersByHealthPlanTask(
                    person.id,
                    healthPlanTaskId,
                    lat,
                    lng,
                    appVersion
                )
                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerify(exactly = 2) { medicalSpecialtyService.getById(any()) }
            }

        @Test
        fun `#Should return specialists ProviderResponse for REFERRAL HealthPlanTask with suggested specialist as highlight on providers sections with title and subtitle`() =
            runBlocking {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP,
                    mapOf(
                        "app_version_with_title_and_subtitle_on_provider_transport" to "5.0.0",
                    ),
                ) {
                    runBlocking {
                        val specialist1 = ConsolidatedSpecialistLightTransport(
                            id = RangeUUID.generate(),
                            type = SPECIALIST_HEALTH_PROFESSIONAL,
                            name = "Specialist 1",
                            address = buildStructuredAddress(),
                            imageUrl = "http://especialista.com.br/logo.png",
                            distance = distance,
                            gender = null,
                        )
                        val specialist2 = specialist1.copy(
                            id = RangeUUID.generate(),
                            name = "Specialist 2",
                            type = CASSI_SPECIALIST
                        )
                        val specialist3 = specialist1.copy(
                            id = RangeUUID.generate(),
                            name = "Specialist 3",
                            type = CLINICAL_COMMUNITY
                        )

                        val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
                            specialty = ReferralSpecialty(
                                name = "specialty",
                                id = specialtyId
                            ),
                            suggestedSpecialist = SuggestedSpecialist(
                                id = specialist3.id,
                                name = specialist3.name,
                                type = SpecialistType.CLINICAL_COMMUNITY,
                            )
                        )

                        val expectedProviderItems = listOf(
                            ProviderTransport(
                                id = specialist1.id,
                                name = specialist1.name,
                                title = specialist1.name,
                                subtitle = null,
                                description = specialist1.address?.formattedAddress(),
                                tag = null,
                                imageUrl = specialist1.imageUrl,
                                type = SPECIALIST_HEALTH_PROFESSIONAL,
                                navigation = Navigation(
                                    mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                                    method = RemoteActionMethod.GET,
                                    endpoint = "/accredited_network/specialists/${specialist1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                                ),
                                distance = distance,
                                deAccreditationDate = null,
                                captions = listOf(Caption("1 km", "local_pin")),
                            ),
                            ProviderTransport(
                                id = specialist2.id,
                                name = specialist2.name,
                                title = specialist2.name,
                                subtitle = null,
                                description = specialist2.address?.formattedAddress(),
                                tag = null,
                                imageUrl = specialist2.imageUrl,
                                type = CASSI_SPECIALIST,
                                navigation = Navigation(
                                    mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                                    method = RemoteActionMethod.GET,
                                    endpoint = "/accredited_network/providers/${CASSI_SPECIALIST}/${specialist2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                                ),
                                distance = distance,
                                deAccreditationDate = null,
                                captions = listOf(Caption("1 km", "local_pin")),
                            )
                        )

                        val expectedResponse = ProviderResponse(
                            emptyContent = ProviderResponse.EmptyContentTransport(
                                title = SPECIALIST_EMPTY_LIST_TITLE,
                                description = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                                action = talkToAliceAgoraAction,
                            ),
                            action = talkToAliceAgoraAction,
                            providersType = "HEALTH_PROFESSIONAL",
                            providersSubtypeId = specialtyId.toString(),
                            emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                            emptyListDescription = SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE,
                            title = "Oftalmologia",
                            otherSectionTitle = "Outros Especialistas e Clínicas",
                            highlights = listOf(
                                ProviderTransport(
                                    id = specialist3.id,
                                    name = specialist3.name,
                                    title = specialist3.name,
                                    subtitle = "Indicado para você",
                                    subtitleInfo = ProviderTransport.SubtitleInfo(
                                        text = "Indicado para você",
                                        colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.MAGENTA
                                    ),
                                    description = specialist3.address?.formattedAddress(),
                                    tag = null,
                                    imageUrl = specialist3.imageUrl,
                                    type = CLINICAL_COMMUNITY,
                                    navigation = Navigation(
                                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                                        method = RemoteActionMethod.GET,
                                        endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                                    ),
                                    distance = distance,
                                    deAccreditationDate = null,
                                    captions = listOf(Caption("1 km", "local_pin")),
                                ),
                            ),
                            items = expectedProviderItems,
                            providers = listOf(
                                ProviderSection(
                                    title = "Indicado para você",
                                    description = null,
                                    items = listOf(
                                        ProviderTransport(
                                            id = specialist3.id,
                                            name = specialist3.name,
                                            title = specialist3.name,
                                            subtitle = "Indicado para você",
                                            subtitleInfo = ProviderTransport.SubtitleInfo(
                                                text = "Indicado para você",
                                                colorScheme = ProviderTransport.SubtitleInfo.ColorSchema.MAGENTA
                                            ),
                                            description = specialist3.address?.formattedAddress(),
                                            tag = null,
                                            imageUrl = specialist3.imageUrl,
                                            type = CLINICAL_COMMUNITY,
                                            navigation = Navigation(
                                                mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                                                method = RemoteActionMethod.GET,
                                                endpoint = "/accredited_network/providers/${CLINICAL_COMMUNITY}/${specialist3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}&specialty_id=${specialtyId}"
                                            ),
                                            distance = distance,
                                            deAccreditationDate = null,
                                            captions = listOf(Caption("1 km", "local_pin")),
                                        ),
                                    ),
                                    type = ProviderSectionType.HIGHLIGHTS,
                                ),
                                ProviderSection(
                                    title = null,
                                    description = null,
                                    items = expectedProviderItems,
                                    type = ProviderSectionType.STANDARD,
                                )
                            ),
                            isDuquesa = false,
                        )
                        val appVersion = SemanticVersion("5.0.0")

                        coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
                        coEvery { memberService.getCurrent(member.personId) } returns member.success()
                        coEvery {
                            consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                                SpecialistRequestByProduct(
                                    productId = member.productId,
                                    specialtyId = specialtyId,
                                    lat = lat,
                                    lng = lng,
                                    rangeInMeters = 50_000,
                                    subSpecialtyId = null,
                                    suggestedSpecialist = healthPlanTask.specialize<Referral>().suggestedSpecialist!!,
                                )
                            )
                        } returns listOf(specialist1, specialist2, specialist3).success()
                        coEvery { medicalSpecialtyService.getById(specialtyId) } returns buildMedicalSpecialty(
                            name = "Oftalmologia",
                            id = specialtyId,
                        ).success()
                        coEvery {
                            medicalSpecialtyService.findBy(
                                filter = Filter(
                                    active = true,
                                    internal = false,
                                    parentSpecialtyId = specialtyId,
                                ),
                                range = null,
                            )
                        } returns emptyList<MedicalSpecialty>().success()
                        coEvery {
                            accreditedNetworkFavoriteService.findByPersonId(
                                person.id,
                                AccreditedNetworkFavoriteService.Filter(
                                    specialtyId = specialtyId,
                                )
                            )
                        } returns emptyList<AccreditedNetworkFavorite>().success()


                        val result = service.getProvidersByHealthPlanTask(
                            person.id,
                            healthPlanTaskId,
                            lat,
                            lng,
                            appVersion
                        )
                        assertThat(result).isSuccessWithData(expectedResponse)

                        coVerifyOnce { memberService.getCurrent(any()) }
                        coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                        coVerify(exactly = 2) { medicalSpecialtyService.getById(any()) }
                    }
                }
            }

        @Test
        fun `#Should return providers ProviderResponse for EMERGENCY HealthPlanTask`() = runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskEmergency()

            val notChildrenPerson = person.copy(dateOfBirth = LocalDateTime.now().minusYears(20))

            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Pronto-socorro 1",
                type = EMERGENCY_UNITY,
                address = structuredAddress,
                imageUrl = "http://provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
                providerId = RangeUUID.generate(),
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Pronto-socorro 2",
                id = RangeUUID.generate(),
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Pronto-socorro 3",
                id = RangeUUID.generate(),
            )

            val expectedProviderItems =
                listOf(
                    ProviderTransport(
                        id = providerUnit1.id,
                        name = providerUnit1.name,
                        title = providerUnit1.name,
                        subtitle = null,
                        description = providerUnit1.address?.formattedAddress(),
                        tag = null,
                        imageUrl = providerUnit1.imageUrl,
                        type = EMERGENCY_UNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${EMERGENCY_UNITY}/${providerUnit1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    ),
                    ProviderTransport(
                        id = providerUnit2.id,
                        name = providerUnit2.name,
                        title = providerUnit2.name,
                        subtitle = null,
                        description = providerUnit2.address?.formattedAddress(),
                        tag = null,
                        imageUrl = providerUnit2.imageUrl,
                        type = EMERGENCY_UNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${EMERGENCY_UNITY}/${providerUnit2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    ), ProviderTransport(
                        id = providerUnit3.id,
                        name = providerUnit3.name,
                        title = providerUnit3.name,
                        subtitle = null,
                        description = providerUnit3.address?.formattedAddress(),
                        tag = null,
                        imageUrl = providerUnit3.imageUrl,
                        type = EMERGENCY_UNITY,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                            method = RemoteActionMethod.GET,
                            endpoint = "/accredited_network/providers/${EMERGENCY_UNITY}/${providerUnit3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = listOf(Caption("1 km", "local_pin")),
                    )
                )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = "Pronto-Socorros",
                otherSectionTitle = "Outros Pronto-Socorros",
                providersType = "HOSPITAL",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    )
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery { personService.get(person.id) } returns notChildrenPerson.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId,
                        types = listOf(EMERGENCY_UNITY),
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()

            val result =
                service.getProvidersByHealthPlanTask(person.id, healthPlanTaskId, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
        }

        @Test
        fun `#Should return providers ProviderResponse for EMERGENCY HealthPlanTask for Children`() = runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskEmergency()

            val childrenPerson = person.copy(dateOfBirth = LocalDateTime.now().minusYears(10))

            val providerUnit1 = ConsolidatedProviderLightTransport(
                name = "Pronto-socorro 1",
                type = EMERGENCY_UNITY_CHILDREN,
                address = structuredAddress,
                imageUrl = "http://provider.com.br/logo.png",
                id = RangeUUID.generate(),
                distance = distance,
            )
            val providerUnit2 = providerUnit1.copy(
                name = "Pronto-socorro 2",
                id = RangeUUID.generate(),
            )
            val providerUnit3 = providerUnit1.copy(
                name = "Pronto-socorro 3",
                id = RangeUUID.generate(),
            )

            val expectedProviderItems = listOf(
                ProviderTransport(
                    id = providerUnit1.id,
                    name = providerUnit1.name,
                    title = providerUnit1.name,
                    subtitle = null,
                    description = providerUnit1.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit1.imageUrl,
                    type = EMERGENCY_UNITY_CHILDREN,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${EMERGENCY_UNITY_CHILDREN}/${providerUnit1.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ),
                ProviderTransport(
                    id = providerUnit2.id,
                    name = providerUnit2.name,
                    title = providerUnit2.name,
                    subtitle = null,
                    description = providerUnit2.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit2.imageUrl,
                    type = EMERGENCY_UNITY_CHILDREN,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${EMERGENCY_UNITY_CHILDREN}/${providerUnit2.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                ), ProviderTransport(
                    id = providerUnit3.id,
                    name = providerUnit3.name,
                    title = providerUnit3.name,
                    subtitle = null,
                    description = providerUnit3.address?.formattedAddress(),
                    tag = null,
                    imageUrl = providerUnit3.imageUrl,
                    type = EMERGENCY_UNITY_CHILDREN,
                    navigation = Navigation(
                        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
                        method = RemoteActionMethod.GET,
                        endpoint = "/accredited_network/providers/${EMERGENCY_UNITY_CHILDREN}/${providerUnit3.id}?lat=${lat}&lng=${lng}&health_plan_task_id=${healthPlanTask.id}"
                    ),
                    distance = distance,
                    deAccreditationDate = null,
                    captions = listOf(Caption("1 km", "local_pin")),
                )
            )

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                action = talkToAliceAgoraAction,
                title = "Pronto-Socorros Infantis",
                otherSectionTitle = "Outros Pronto-Socorros Infantis",
                providersType = "HOSPITAL",
                items = expectedProviderItems,
                providers = listOf(
                    ProviderSection(
                        title = null,
                        description = null,
                        items = expectedProviderItems,
                        type = ProviderSectionType.STANDARD,
                    )
                ),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
            coEvery { memberService.getCurrent(member.personId) } returns member.success()
            coEvery { personService.get(person.id) } returns childrenPerson.success()
            coEvery {
                consolidatedAccreditedProvidersNetworkService.getProviderUnits(
                    UnitsRequest(
                        productId = member.productId,
                        types = listOf(EMERGENCY_UNITY_CHILDREN),
                        lat = lat,
                        lng = lng,
                        rangeInMeters = 50_000
                    )
                )
            } returns listOf(providerUnit1, providerUnit2, providerUnit3).success()

            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "children_age_threshold", 12) {
                val result =
                    service.getProvidersByHealthPlanTask(person.id, healthPlanTaskId, lat, lng, appVersion)
                assertThat(result).isSuccessWithData(expectedResponse)
            }

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
        }

        @Test
        fun `#Should return empty ProviderResponse for others HealthPlanTask`() = runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTaskPrescription()

            val expectedResponse = ProviderResponse(
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = talkToAliceAgoraAction,
                ),
                title = "Rede Credenciada",
                otherSectionTitle = "Outros Especialistas e Clínicas",
                action = talkToAliceAgoraAction,
                items = emptyList(),
                isDuquesa = false,
            )
            val appVersion = SemanticVersion("4.0.0")

            coEvery { healthPlanTaskService.get(healthPlanTaskId) } returns healthPlanTask.success()
            coEvery { memberService.getCurrent(person.id) } returns member.success()

            val result =
                service.getProvidersByHealthPlanTask(person.id, healthPlanTaskId, lat, lng, appVersion)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyNone { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
            coVerifyNone { consolidatedAccreditedProvidersNetworkService.getProviderUnits(any()) }
        }
    }

    @Nested
    inner class GetProviderDetails {
        private val providerId = RangeUUID.generate()
        private val personId = person.id
        private val appVersion = SemanticVersion("4.0.0")

        private val latitude = "-23.570976575176164"
        private val longitude = "-46.65298612738142"
        val address = buildStructuredAddress()
        val addresses = listOf(address)
        val specialty = buildMedicalSpecialty()
        private val providerUnit = buildProviderUnit(
            contractOrigin = ProviderUnit.Origin.ALICE,
            type = ProviderUnit.Type.CLINICAL_COMMUNITY,
            medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialtyId = specialty.id))
        ).copy(address = address)

        @TestInstance(TestInstance.Lifecycle.PER_CLASS)
        @Nested
        inner class TestHealthProfessionalType {
            private val product = TestModelFactory.buildProduct(displayName = "Alice")
            private val subSpecialty = buildMedicalSpecialty(parentSpecialtyId = specialty.id)
            private val healthProfessional = buildHealthProfessional(
                id = providerId,
                name = "Dr. Fulano",
                specialtyId = specialty.id,
                subSpecialtyIds = listOf(subSpecialty.id),
                addressesStructured = addresses
            )
            private val providerDetails = ProviderDetailsTransport(
                id = providerId,
                type = HEALTH_PROFESSIONAL,
                title = HEALTH_PROFESSIONAL.title,
                name = healthProfessional.name,
                header = ProviderDetailsTransport.Header(
                    name = healthProfessional.name,
                ),
                council = healthProfessional.council.toString(),
                cardDescription = "Para atendimento, use a carteirinha Alice:",
                membershipCard = MemberCardResponse(
                    number = person.nationalId,
                    type = MemberCardType.ALICE,
                    ansNumber = "-",
                    productDisplayName = product.title,
                    displayName = product.displayName!!,
                    complementName = product.complementName
                ),
                specialty = specialty.name,
                subSpecialties = listOf(subSpecialty.name),
                providerImageUrl = null,
                disclaimer = null,
                addresses = listOf(
                    ProviderDetailsTransport.Address(
                        label = "Endereço",
                        address = address.formattedAddress(),
                        distance = null
                    )
                ),
                phoneNumbers = emptyList(),
                typeOfService = emptyList(),
                providerInformation = ProviderDetailsTransport.ProviderInformation(
                    imageUrl = healthProfessional.imageUrl,
                    curiosity = healthProfessional.profileBio,
                    education = healthProfessional.education,
                    qualifications = healthProfessional.qualifications.map { it.description }
                ),
                action = null,
                careCoordCard = null,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = providerId,
                    specialtyIds = listOf(specialty.id),
                    referenceType = HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            private fun testHealthProfessionalTypeParameters() = listOf(
                arrayOf(
                    "return ProviderDetailsTransport when is Alice member",
                    member,
                    providerDetails,
                    false
                ),
                arrayOf(
                    "return ProviderDetailsTransport with alice type in card when is Duquesa member",
                    member.copy(brand = Brand.DUQUESA),
                    providerDetails,
                    true
                )
            )

            @ParameterizedTest(name = "{0}")
            @MethodSource("testHealthProfessionalTypeParameters")
            fun `Return correct ProviderDetailsTransport when type is HEALTH_PROFESSIONAL`(
                testName: String,
                member: Member,
                providerDetails: ProviderDetailsTransport,
                hasDuquesaMemberMigrateToAliceApp: Boolean
            ) = runBlocking {
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )

                coEvery { personService.get(personId) } returns person.success()

                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()

                coEvery {
                    healthProfessionalService.get(
                        providerId,
                        FindOptions(withStaff = false, withContact = true)
                    )
                } returns healthProfessional.success()

                coEvery {
                    medicalSpecialtyService.getById(healthProfessional.specialtyId!!)
                } returns specialty.success()

                coEvery {
                    medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds)
                } returns listOf(subSpecialty).success()

                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns emptyList<HealthPlanTask>().success()

                coEvery {
                    accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                        person.id,
                        healthProfessional.id,
                    )
                } returns NotFoundException().failure()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = HEALTH_PROFESSIONAL,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isSuccessWithData(providerDetails)

                coVerifyOnce { healthProfessionalService.get(any(), any()) }
            }
        }

        @Test
        fun `Return correct ProviderDetailsTransport when type is CASSI_SPECIALIST`() = runBlocking {
            val cassiMember = TestModelFactory.buildCassiMember()
            val member = member.copy(cassiMember = cassiMember)
            val specialty = buildMedicalSpecialty()
            val specialist = TestModelFactory.buildCassiSpecialist(
                id = providerId,
                specialtyId = specialty.id,
                address = addresses
            )
            val providerDetails = ProviderDetailsTransport(
                id = providerId,
                type = CASSI_SPECIALIST,
                title = CASSI_SPECIALIST.title,
                name = "Iago",
                header = ProviderDetailsTransport.Header(
                    name = "Iago",
                    providerImageUrl = specialist.imageUrl,
                ),
                council = specialist.council.toString(),
                membershipCard = MemberCardResponse(
                    number = member.cassiMember!!.accountNumber!!,
                    type = MemberCardType.CASSI,
                    ansNumber = "34665-9",
                    startDate = member.cassiMember!!.startDate,
                    expirationDate = member.cassiMember!!.expirationDate,
                ),
                specialty = specialty.name,
                disclaimer = ContactCallOutResponse(
                    title = "Cobertura Rede Cassi",
                    body = """Entre em contato com esta unidade para conferir se os serviços que você precisa estão disponíveis e, se necessário, informe que seu plano é "Cassi Reciprocidade".""",
                    variant = ContactCallOutResponse.Variant.TUTORIAL
                ),
                addresses = listOf(
                    ProviderDetailsTransport.Address(
                        label = "Endereço",
                        address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                        distance = null
                    )
                ),
                phoneNumbers = emptyList(),
                typeOfService = emptyList(),
                providerInformation = null,
                action = null,
                careCoordCard = null,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = providerId,
                    specialtyIds = listOf(specialty.id),
                    referenceType = CASSI_SPECIALIST,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery { personService.get(personId) } returns person.success()
            coEvery {
                memberService.findActiveMembership(
                    personId,
                    findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                )
            } returns member.success()
            coEvery { cassiSpecialistService.get(providerId) } returns specialist.success()
            coEvery { medicalSpecialtyService.getById(specialist.specialtyId) } returns specialty.success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                    person.id,
                    HealthPlanTaskType.REFERRAL
                )
            } returns emptyList<Referral>().success()
            coEvery {
                accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                    person.id,
                    specialist.id,
                )
            } returns NotFoundException().failure()

            val result =
                service.getProviderDetails(
                    personId = personId,
                    providerType = CASSI_SPECIALIST,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

            assertThat(result).isSuccessWithData(providerDetails)
        }

        @Test
        fun `Return error when type is CASSI_SPECIALIST and member misses cassiMember`() = runBlocking {
            val member = member.copy(cassiMember = null)
            val specialty = buildMedicalSpecialty()
            val specialist = TestModelFactory.buildCassiSpecialist(
                specialtyId = specialty.id,
            )
            coEvery { personService.get(personId) } returns person.success()
            coEvery {
                memberService.findActiveMembership(
                    personId,
                    findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                )
            } returns member.success()
            coEvery { cassiSpecialistService.get(providerId) } returns specialist.success()
            coEvery { medicalSpecialtyService.getById(specialist.specialtyId) } returns specialty.success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                    person.id,
                    HealthPlanTaskType.REFERRAL
                )
            } returns emptyList<Referral>().success()

            val result =
                service.getProviderDetails(
                    personId = personId,
                    providerType = CASSI_SPECIALIST,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

            assertThat(result).isFailureOfType(NotFoundException::class)
        }

        @Test
        fun `Return correct ProviderDetailsTransport when type is CLINICAL_COMMUNITY, contract is Alice`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )
                val specialistId = RangeUUID.generate()

                val providerDetails = ProviderDetailsTransport(

                    id = providerUnit.id,
                    type = CLINICAL_COMMUNITY,
                    title = CLINICAL_COMMUNITY.title,
                    name = "Oswaldo Cruz Paulista",
                    addresses = listOf(
                        ProviderDetailsTransport.Address(
                            label = "Endereço",
                            address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                            distance = null
                        )
                    ),
                    header = ProviderDetailsTransport.Header(
                        name = "Oswaldo Cruz Paulista",
                        providerImageUrl = "http://pudim.com.br/logo.png",
                    ),
                    cardDescription = "Para atendimento, use a carteirinha Alice:",
                    membershipCard = MemberCardResponse(
                        number = "609.048.950-68",
                        type = MemberCardType.ALICE,
                        ansNumber = "-",
                        productDisplayName = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
                        displayName = "Conforto +",
                        complementName = "(Reembolso)"
                    ),
                    phoneNumbers = listOf(
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Telefone 1",
                            phoneNumber = "(011) 99999-9999",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011999999999"
                        ),
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Secundário",
                            phoneNumber = "(011) 98888-8888",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011988888888"
                        )
                    ),
                    providerImageUrl = "http://pudim.com.br/logo.png",
                    council = null,
                    specialty = "Ortopedia",
                    subSpecialties = null,
                    disclaimer = null,
                    typeOfService = null,
                    providerInformation = null,
                    action = null,
                    careCoordCard = null,
                    favoriteInfo = FavoriteInfoTransportBuilder.build(
                        referenceId = specialistId,
                        specialtyIds = emptyList(),
                        referenceType = CLINICAL_COMMUNITY,
                        isFavorite = false,
                        appVersion = appVersion,
                    )
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()
                coEvery { providerUnitService.get(providerId) } returns providerUnit.success()
                coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns listOf(healthPlanTask).success()
                coEvery {
                    accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                        person.id,
                        providerUnit.id,
                    )
                } returns NotFoundException().failure()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = CLINICAL_COMMUNITY,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isSuccessWithData(providerDetails)
            }

        @Test
        fun `Return correct ProviderDetailsTransport when type is CLINICAL_COMMUNITY, contract is Alice and there isn't specialty`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )
                val specialistId = RangeUUID.generate()
                val providerDetails = ProviderDetailsTransport(
                    id = providerUnit.id,
                    type = CLINICAL_COMMUNITY,
                    title = CLINICAL_COMMUNITY.title,
                    name = "Oswaldo Cruz Paulista",
                    addresses = listOf(
                        ProviderDetailsTransport.Address(
                            label = "Endereço",
                            address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                            distance = null
                        )
                    ),
                    header = ProviderDetailsTransport.Header(
                        name = "Oswaldo Cruz Paulista",
                        providerImageUrl = "http://pudim.com.br/logo.png",
                    ),
                    cardDescription = "Para atendimento, use a carteirinha Alice:",
                    membershipCard = MemberCardResponse(
                        number = "609.048.950-68",
                        type = MemberCardType.ALICE,
                        ansNumber = "-",
                        productDisplayName = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
                        displayName = "Conforto +",
                        complementName = "(Reembolso)"
                    ),
                    phoneNumbers = listOf(
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Telefone 1",
                            phoneNumber = "(011) 99999-9999",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011999999999"
                        ),
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Secundário",
                            phoneNumber = "(011) 98888-8888",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011988888888"
                        )
                    ),
                    providerImageUrl = "http://pudim.com.br/logo.png",
                    council = null,
                    specialty = "",
                    subSpecialties = null,
                    disclaimer = null,
                    typeOfService = null,
                    providerInformation = null,
                    action = null,
                    careCoordCard = null,
                    favoriteInfo = FavoriteInfoTransportBuilder.build(
                        referenceId = specialistId,
                        specialtyIds = emptyList(),
                        referenceType = CLINICAL_COMMUNITY,
                        isFavorite = false,
                        appVersion = appVersion,
                    )
                )
                val providerUnit1 = providerUnit.copy(medicalSpecialtyProfile = emptyList())

                coEvery { personService.get(personId) } returns person.success()
                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()
                coEvery { providerUnitService.get(providerId) } returns providerUnit1.success()

                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns listOf(healthPlanTask).success()
                coEvery {
                    accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                        person.id,
                        providerUnit1.id,
                    )
                } returns NotFoundException().failure()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = CLINICAL_COMMUNITY,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isSuccessWithData(providerDetails)

                coVerifyNone { medicalSpecialtyService.getById(any()) }
            }

        @Test
        fun `Return correct ProviderDetailsTransport when type is CLINICAL_COMMUNITY, contract is Alice, with referral`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )
                val specialistId = RangeUUID.generate()
                val providerDetails = ProviderDetailsTransport(

                    id = providerUnit.id,
                    type = CLINICAL_COMMUNITY,
                    title = CLINICAL_COMMUNITY.title,
                    name = "Oswaldo Cruz Paulista",
                    addresses = listOf(
                        ProviderDetailsTransport.Address(
                            label = "Endereço",
                            address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                            distance = null
                        )
                    ),
                    header = ProviderDetailsTransport.Header(
                        name = "Oswaldo Cruz Paulista",
                        providerImageUrl = "http://pudim.com.br/logo.png",
                    ),
                    cardDescription = "Para atendimento, use a carteirinha Alice:",
                    membershipCard = MemberCardResponse(
                        number = "609.048.950-68",
                        type = MemberCardType.ALICE,
                        ansNumber = "-",
                        productDisplayName = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
                        displayName = "Conforto +",
                        complementName = "(Reembolso)"
                    ),
                    phoneNumbers = listOf(
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Telefone 1",
                            phoneNumber = "(011) 99999-9999",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011999999999"
                        ),
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Secundário",
                            phoneNumber = "(011) 98888-8888",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011988888888"
                        )
                    ),
                    providerImageUrl = "http://pudim.com.br/logo.png",
                    council = null,
                    specialty = "Ortopedia",
                    subSpecialties = null,
                    disclaimer = null,
                    typeOfService = null,
                    providerInformation = null,
                    action = null,
                    careCoordCard = null,
                    favoriteInfo = FavoriteInfoTransportBuilder.build(
                        referenceId = specialistId,
                        specialtyIds = emptyList(),
                        referenceType = CLINICAL_COMMUNITY,
                        isFavorite = false,
                        appVersion = appVersion,
                    )
                )

                val healthPlanTaskFromSpecialty = TestModelFactory.buildHealthPlanTaskReferral(
                    specialty = ReferralSpecialty(
                        id = specialty.id,
                        name = specialty.name
                    )
                )
                val healthPlanTaskAnotherSpecialty = TestModelFactory.buildHealthPlanTaskReferral()

                coEvery { personService.get(personId) } returns person.success()
                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()
                coEvery { providerUnitService.get(providerId) } returns providerUnit.success()
                coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns listOf(healthPlanTaskFromSpecialty, healthPlanTaskAnotherSpecialty).success()
                coEvery {
                    accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                        person.id,
                        providerUnit.id,
                    )
                } returns NotFoundException().failure()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = CLINICAL_COMMUNITY,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isSuccessWithData(providerDetails)
            }

        @Test
        fun `Return correct ProviderDetailsTransport when type is other PROVIDER, contract is Alice`() = runBlocking {
            val product = TestModelFactory.buildProduct()
            val memberWithProduct = MemberWithProduct(
                member = member,
                product = product
            )
            val providerUnit = providerUnit.copy(
                contractOrigin = ProviderUnit.Origin.ALICE,
                type = ProviderUnit.Type.HOSPITAL,
                medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialtyId = specialty.id))
            )
            val providerDetails = ProviderDetailsTransport(
                id = providerUnit.id,
                type = HOSPITAL,
                title = HOSPITAL.title,
                name = "Oswaldo Cruz Paulista",
                council = null,
                cardDescription = "Para atendimento, use a carteirinha Alice:",
                membershipCard = MemberCardResponse(
                    number = "609.048.950-68",
                    type = MemberCardType.ALICE,
                    startDate = null,
                    expirationDate = null,
                    productDisplayName = product.title,
                    displayName = "Conforto +",
                    complementName = "(Reembolso)",
                    ansNumber = "-"
                ),
                header = ProviderDetailsTransport.Header(
                    name = "Oswaldo Cruz Paulista",
                    providerImageUrl = "http://pudim.com.br/logo.png",
                ),
                specialty = null,
                subSpecialties = null,
                providerImageUrl = "http://pudim.com.br/logo.png",
                disclaimer = null,
                addresses = listOf(
                    ProviderDetailsTransport.Address(
                        label = "Endereço",
                        address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                        distance = null
                    )
                ),
                phoneNumbers = listOf(
                    ProviderDetailsTransport.PhoneNumber(
                        label = "Telefone 1",
                        phoneNumber = "(011) 99999-9999",
                        type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                        phoneUrl = "tel:011999999999"
                    ),
                    ProviderDetailsTransport.PhoneNumber(
                        label = "Secundário",
                        phoneNumber = "(011) 98888-8888",
                        type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                        phoneUrl = "tel:011988888888"
                    )
                ),
                typeOfService = null,
                providerInformation = null,
                action = null,
                careCoordCard = null,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = providerUnit.id,
                    specialtyIds = listOf(specialty.id),
                    referenceType = HOSPITAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery { personService.get(personId) } returns person.success()
            coEvery {
                memberService.findActiveMembershipWithProduct(
                    personId,
                    findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                )
            } returns memberWithProduct.success()
            coEvery { providerUnitService.get(providerId) } returns providerUnit.success()
            coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                    person.id,
                    HealthPlanTaskType.REFERRAL
                )
            } returns emptyList<Referral>().success()
            coEvery {
                accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                    person.id,
                    providerUnit.id,
                )
            } returns NotFoundException().failure()

            val result = service.getProviderDetails(
                personId = personId,
                providerType = HOSPITAL,
                providerId = providerId,
                appVersion = appVersion,
                lat = latitude,
                lng = longitude,
                specialtyId = specialty.id,
            )

            assertThat(result).isSuccessWithData(providerDetails)
        }

        @Test
        fun `Return correct ProviderDetailsTransport when type is PROVIDER, contract is Cassi`() =
            runBlocking {
                val cassiMember = TestModelFactory.buildCassiMember()
                val member = member.copy(cassiMember = cassiMember)
                val product = TestModelFactory.buildProduct()
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )
                val providerUnit = providerUnit.copy(
                    contractOrigin = ProviderUnit.Origin.CASSI,
                    type = ProviderUnit.Type.HOSPITAL,
                    medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialtyId = specialty.id))
                )
                val providerDetails = ProviderDetailsTransport(
                    id = providerUnit.id,
                    type = HOSPITAL,
                    title = HOSPITAL.title,
                    name = "Oswaldo Cruz Paulista",
                    header = ProviderDetailsTransport.Header(
                        name = "Oswaldo Cruz Paulista",
                        providerImageUrl = "http://pudim.com.br/logo.png",
                    ),
                    providerImageUrl = "http://pudim.com.br/logo.png",
                    membershipCard = MemberCardResponse(
                        number = cassiMember.accountNumber!!,
                        type = MemberCardType.CASSI,
                        ansNumber = "34665-9",
                        startDate = cassiMember.startDate,
                        expirationDate = cassiMember.expirationDate,
                    ),
                    disclaimer = ContactCallOutResponse(
                        title = "Cobertura Rede Cassi",
                        body = """Entre em contato com esta unidade para conferir se os serviços que você precisa estão disponíveis e, se necessário, informe que seu plano é "Cassi Reciprocidade".""",
                        variant = ContactCallOutResponse.Variant.TUTORIAL
                    ),
                    addresses = listOf(
                        ProviderDetailsTransport.Address(
                            label = "Endereço",
                            address = "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020",
                            distance = null
                        )
                    ),
                    phoneNumbers = listOf(
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Telefone 1",
                            phoneNumber = "(011) 99999-9999",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011999999999"
                        ),
                        ProviderDetailsTransport.PhoneNumber(
                            label = "Secundário",
                            phoneNumber = "(011) 98888-8888",
                            type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                            phoneUrl = "tel:011988888888"
                        )
                    ),
                    favoriteInfo = FavoriteInfoTransportBuilder.build(
                        referenceId = providerUnit.id,
                        specialtyIds = listOf(specialty.id),
                        referenceType = HOSPITAL,
                        isFavorite = false,
                        appVersion = appVersion,
                    )
                )

                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()
                coEvery { providerUnitService.get(providerId) } returns providerUnit.success()
                coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns listOf(healthPlanTask).success()
                coEvery { personService.get(personId) } returns person.success()
                coEvery {
                    accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                        person.id,
                        providerUnit.id,
                    )
                } returns NotFoundException().failure()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = HOSPITAL,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isSuccessWithData(providerDetails)

                coVerifyOnce { personService.get(any()) }
            }

        @Test
        fun `Return error when type is PROVIDER, contract is Cassi and member misses cassiMember`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val memberWithProduct = MemberWithProduct(
                    member = member,
                    product = product
                )
                val providerUnit = providerUnit.copy(
                    contractOrigin = ProviderUnit.Origin.CASSI,
                    type = ProviderUnit.Type.HOSPITAL,
                    medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialtyId = specialty.id))
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery {
                    memberService.findActiveMembershipWithProduct(
                        personId,
                        findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                    )
                } returns memberWithProduct.success()
                coEvery { providerUnitService.get(providerId) } returns providerUnit.success()
                coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
                coEvery {
                    healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
                        person.id,
                        HealthPlanTaskType.REFERRAL
                    )
                } returns listOf(healthPlanTask).success()

                val result = service.getProviderDetails(
                    personId = personId,
                    providerType = HOSPITAL,
                    providerId = providerId,
                    appVersion = appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = specialty.id,
                )

                assertThat(result).isFailureOfType(NotFoundException::class)
            }
    }

    @Nested
    inner class GetAdvancedAccessProviders {

        private val specialty = buildMedicalSpecialty(
            name = "MFC",
        )

        private val referralSpecialty = ReferralSpecialty(name = "MFC", id = specialty.id)

        private val subSpecialty = buildMedicalSpecialty(
            name = "Acesso Avançado",
            isAdvancedAccess = true,
        )

        private val referralSubSpecialty = ReferralSpecialty(name = "Acesso Avançado", id = subSpecialty.id)

        private val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
            specialty = referralSpecialty,
            subSpecialty = referralSubSpecialty,
        )

        private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
            id = RangeUUID.generate(),
            title = "Acesso Avançado",
            category = AppointmentScheduleType.PHYSICIAN_ONSITE,
        )

        private val localDateTime = LocalDateTime.now().atBeginningOfTheDay().plusHours(3)

        @Test
        fun `#Should return ProviderResponse`() = runBlocking {
            mockLocalDateTime(localDateTime) {
                val scheduleUrl = "http:///schedule/advanced-access"

                val expectedTagsTitle = "Próxima data: **Hoje ${localDateTime.toCustomFormat("dd/MM")}**"
                val expectedTags = listOf(
                    Tag(text = localDateTime.plusHours(1).toBrazilianTimeFormat()),
                    Tag(text = localDateTime.plusHours(2).toBrazilianTimeFormat()),
                    Tag(text = localDateTime.plusHours(3).toBrazilianTimeFormat()),
                    Tag(text = localDateTime.plusHours(4).toBrazilianTimeFormat()),
                    Tag(text = "mais"),
                )

                val address = buildStructuredAddress(
                    id = RangeUUID.generate(),
                    latitude = lat,
                    longitude = lng,
                )

                val providerUnit = buildProviderUnit(
                    id = RangeUUID.generate(),
                    name = "Oswaldo Cruz Paulista",
                ).copy(address = address)

                val staffSchedules = listOf(
                    buildStaffSchedule(
                        providerUnitId = providerUnit.id,
                    ),
                    buildStaffSchedule(
                        providerUnitId = RangeUUID.generate(),
                    ),
                    buildStaffSchedule(
                        providerUnitId = RangeUUID.generate(),
                    )
                )

                val specialist1 = ConsolidatedSpecialistLightTransport(
                    id = RangeUUID.generate(),
                    type = SPECIALIST_HEALTH_PROFESSIONAL,
                    name = "Specialist 1",
                    address = buildStructuredAddress(),
                    imageUrl = "http://especialista.com.br/logo.png",
                    distance = distance,
                    appointmentTypes = listOf(ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL),
                    gender = null,
                    specialtyIds = listOf(specialty.id),
                    subSpecialtyIds = listOf(subSpecialty.id),
                    staffId = null,
                )
                val specialist2 = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 2",
                    staffId = null,
                )
                val specialistWithoutSlots = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 3",
                    staffId = null,
                )
                val specialistWithoutStaffId = specialist1.copy(
                    id = RangeUUID.generate(),
                    name = "Specialist 4",
                    staffId = null,
                )

                val staffId1 = RangeUUID.generate()
                val staffId2 = RangeUUID.generate()
                val staffIf3 = RangeUUID.generate()

                val healthProfessionals = listOf(
                    buildHealthProfessional(
                        id = specialist1.id,
                        staffId = staffId1,
                    ),
                    buildHealthProfessional(
                        id = specialist2.id,
                        staffId = staffId2,
                    ),
                    buildHealthProfessional(
                        id = specialistWithoutSlots.id,
                        staffId = staffIf3,
                    ),
                )

                val expectedProviderItems = listOf(
                    ProviderTransport(
                        id = specialist1.id,
                        staffId = staffId1,
                        name = specialist1.name,
                        title = specialist1.name,
                        subtitle = null,
                        description = "**0 m** - ${specialist1.address?.formattedAddress()}",
                        tag = null,
                        tags = expectedTags,
                        tagsTitle = expectedTagsTitle,
                        imageUrl = specialist1.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.WEBVIEW,
                            params = mapOf(
                                "feedback_message" to "Agendamento efetuado com sucesso!",
                                "link" to "http:///schedule/advanced-access",
                                "pop_on_complete" to true,
                                "token" to "true"
                            )
                        ),
                        distance = 0.0,
                        deAccreditationDate = null,
                        captions = emptyList(),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist1.subSpecialtyIds,
                        providerUnitId = providerUnit.id,
                        availabilityInNextDays = listOf("Hoje", "Amanhã", "Depois de amanhã"),
                        scheduleAvailabilityDays = 1,
                    ),
                    ProviderTransport(
                        id = specialist2.id,
                        staffId = staffId2,
                        name = specialist2.name,
                        title = specialist2.name,
                        subtitle = null,
                        description = "**0 m** - ${specialist2.address?.formattedAddress()}",
                        tag = null,
                        tags = expectedTags,
                        tagsTitle = expectedTagsTitle,
                        imageUrl = specialist2.imageUrl,
                        type = SPECIALIST_HEALTH_PROFESSIONAL,
                        navigation = Navigation(
                            mobileRoute = MobileRouting.WEBVIEW,
                            params = mapOf(
                                "feedback_message" to "Agendamento efetuado com sucesso!",
                                "link" to "http:///schedule/advanced-access",
                                "pop_on_complete" to true,
                                "token" to "true"
                            )
                        ),
                        distance = 0.0,
                        deAccreditationDate = null,
                        captions = emptyList(),
                        appointmentTypes = listOf(
                            ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL,
                        ),
                        subSpecialtyIds = specialist2.subSpecialtyIds,
                        providerUnitId = providerUnit.id,
                        availabilityInNextDays = listOf("Hoje", "Amanhã", "Depois de amanhã"),
                        scheduleAvailabilityDays = 1,
                    )
                )

                val expectedResponse = ProviderResponse(
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        title = ADVANCED_ACCESS_EMPTY_LIST_TITLE,
                        description = null,
                        action = talkToAliceAgoraAction,
                    ),
                    action = talkToAliceAgoraAction,
                    providersType = "HEALTH_PROFESSIONAL",
                    providersSubtypeId = specialty.id.toString(),
                    emptyListTitle = ADVANCED_ACCESS_EMPTY_LIST_TITLE,
                    emptyListDescription = null,
                    title = ADVANCED_ACCESS_TITLE,
                    otherSectionTitle = "Outros Especialistas e Clínicas",
                    items = expectedProviderItems,
                    providers = listOf(
                        ProviderSection(
                            title = null,
                            description = null,
                            items = expectedProviderItems,
                            type = ProviderSectionType.STANDARD,
                        )
                    ),
                    isDuquesa = false,
                    availabilityInNextDays = listOf("Hoje", "Amanhã", "Depois de amanhã")
                )
                val appVersion = SemanticVersion("4.0.0")

                coEvery {
                    eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
                } returns listOf(
                    EventTypeProviderUnit(
                        id = RangeUUID.generate(),
                        providerUnitId = providerUnit.id,
                        status = Status.ACTIVE,
                        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    ),
                    EventTypeProviderUnit(
                        id = RangeUUID.generate(),
                        providerUnitId = RangeUUID.generate(),
                        status = Status.ACTIVE,
                        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    )
                ).success()

                coEvery {
                    appointmentScheduleEventTypeService.getBySubSpecialties(
                        listOf(subSpecialty.id),
                        AppointmentScheduleType.PHYSICIAN_ONSITE
                    )
                } returns listOf(appointmentScheduleEventType).success()


                val slots = listOf(
                    localDateTime.plusHours(1),
                    localDateTime.plusHours(2),
                    localDateTime.plusHours(3),
                    localDateTime.plusHours(4),
                    localDateTime.plusHours(5),
                    localDateTime.plusDays(1),
                    localDateTime.plusDays(2),
                )

                coEvery {
                    specialistSchedulingInternalService.getNextNAvailableDatetimeForSpecialistByEventType(
                        staffId1, any(), any(), any(), any()
                    )
                } returns slots.success()

                coEvery {
                    specialistSchedulingInternalService.getNextNAvailableDatetimeForSpecialistByEventType(
                        staffId2, any(), any(), any(), any()
                    )
                } returns slots.success()

                coEvery {
                    specialistSchedulingInternalService.getNextNAvailableDatetimeForSpecialistByEventType(
                        staffIf3, any(), any(), any(), any()
                    )
                } returns emptyList<LocalDateTime>().success()

                coEvery { memberService.getCurrent(member.personId) } returns member.success()

                coEvery { healthPlanTaskService.get(healthPlanTask.id) } returns healthPlanTask.success()

                coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()

                coEvery { medicalSpecialtyService.getById(subSpecialty.id) } returns subSpecialty.success()

                coEvery {
                    consolidatedAccreditedSpecialistNetworkService.getSpecialist(
                        SpecialistRequestByProduct(
                            productId = member.productId,
                            specialtyId = specialty.id,
                            subSpecialtyId = subSpecialty.id,
                            lat = lat,
                            lng = lng,
                            rangeInMeters = 5_000_000,
                        )
                    )
                } returns listOf(
                    specialist1,
                    specialist2,
                    specialistWithoutSlots,
                    specialistWithoutStaffId,
                ).success()

                coEvery { kafkaProducerService.produce(any<MemberAccreditedNetworkTrackerEvent>()) } returns mockk()

                coEvery {
                    healthProfessionalService.getByIds(
                        listOf(
                            specialist1.id,
                            specialist2.id,
                            specialistWithoutSlots.id,
                            specialistWithoutStaffId.id,
                        )
                    )
                } returns healthProfessionals.success()

                coEvery {
                    schedulingUrlBuilder.buildScheduleUrlLight(
                        calendarUrl = null,
                        extras = any(),
                        shouldUseInternalScheduler = true,
                        isAdvancedAccess = true
                    )
                } returns scheduleUrl

                coEvery { staffScheduleService.getStaffSchedules(any()) } returns staffSchedules.success()

                coEvery { providerUnitService.getByIds(any()) } returns listOf(providerUnit).success()

                val result =
                    service.getProvidersByHealthPlanTask(person.id, healthPlanTask.id, lat, lng, appVersion)

                assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { consolidatedAccreditedSpecialistNetworkService.getSpecialist(any()) }
                coVerify(exactly = 3) { medicalSpecialtyService.getById(any()) }
            }
        }
    }
}
