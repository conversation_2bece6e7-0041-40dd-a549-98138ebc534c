package br.com.alice.moneyin.routes

import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.moneyin.consumers.AcquirerPaymentNotificationConsumer
import br.com.alice.moneyin.consumers.BillingAccountablePartyAssignedConsumer
import br.com.alice.moneyin.consumers.CreateExternalPaymentConsumer
import br.com.alice.moneyin.consumers.CreateInvoicePaymentConsumer
import br.com.alice.moneyin.consumers.ExternalInvoiceCreatedConsumer
import br.com.alice.moneyin.consumers.InvoiceConsumer
import br.com.alice.moneyin.consumers.InvoicePaymentConsumer
import br.com.alice.moneyin.consumers.InvoicesNearOverdueJobConsumer
import br.com.alice.moneyin.consumers.MemberCreatedConsumer
import br.com.alice.moneyin.consumers.MemberInvoiceGroupConsumer
import br.com.alice.moneyin.consumers.MemberInvoiceGroupProcessedConsumer
import br.com.alice.moneyin.consumers.NullvsMemberInvoiceGroupCreatedConsumer
import br.com.alice.moneyin.consumers.PaymentNotificationConsumer
import br.com.alice.moneyin.consumers.PersonUpdatedConsumer
import br.com.alice.moneyin.consumers.PreActivationPaymentConsumer
import br.com.alice.moneyin.event.AcquirerPaymentWebhookReceivedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyAssignedEvent
import br.com.alice.moneyin.event.CreateExternalPaymentRequestEvent
import br.com.alice.moneyin.event.CreateInvoicesPaymentRequestEvent
import br.com.alice.moneyin.event.ExternalInvoiceCreatedEvent
import br.com.alice.moneyin.event.InvoicePaidEvent
import br.com.alice.moneyin.event.InvoicePaymentApprovedEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import br.com.alice.moneyin.event.IuguPaymentWebhookReceivedEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupProcessedEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import br.com.alice.moneyin.event.ProcessInvoiceNearOverdueEvent
import br.com.alice.moneyin.event.SendInvoiceNearOverdueEvent
import br.com.alice.nullvs.events.NullvsMemberInvoiceGroupCreatedEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {
    val memberCreatedConsumer by inject<MemberCreatedConsumer>()
    consume(
        "member-created-send-billing-accountable-party-email",
        MemberCreatedEvent.name,
        memberCreatedConsumer::sendBillingAccountablePartyEmail
    )

    val personUpdatedConsumer by inject<PersonUpdatedConsumer>()
    consume(
        "update-billing-accountable-party",
        PersonUpdatedEvent.name,
        personUpdatedConsumer::updateBillingAccountableParty
    )

    val billingAccountablePartyAssignedConsumer by inject<BillingAccountablePartyAssignedConsumer>()
    consume(
        "billing-accountable-party-assigned-send-email",
        BillingAccountablePartyAssignedEvent.name,
        billingAccountablePartyAssignedConsumer::sendEmail
    )

    val invoiceConsumer by inject<InvoiceConsumer>()
    consume("invoice-paid-cancel-pending-payments", InvoicePaidEvent.name, invoiceConsumer::cancelPendingPayments)

    val paymentNotificationsConsumer by inject<PaymentNotificationConsumer>()
    consume(
        "iugu-payment-webhook-received-update-invoice-status",
        IuguPaymentWebhookReceivedEvent.name,
        paymentNotificationsConsumer::updateInvoiceStatusFromIuguPaymentWebhook
    )

    val invoicePaymentConsumer by inject<InvoicePaymentConsumer>()
    consume(
        "invoice-payment-approved-close-invoice",
        InvoicePaymentApprovedEvent.name,
        invoicePaymentConsumer::closeInvoice
    )
    consume(
        "invoice-payment-approved-emit-member-invoice-group",
        InvoicePaymentApprovedEvent.name,
        invoicePaymentConsumer::emitMemberInvoiceGroup
    )
    consume(
        "invoice-payment-created-associate-member-invoice-group",
        InvoicePaymentCreatedEvent.name,
        invoicePaymentConsumer::associateMemberInvoiceGroup
    )
    consume(
        "invoice-payment-approved-emit-invoice-liquidation",
        InvoicePaymentApprovedEvent.name,
        invoicePaymentConsumer::emitInvoiceLiquidation
    )
    consume(
        "invoice-payment-approved-emit-pre-activation-payment",
        InvoicePaymentApprovedEvent.name,
        invoicePaymentConsumer::emitInvoicePreActivationPayment
    )

    val memberInvoiceGroupConsumer by inject<MemberInvoiceGroupConsumer>()
    consume(
        "member-invoice-group-paid-close-invoice",
        MemberInvoiceGroupPaidEvent.name,
        memberInvoiceGroupConsumer::closeInvoiceMemberByMemberInvoiceGroup
    )

    val preActivationPaymentConsumer by inject<PreActivationPaymentConsumer>()
    consume(
        "pre-activation-payment-paid-close-invoice",
        PreActivationPaymentPaidEvent.name,
        preActivationPaymentConsumer::closeInvoiceMemberByPreActivationPayment
    )

    val nullvsMemberInvoiceGroupCreatedConsumer by inject<NullvsMemberInvoiceGroupCreatedConsumer>()
    consume(
        "member-invoice-group-created-create-payment",
        NullvsMemberInvoiceGroupCreatedEvent.name,
        nullvsMemberInvoiceGroupCreatedConsumer::createInvoicePaymentForMemberInvoiceGroup
    )

    val externalInvoiceCreatedConsumer by inject<ExternalInvoiceCreatedConsumer>()
    consume(
        "external-invoice-created-create-payment-detail",
        ExternalInvoiceCreatedEvent.name,
        externalInvoiceCreatedConsumer::createPaymentDetailAndSendEmail
    )
    consume(
        "update-hubspot-with-payment-url",
        ExternalInvoiceCreatedEvent.name,
        externalInvoiceCreatedConsumer::updateHubspotWithPaymentUrl
    )

    val createInvoicePaymentConsumer by inject<CreateInvoicePaymentConsumer>()
    consume(
        "create-invoice-payment-request-create-invoice-payment",
        CreateInvoicesPaymentRequestEvent.name,
        createInvoicePaymentConsumer::processRequest
    )

    val createExternalPaymentConsumer by inject<CreateExternalPaymentConsumer>()
    consume(
        "create-external-payment-request-create-external-invoice",
        CreateExternalPaymentRequestEvent.name,
        createExternalPaymentConsumer::processRequest
    )

    val invoiceNearOverdueConsumer by inject<InvoicesNearOverdueJobConsumer>()
    consume(
        "send-invoice-near-overdue",
        SendInvoiceNearOverdueEvent.name,
        invoiceNearOverdueConsumer::sendInvoicesNearOverdueEvents
    )
    consume(
        "process-invoice-near-overdue",
        ProcessInvoiceNearOverdueEvent.name,
        invoiceNearOverdueConsumer::processInvoiceNearOverdue
    )

    val acquirerPaymentNotificationConsumer by inject<AcquirerPaymentNotificationConsumer>()
    consume(
        "mark-invoice-payment-as-paid-from-acquirer",
        AcquirerPaymentWebhookReceivedEvent.name,
        acquirerPaymentNotificationConsumer::markInvoicePaymentAsPaidFromWebhook
    )

    val memberInvoiceGroupProcessedConsumer by inject<MemberInvoiceGroupProcessedConsumer>()
    consume(
        "update-invoice-payments-with-member-invoice-ids",
        MemberInvoiceGroupProcessedEvent.name,
        memberInvoiceGroupProcessedConsumer::updateInvoicePaymentsWithMemberInvoiceIds
    )
}
