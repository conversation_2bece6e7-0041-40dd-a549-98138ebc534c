package br.com.alice.nullvs.ioc

import TotvsHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.nullvs.SERVICE_NAME
import br.com.alice.nullvs.ServiceConfig
import br.com.alice.nullvs.client.NullvsExternalIdService
import br.com.alice.nullvs.client.NullvsIntegrationLogQueryService
import br.com.alice.nullvs.clients.TotvsClientClient
import br.com.alice.nullvs.clients.TotvsCompanyClient
import br.com.alice.nullvs.clients.TotvsInvoiceClient
import br.com.alice.nullvs.clients.TotvsInvoiceItemClient
import br.com.alice.nullvs.clients.TotvsMemberClient
import br.com.alice.nullvs.clients.TotvsTablePriceClient
import br.com.alice.nullvs.clients.ViaCEPClient
import br.com.alice.nullvs.clients.ViaCEPClientHttpConfig
import br.com.alice.nullvs.consumers.NullvsCreateIntegrationRecordConsumer
import br.com.alice.nullvs.consumers.ProductConsumer
import br.com.alice.nullvs.consumers.client.BillingAccountablePartyConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientBatchRequestConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientBatchResponseConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientNotFoundConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.client.NullvsSyncClientRequestConsumer
import br.com.alice.nullvs.consumers.company.CompanyContractConsumer
import br.com.alice.nullvs.consumers.company.CompanySubContractConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractBatchRequestConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractBatchResponseConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubContractBatchResponseConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubcontractBatchRequestConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubcontractWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.company.NullvsPriceListingWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.invoice.InvoiceConsumer
import br.com.alice.nullvs.consumers.invoice.InvoiceItemConsumer
import br.com.alice.nullvs.consumers.invoice.MemberInvoiceGroupCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.MemberInvoiceGroupCreatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceBatchRequestConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCanceledWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCreatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCreatedWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceLiquidationPaidConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceResponseConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsMemberInvoiceBatchConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupGeneratedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupUpdatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsSyncInvoiceRequestConsumer
import br.com.alice.nullvs.consumers.member.BeneficiaryConsumer
import br.com.alice.nullvs.consumers.member.BeneficiaryForActiveMemberConsumer
import br.com.alice.nullvs.consumers.member.MemberConsumer
import br.com.alice.nullvs.consumers.member.NullvsMemberBatchConsumer
import br.com.alice.nullvs.consumers.member.NullvsMemberWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.member.PersonConsumer
import br.com.alice.nullvs.consumers.member.ProductChangedConsumer
import br.com.alice.nullvs.controllers.BackfillController
import br.com.alice.nullvs.controllers.InvoiceController
import br.com.alice.nullvs.controllers.LiquidationController
import br.com.alice.nullvs.controllers.RecurrentController
import br.com.alice.nullvs.converters.NullvsClientConverterService
import br.com.alice.nullvs.services.NullvsExternalIdServiceImpl
import br.com.alice.nullvs.services.NullvsIntegrationLogQueryServiceImpl
import br.com.alice.nullvs.services.internals.AnsNumberCacheService
import br.com.alice.nullvs.services.internals.CompanyInfoCacheService
import br.com.alice.nullvs.services.internals.NullsIntegrationLogReprocessService
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.TotvsCompanyIntegrationService
import br.com.alice.nullvs.services.internals.TotvsInvoiceItemIntegrationService
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.nullvs.services.internals.TotvsMemberService
import br.com.alice.nullvs.services.internals.TotvsPriceListingService
import br.com.alice.nullvs.services.internals.dependency.ContractDependency
import br.com.alice.nullvs.services.internals.dependency.MemberDependency
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import br.com.alice.nullvs.services.internals.dependency.SubContractDependency
import br.com.alice.nullvs.webhooks.TotvsWebhookReceiver
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    // Configuration
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }
    single<Invoker> { DataLayerClientConfiguration.build() }

    // Client
    val totvsConfig = ServiceConfig.TotvsMember
    single { TotvsMemberClient(totvsConfig.baseURL(), totvsConfig.secretKey(), TotvsHttpClient()) }
    single {
        TotvsClientClient(
            ServiceConfig.TotvsClient.baseURL(),
            ServiceConfig.TotvsClient.secretKey(),
            TotvsHttpClient()
        )
    }
    single {
        TotvsInvoiceClient(
            ServiceConfig.TotvsInvoice.baseURL(),
            ServiceConfig.TotvsInvoice.secretKey(),
            TotvsHttpClient()
        )
    }
    single {
        TotvsInvoiceItemClient(
            ServiceConfig.TotvsInvoiceItem.baseURL(),
            ServiceConfig.TotvsInvoiceItem.secretKey(),
            TotvsHttpClient()
        )
    }
    single {
        ViaCEPClient(
            ViaCEPClientHttpConfig(),
            ServiceConfig.ViaCEP.baseURL(),
        )
    }
    single {
        TotvsCompanyClient(
            ServiceConfig.TotvsCompany.baseURL(),
            ServiceConfig.TotvsCompany.secretKey(),
            TotvsHttpClient(),
        )
    }
    single {
        TotvsTablePriceClient(
            ServiceConfig.TotvsTablePrice.baseURL(),
            ServiceConfig.TotvsTablePrice.secretKey(),
            TotvsHttpClient(),
        )
    }

    // Cache factory
    val cache = CacheFactory.newInstance("business-domain-service-cache")

    // Internal Services
    single { NullvsIntegrationRecordService(get(), get(), get()) }
    single { NullvsIntegrationLogService(get()) }
    single { NullvsClientConverterService(get(), get()) }
    single { TotvsMemberService(get(), get(), get()) }
    single { TotvsPriceListingService(get(), get(), get(), get(), get(), get(), get()) }
    single {
        TotvsMemberIntegrationService(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    single { TotvsCompanyIntegrationService(get(), get(), get(), get(), get(), get()) }
    single { NullsIntegrationLogReprocessService(get(), get(), get(), get(), get(), get()) }
    single { TotvsInvoiceItemIntegrationService(get(), get(), get(), get(), get(), get(), get()) }
    single { AnsNumberCacheService(cache, get()) }
    single { CompanyInfoCacheService(cache, get(), get(), get(), get()) }
    single { NullvsDependencyService(get(), get(), get(), get()) }
    single { MemberDependency(get(), get()) }
    single { ContractDependency(get(), get()) }
    single { SubContractDependency(get(), get()) }

    // Exposed Services
    single<NullvsExternalIdService> { NullvsExternalIdServiceImpl(get()) }
    single<NullvsIntegrationLogQueryService> { NullvsIntegrationLogQueryServiceImpl(get()) }

    loadServiceServers("br.com.alice.nullvs.services")

    // Controllers
    single { HealthController(SERVICE_NAME) }
    single { TotvsWebhookReceiver(get()) }
    single { RecurrentController(get(), get(), get(), get()) }
    single { InvoiceController(get(), get(), get(), get(), get(), get(), get()) }
    single {
        BackfillController(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    single { LiquidationController(get(), get(), get(), get()) }

    // Consumers
    single { NullvsMemberBatchConsumer(get(), get(), get(), get()) }
    single { NullvsMemberWebhookReceivedConsumer(get(), get(), get(), get(), get()) }
    single { NullvsClientWebhookReceivedConsumer(get(), get(), get()) }
    single { BeneficiaryConsumer(get(), get(), get()) }
    single { BeneficiaryForActiveMemberConsumer(get(), get(), get()) }
    single { MemberConsumer(get(), get(), get(), get()) }
    single { MemberInvoiceGroupCreatedConsumer(get()) }
    single { MemberInvoiceGroupCanceledConsumer(get()) }
    single { NullvsClientBatchResponseConsumer(get()) }
    single { NullvsClientNotFoundConsumer(get(), get(), get()) }
    single { NullvsCompanyContractBatchResponseConsumer(get()) }
    single { NullvsInvoiceResponseConsumer(get()) }
    single { BillingAccountablePartyConsumer(get(), get(), get(), get(), get(), get()) }
    single { NullvsPaymentGroupGeneratedConsumer(get(), get(), get()) }
    single { NullvsPaymentGroupUpdatedConsumer(get(), get(), get(), get()) }
    single { NullvsPaymentGroupCanceledConsumer(get(), get()) }
    single { NullvsSyncClientRequestConsumer(get(), get()) }
    single { NullvsClientBatchRequestConsumer(get(), get(), get()) }
    single { NullvsCreateIntegrationRecordConsumer(get(), get(), get(), get()) }
    single { NullvsInvoiceWebhookConsumer(get(), get(), get(), get(), get(), get()) }
    single { NullvsInvoiceBatchRequestConsumer(get(), get(), get()) }
    single { InvoiceConsumer(get(), get(), get(), get()) }
    single { ProductChangedConsumer(get(), get()) }
    single { NullvsPaymentWebhookReceivedConsumer(get()) }
    single { PersonConsumer(get(), get(), get()) }
    single { NullvsSyncInvoiceRequestConsumer(get(), get(), get()) }
    single { NullvsCompanySubContractBatchResponseConsumer(get()) }
    single { CompanyContractConsumer(get(), get(), get()) }
    single { CompanySubContractConsumer(get(), get(), get(), get(), get()) }
    single { NullvsCompanyContractWebhookReceivedConsumer(get(), get(), get(), get(), get()) }
    single { NullvsCompanyContractBatchRequestConsumer(get(), get(), get()) }
    single { NullvsCompanySubcontractWebhookReceivedConsumer(get(), get(), get(), get(), get(), get()) }
    single { NullvsCompanySubcontractBatchRequestConsumer(get(), get(), get()) }
    single { NullvsPriceListingWebhookReceivedConsumer(get(), get(), get(), get(), get()) }
    single { NullvsInvoiceItemCreatedWebhookConsumer(get(), get(), get()) }
    single { NullvsInvoiceItemCanceledWebhookConsumer(get(), get(), get()) }
    single { NullvsInvoiceItemCreatedConsumer(get(), get()) }
    single { NullvsInvoiceItemCanceledConsumer(get(), get()) }
    single { InvoiceItemConsumer(get(), get(), get()) }
    single { NullvsMemberInvoiceBatchConsumer(get(), get()) }
    single { NullvsInvoiceLiquidationPaidConsumer(get(), get()) }
    single { ProductConsumer(get()) }
}
