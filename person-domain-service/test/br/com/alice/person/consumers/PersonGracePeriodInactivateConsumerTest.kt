package br.com.alice.person.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonGracePeriodStatus
import br.com.alice.person.client.PersonGracePeriodService
import br.com.alice.person.events.PersonGracePeriodAddEvent
import br.com.alice.person.events.PersonGracePeriodInactivateEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PersonGracePeriodInactivateConsumerTest: ConsumerTest() {

    private val personGracePeriodService: PersonGracePeriodService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer = PersonGracePeriodInactivateConsumer(
        personGracePeriodService,
        kafkaProducerService,
    )
    private val personId = PersonId()
    private val personGracePeriod = TestModelFactory.buildPersonGracePeriod(
        personId = personId,
        status = PersonGracePeriodStatus.INACTIVE,
    )

    private val event = PersonGracePeriodInactivateEvent(
        personGracePeriod = personGracePeriod,
    )

    @Test
    fun `#inactivate should update the PersonGracePeriod with inactive status and produce PersonGracePeriodAddEvent`() = runBlocking {
        coEvery { personGracePeriodService.update(personGracePeriod) } returns personGracePeriod.success()
        coEvery { kafkaProducerService.produce(any<PersonGracePeriodAddEvent>()) } returns mockk()

        val result = consumer.inactivate(event)
        assertThat(result).isSuccess()

        coVerifyOnce { personGracePeriodService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any<PersonGracePeriodAddEvent>()) }
    }
}
