package br.com.alice.schedule.services

import br.com.alice.common.core.Status
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeModel
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.services.AppointmentScheduleEventTypeModelDataService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeFilters
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.converters.AppointmentScheduleEventTypeWithProviderUnitsConverter
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.converters.toTransport
import br.com.alice.schedule.exceptions.ExistingGenericEventTypeWithSameSubSpecialtyException
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import br.com.alice.schedule.model.events.AppointmentScheduleEventTypeUpdated
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class AppointmentScheduleEventTypeServiceImpl(
    private val dataService: AppointmentScheduleEventTypeModelDataService,
    private val kafkaProducerService: KafkaProducerService,
    private val eventTypeProviderUnitService: EventTypeProviderUnitServiceImpl
) : AppointmentScheduleEventTypeService {

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun findBy(filters: AppointmentScheduleEventTypeFilters): Result<List<AppointmentScheduleEventType>, Throwable> =
        catchResult {
            filters.isValid()

            dataService.find {
                where {
                    basePredicateForFilters()
                        .withFilter(filters.ids) { this.id.inList(it) }
                        .withFilter(filters.statuses) { this.status.inList(it) }
                        .withFilter(filters.title) { this.title.like(it) }
                        .withFilter(filters.searchQuery) { this.searchTokens.search(it) }
                        .withFilter(filters.categories) { this.category.inList(it) }
                        .withFilter(filters.specialtyIds) { this.specialty.inList(it) }
                        .withFilter(filters.subSpecialtyIds) { this.subSpecialtyIds.containsAny(it) }
                        .withFilter(filters.isMultiProfessionalReferral) { this.isMultiProfessionalReferral.eq(it) }!!
                }
                    .let { query ->
                        filters.range?.let { range ->
                            query
                                .offset { range.first }
                                .limit { range.last }
                        } ?: query
                    }
                    .orderBy { createdAt }
                    .sortOrder { filters.sortOrder }
            }
                .mapEach { it.toTransport() }
        }

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun countBy(filters: AppointmentScheduleEventTypeFilters): Result<Int, Throwable> =
        catchResult {
            filters.isValid()

            dataService.count {
                where {
                    basePredicateForFilters()
                        .withFilter(filters.ids) { this.id.inList(it) }
                        .withFilter(filters.statuses) { this.status.inList(it) }
                        .withFilter(filters.title) { this.title.like(it) }
                        .withFilter(filters.searchQuery) { this.searchTokens.search(it) }
                        .withFilter(filters.categories) { this.category.inList(it) }
                        .withFilter(filters.specialtyIds) { this.specialty.inList(it) }
                        .withFilter(filters.subSpecialtyIds) { this.subSpecialtyIds.containsAny(it) }
                        .withFilter(filters.isMultiProfessionalReferral) { this.isMultiProfessionalReferral.eq(it) }!!
                }
            }
        }

    override suspend fun createWithProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ): Result<AppointmentScheduleEventType, Throwable> {
        val subSpecialties = appointmentScheduleEventType.subSpecialtyIds

        return when {
            subSpecialties.isNullOrEmpty() -> addWithProviderUnits(
                appointmentScheduleEventType.toModel(),
                eventTypeProviderUnits
            )

            else -> {
                val isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral
                getContainingAnyOfSubSpecialtyIdsForSameReferralTypeAndModelType(
                    subSpecialtyIds = subSpecialties,
                    isMultiProfessionalReferral = isMultiProfessionalReferral,
                    healthcareModelType = appointmentScheduleEventType.healthcareModelType
                )
                    .flatMap {
                        if (it.isNotEmpty()) {
                            return ExistingGenericEventTypeWithSameSubSpecialtyException().failure()
                        } else {
                            addWithProviderUnits(appointmentScheduleEventType.toModel(), eventTypeProviderUnits)
                        }
                    }
            }
        }.map { it.toTransport() }
    }

    override suspend fun updateWithProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ): Result<AppointmentScheduleEventType, Throwable> =
        dataService.get(appointmentScheduleEventType.id)
            .flatMap { currentAppointmentScheduleEventType ->
                val appointmentScheduleEventTypeToBeUpdated =
                    appointmentScheduleEventType.copy(version = currentAppointmentScheduleEventType.version)
                val isMultiProfessionalReferral = appointmentScheduleEventTypeToBeUpdated.isMultiProfessionalReferral
                val subSpecialties = appointmentScheduleEventTypeToBeUpdated.subSpecialtyIds
                val shouldSkipValidation = appointmentScheduleEventTypeToBeUpdated.status != Status.ACTIVE

                return when {
                    subSpecialties.isNullOrEmpty() || shouldSkipValidation -> updateWithProviderUnitsAndPublishEvent(
                        appointmentScheduleEventTypeToBeUpdated,
                        eventTypeProviderUnits
                    )

                    else ->
                        getContainingAnyOfSubSpecialtyIdsForSameReferralTypeAndModelType(
                            subSpecialtyIds = subSpecialties,
                            isMultiProfessionalReferral = isMultiProfessionalReferral,
                            healthcareModelType = appointmentScheduleEventType.healthcareModelType
                        )
                            .flatMap {
                                val foundAppointmentsScheduleEventType =
                                    it.filter { foundAppointmentScheduleEventType -> foundAppointmentScheduleEventType.id != currentAppointmentScheduleEventType.id }
                                if (foundAppointmentsScheduleEventType.isNotEmpty()) {
                                    return ExistingGenericEventTypeWithSameSubSpecialtyException().failure()
                                } else {
                                    updateWithProviderUnitsAndPublishEvent(
                                        appointmentScheduleEventTypeToBeUpdated,
                                        eventTypeProviderUnits
                                    )
                                }
                            }
                }
            }

    override suspend fun update(appointmentScheduleEventType: AppointmentScheduleEventType) =
        dataService.update(appointmentScheduleEventType.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(AppointmentScheduleEventTypeUpdated(it)) }

    override suspend fun get(id: UUID): Result<AppointmentScheduleEventType, Throwable> =
        dataService.get(id).map { it.toTransport() }

    override suspend fun getWithProviderUnits(id: UUID): Result<AppointmentScheduleEventTypeWithProviderUnits, Throwable> =
        this.get(id)
            .flatMapPair { eventTypeProviderUnitService.getForEventType(it.id) }
            .map { eventTypeProviderUnitsAndEventTypes ->
                val eventTypeProviderUnits = eventTypeProviderUnitsAndEventTypes.first
                val eventType = eventTypeProviderUnitsAndEventTypes.second
                AppointmentScheduleEventTypeWithProviderUnitsConverter.convert(
                    source = eventType,
                    eventTypeProviderUnits = eventTypeProviderUnits
                )
            }

    override suspend fun getActiveByTitle(title: String): Result<List<AppointmentScheduleEventType>, Throwable> =
        dataService.find {
            where {
                this.title.like(title).and(this.status.eq(Status.ACTIVE))
            }
        }.mapEach { it.toTransport() }

    override suspend fun getActiveByIds(ids: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable> =
        dataService.find {
            where {
                this.id.inList(ids) and
                        this.status.eq(Status.ACTIVE)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findByRange(range: IntRange) =
        dataService.find {
            orderBy { this.createdAt }
                .sortOrder { this.asc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun countAll(): Result<Int, Throwable> = dataService.count { all() }

    override suspend fun count(
        searchQuery: String?,
        appointmentScheduleType: AppointmentScheduleType?,
        status: List<Status>?
    ): Result<Int, Throwable> =
        dataService.count {
            where {
                this.title.isNotNull()
                    .andSearch(searchQuery)
                    .andCategoryPredicate(appointmentScheduleType)
                    .andStatusPredicate(status)
            }
        }

    override suspend fun query(
        searchQuery: String?,
        appointmentScheduleType: AppointmentScheduleType?,
        status: List<Status>?,
        range: IntRange
    ): Result<List<AppointmentScheduleEventTypeWithProviderUnits>, Throwable> =
        dataService.find {
            where {
                this.title.isNotNull()
                    .andSearch(searchQuery)
                    .andCategoryPredicate(appointmentScheduleType)
                    .andStatusPredicate(status)
            }
                .offset { range.first }
                .limit { range.count() }
                .orderBy { createdAt }
                .sortOrder { SortOrder.Descending }
        }
            .flatMapPair { appointmentScheduleTypes ->
                val appointmentScheduleTypesIds = appointmentScheduleTypes.map { it.id }.distinct()
                eventTypeProviderUnitService.getForEventTypes(appointmentScheduleTypesIds)
            }
            .map { eventTypesAndProviderUnits ->
                val eventTypeProviderUnits = eventTypesAndProviderUnits.first
                val eventTypes = eventTypesAndProviderUnits.second

                eventTypes.map { appointmentScheduleEventType ->
                    val filteredEventTypeProviderUnits = eventTypeProviderUnits
                        .filter { it.appointmentScheduleEventTypeId == appointmentScheduleEventType.id }

                    AppointmentScheduleEventTypeWithProviderUnitsConverter.convert(
                        source = appointmentScheduleEventType.toTransport(),
                        eventTypeProviderUnits = filteredEventTypeProviderUnits
                    )
                }
            }

    override suspend fun getBySpecialties(specialtyIds: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable> =
        dataService.find {
            where {
                this.specialty.inList(specialtyIds).and(status.eq(Status.ACTIVE))
            }
        }.mapEach { it.toTransport() }

    override suspend fun getBySubSpecialties(
        specialtyIds: List<UUID>,
        category: AppointmentScheduleType?
    ): Result<List<AppointmentScheduleEventType>, Throwable> =
        dataService.find {
            where {
                this.subSpecialtyIds.containsAny(specialtyIds).and(status.eq(Status.ACTIVE))
                    .andCategoryPredicate(category)
            }
        }.mapEach { it.toTransport() }

    override suspend fun getByIds(ids: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable> =
        dataService.find {
            where { this.id.inList(ids) }
        }.mapEach { it.toTransport() }

    private fun Predicate.andCategoryPredicate(category: AppointmentScheduleType?) =
        if (category == null) this
        else this and Predicate.eq(
            AppointmentScheduleEventTypeModelDataService.FieldOptions().category,
            category
        )

    private fun Predicate.andSearch(searchQuery: String?): Predicate =
        if (searchQuery.isNullOrEmpty()) this
        else this and Predicate.search(
            AppointmentScheduleEventTypeModelDataService.FieldOptions().searchTokens,
            searchQuery
        )

    private fun Predicate.andStatusPredicate(statusList: List<Status>?): Predicate =
        if (statusList.isNullOrEmpty()) this
        else this and Predicate.inList(
            AppointmentScheduleEventTypeModelDataService.FieldOptions().status,
            statusList,
        )

    private suspend fun getContainingAnyOfSubSpecialtyIdsForSameReferralTypeAndModelType(
        subSpecialtyIds: List<UUID>,
        isMultiProfessionalReferral: Boolean,
        healthcareModelType: HealthcareModelType
    ): Result<List<AppointmentScheduleEventTypeModel>, Throwable> {
        return dataService.find {
            where {
                this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                        this.status.eq(Status.ACTIVE) and
                        this.isMultiProfessionalReferral.eq(isMultiProfessionalReferral) and
                        this.modelType.eq(healthcareModelType)
            }
        }
    }

    private suspend fun addWithProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventTypeModel,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ) = dataService.add(appointmentScheduleEventType).then {
        if (eventTypeProviderUnits.isNotEmpty()) {
            eventTypeProviderUnitService.associateEventTypeWithProviderUnits(
                appointmentScheduleEventTypeId = it.id,
                existingActiveEventTypeProviderUnits = emptyList(),
                eventTypeProviderUnits = eventTypeProviderUnits
            )
        }
    }

    private suspend fun updateWithProviderUnitsAndPublishEvent(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ) = update(appointmentScheduleEventType)
        .then { updatedAppointmentScheduleEventType ->
            eventTypeProviderUnitService.updateEventTypeProviderUnitsAssociations(
                updatedAppointmentScheduleEventType,
                eventTypeProviderUnits
            )
        }
}
