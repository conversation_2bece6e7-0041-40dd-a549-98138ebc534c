package br.com.alice.staff.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CouncilModel
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.withStaff
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.models.StaffWithHealthProfessional
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthProfessionalServiceImplTest {
    private val healthProfessionalDataService: HealthProfessionalModelDataService = mockk()
    private val staffService: StaffService = mockk()
    private val contactService: ContactServiceImpl = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val healthProfessionalService =
        HealthProfessionalServiceImpl(
            healthProfessionalDataService,
            staffService,
            contactService,
            kafkaProducerService
        )

    private val structuredAddress = TestModelFactory.buildStructuredAddress()
    private val contact = TestModelFactory.buildContact(addressId = structuredAddress.id)
    private val healthProfessional = TestModelFactory.buildHealthProfessional(specialtyId = RangeUUID.generate())
    private val healthProfessionalModel = healthProfessional.toModel()
    private val otherHealthProfessional = TestModelFactory.buildHealthProfessional(specialtyId = RangeUUID.generate())
    private val staff = TestModelFactory.buildStaff(id = healthProfessional.staffId)
    private val staffModel = staff.toModel()
    private val otherStaff = TestModelFactory.buildStaff(id = otherHealthProfessional.staffId)

    @BeforeTest
    fun setup() {
        clearMocks(healthProfessionalDataService)
    }

    @Test
    fun `#get should return health professional by id`() = runBlocking {
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.id.eq(healthProfessional.id) } })
        } returns healthProfessionalModel.success()
        coEvery { staffService.get(healthProfessional.staffId) } returns staff.success()

        val result = healthProfessionalService.get(healthProfessional.id, FindOptions(withStaff = true))

        assertThat(result).isSuccessWithData(healthProfessional.withStaff(staff))
        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { staffService.get(any()) }
    }

    @Test
    fun `#get should return health professional active by id with contacts`() = runBlocking {
        coEvery {
            healthProfessionalDataService.findOne(queryEq {
                where {
                    this.id.eq(healthProfessional.id) and this.status.inList(
                        listOf(SpecialistStatus.ACTIVE)
                    )
                }
            })
        } returns healthProfessionalModel.success()
        coEvery { staffService.get(healthProfessional.staffId) } returns staff.success()
        coEvery {
            contactService.findByIds(
                healthProfessional.contactIds,
                ContactService.FieldOptions(includeAddress = true)
            )
        } returns listOf(contact).success()

        val result = healthProfessionalService.get(
            healthProfessional.id,
            FindOptions(
                withStaff = true,
                withContact = true,
                status = listOf(SpecialistStatus.ACTIVE)
            )
        )
        val expectedResult = healthProfessional.withStaff(staff)
            .copy(contacts = listOf(contact), addressesStructured = emptyList())

        assertThat(result).isSuccessWithData(expectedResult)
        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { staffService.get(any()) }
    }

    @Test
    fun `#getByStaffIds should return health professional list`() = runBlocking {
        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where { this.staffId.inList(listOf(staff.id)) }
                }
            )
        } returns listOf(healthProfessionalModel.withStaff(staffModel)).success()
        coEvery { staffService.findByList(listOf(healthProfessional.staffId)) } returns listOf(staff).success()

        val result = healthProfessionalService.getByStaffIds(listOf(staff.id))

        assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))
        coVerifyOnce { healthProfessionalDataService.find(any()) }
        coVerifyOnce  { staffService.findByList(any()) }
    }

    @Test
    fun `#findByStaffIds should return health professional list`() = runBlocking {
        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where { this.staffId.inList(listOf(staff.id)) }
                }
            )
        } returns listOf(healthProfessionalModel).success()

        val result = healthProfessionalService.findByStaffIds(listOf(staff.id))

        assertThat(result).isSuccessWithData(listOf(healthProfessional))
        coVerifyOnce  { healthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#findByStaffId should returns health professional find by staffId`() = runBlocking {
        val staffId = RangeUUID.generate()
        val healthProfessional = healthProfessional.copy(staffId = staffId)
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.staffId.eq(staffId) } }
            )
        } returns healthProfessional.toModel().success()
        coEvery { staffService.get(staffId) } returns staff.success()

        val result = healthProfessionalService.findByStaffId(
            staffId, FindOptions(withStaff = true, withContact = false)
        )

        assertThat(result).isSuccessWithData(healthProfessional.withStaff(staff))
        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { staffService.get(any()) }
    }


    @Test
    fun `#findByStaffId should returns health professional find by staffId with contact`() = runBlocking {
        val staffId = RangeUUID.generate()
        val address = TestModelFactory.buildStructuredAddress()
        val contact = TestModelFactory.buildContact(addressId = address.id).copy(address = address)
        val healthProfessional = healthProfessional.copy(staffId = staffId, contactIds = listOf(contact.id))
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.staffId.eq(staffId) } }
            )
        } returns healthProfessional.toModel().success()
        coEvery {
            contactService.findByIds(healthProfessional.contactIds, ContactService.FieldOptions(includeAddress = true))
        } returns listOf(contact).success()

        coEvery { staffService.get(staffId) } returns staff.success()

        val result = healthProfessionalService.findByStaffId(
            staffId, FindOptions(withStaff = true, withContact = true)
        )

        assertThat(result).isSuccessWithData(
            healthProfessional.withStaff(staff).copy(addressesStructured = listOf(address), contacts = listOf(contact))
        )
        coVerifyOnce { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce { contactService.findByIds(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#getByUrlSlugAndRoles should return active health professional by name and role with range`() = runBlocking {
        val urlSlug = "url-slug"
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
        coEvery {
            healthProfessionalDataService.findOne(queryEq { where { this.urlSlug.eq(urlSlug) } })
        } returns healthProfessionalModel.success()
        coEvery { staffService.getActiveWithRole(healthProfessional.staffId, roles) } returns staff.success()

        val result = healthProfessionalService.getByUrlSlugAndRoles(urlSlug, roles)

        assertThat(result).isSuccessWithData(healthProfessional.withStaff(staff))
        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { staffService.getActiveWithRole(any(), any()) }
    }

    @Test
    fun `#searchActiveByNameAndRoleWithRange should find health professional with all parameters`() = runBlocking {
        val staffIds = listOf(staff.id)
        val namePrefix = staff.firstName.substring(0, 4)
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
        val types = listOf(StaffType.PITAYA)
        val range = IntRange(0, 2)
        coEvery {
            staffService.searchByNameAndRoleWithRange(staffIds, range, roles, namePrefix, true, types)
        } returns listOf(staff).success()
        coEvery {
            healthProfessionalDataService.find(queryEq { where { this.staffId.inList(staffIds) } })
        } returns listOf(healthProfessionalModel.withStaff(staffModel)).success()

        val result =
            healthProfessionalService.searchByNameAndRoleWithRange(staffIds, range, roles, namePrefix, true, types)

        assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))
        coVerifyOnce  { staffService.searchByNameAndRoleWithRange(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce  { healthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#countActiveByNameAndRoleWithRange should count health professional with all parameters`(): Unit =
        runBlocking {
            val staffIds = listOf(RangeUUID.generate())
            val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
            val namePrefix = staff.firstName.substring(0, 4)
            val types = listOf(StaffType.PITAYA)
            coEvery {
                staffService.countByNameAndRoleWithRange(
                    staffIds,
                    roles,
                    namePrefix,
                    true,
                    types
                )
            } returns 1.success()

            val result = healthProfessionalService.countByNameAndRoleWithRange(staffIds, roles, namePrefix, true, types)

            assertThat(result).isEqualTo(1.success())
        }

    @Test
    fun `#countBySpecialtyIdsAndTiers should count specialists`() = runBlocking {
        val specialtyIdList = listOf(RangeUUID.generate())
        val tiersList = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT)

        coEvery {
            healthProfessionalDataService.count(
                queryEq {
                    where {
                        specialtyId.inList(specialtyIdList).and(this.tier.inList(tiersList))
                    }
                }
            )
        } returns 3.success()

        val result = healthProfessionalService.countBySpecialtyIdsAndTiers(specialtyIdList, tiersList)

        assertThat(result).isSuccessWithData(3)
        coVerifyOnce { healthProfessionalDataService.count(any()) }
    }

    @Test
    fun `#getBySpecialtyIdsAndTiers should get specialists`() = runBlocking {
        val specialtyIdList = listOf(RangeUUID.generate())
        val tiersList = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT)

        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where {
                        specialtyId.inList(specialtyIdList)
                            .and(this.tier.inList(tiersList))
                            .and(showOnApp.eq(true))
                    }
                }
            )
        } returns listOf(healthProfessionalModel).success()

        val result = healthProfessionalService.getBySpecialtyIdsAndTiers(specialtyIdList, tiersList)

        assertThat(result).isSuccessWithData(listOf(healthProfessional))
        coVerifyOnce { healthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#setOnCall returns updated HP when set value to on call`(): Unit = runBlocking {
        val expected = healthProfessional.copy(onCall = true)
        coEvery {
            healthProfessionalDataService.findOne(queryEq { where { this.staffId.eq(staff.id) } })
        } returns healthProfessionalModel.success()
        coEvery {
            healthProfessionalDataService.update(expected.toModel())
        } returns expected.toModel().success()

        val result = healthProfessionalService.setOnCall(staff.id, true)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { healthProfessionalDataService.update(any()) }
        confirmVerified(
            healthProfessionalDataService,
            staffService
        )
    }

    @Test
    fun `#setOnCall return error when an error occurs to set value to on call`(): Unit = runBlocking {
        coEvery {
            healthProfessionalDataService.findOne(queryEq { where { this.staffId.eq(staff.id) } })
        } returns healthProfessionalModel.success()
        coEvery {
            healthProfessionalDataService.update(healthProfessionalModel.copy(onCall = true))
        } returns Exception().failure()

        val result = healthProfessionalService.setOnCall(staff.id, true)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce  { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce  { healthProfessionalDataService.update(any()) }
        confirmVerified(
            healthProfessionalDataService,
            staffService
        )
    }

    @Test
    fun `#findByInternalSpecialties should return health professional by internalSpecialtyId`() = runBlocking {
        val internalSpecialtyIds = listOf(RangeUUID.generate())

        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where {
                        this.internalSpecialtyId.inList(internalSpecialtyIds)
                    }
                }
            )
        } returns listOf(healthProfessionalModel).success()

        coEvery {
            staffService.findActivesById(
                listOf(healthProfessional.staffId)
            )
        } returns listOf(staff).success()

        val result = healthProfessionalService.findByInternalSpecialties(internalSpecialtyIds)

        assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))

        coVerifyOnce  { healthProfessionalDataService.find(any()) }
        coVerifyOnce  { staffService.findActivesById(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findActivesByInternalAndExternalSpecialties should return health professional by SpecialtyIds`() =
        runBlocking {
            val specialtyIds = listOf(RangeUUID.generate())

            coEvery {
                healthProfessionalDataService.find(
                    queryEq {
                        where {
                            this.status.eq(SpecialistStatus.ACTIVE).and(
                                scope(
                                    this.internalSpecialtyId.inList(specialtyIds) or this.specialtyId.inList(
                                        specialtyIds
                                    )
                                )
                            )
                        }
                    }
                )
            } returns listOf(healthProfessionalModel).success()

            coEvery {
                staffService.findActivesById(
                    listOf(healthProfessional.staffId)
                )
            } returns listOf(staff).success()

            val result = healthProfessionalService.findActivesByInternalAndExternalSpecialties(specialtyIds)

            assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))

            coVerifyOnce  { healthProfessionalDataService.find(any()) }
            coVerifyOnce  { staffService.findActivesById(any()) }
        }

    @Test
    fun `#findActivesWithRole should return  active health professional by role`() = runBlocking {
        val roles = listOf(Role.NUTRITIONIST)
        coEvery { staffService.findActivesWithAnyRole(roles) } returns listOf(staff).success()
        coEvery {
            healthProfessionalDataService.find(queryEq { where { this.staffId.inList(listOf(staff.id)) } })
        } returns listOf(healthProfessionalModel).success()

        val result = healthProfessionalService.findActivesWithAnyRole(roles)

        assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))
        coVerifyOnce  { staffService.findActivesWithAnyRole(any()) }
        coVerifyOnce  { healthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#findStaffsWithHealthProfessionalIfExists should return staff with health professional`() = runBlocking {
        val staffIds = listOf(staff.id, otherStaff.id)
        coEvery {
            healthProfessionalDataService.find(
                queryEq { where { this.staffId.inList(staffIds) } }
            )
        } returns listOf(healthProfessionalModel, otherHealthProfessional.toModel()).success()
        coEvery { staffService.findByList(staffIds) } returns listOf(staff, otherStaff).success()

        val result = healthProfessionalService.findStaffsWithHealthProfessionalIfExists(staffIds)

        assertThat(result).isSuccessWithData(
            listOf(
                StaffWithHealthProfessional(staff, healthProfessional),
                StaffWithHealthProfessional(otherStaff, otherHealthProfessional)
            )
        )
        coVerifyOnce  { healthProfessionalDataService.find(any()) }
        coVerifyOnce  { staffService.findByList(any()) }
    }

    @Test
    fun `#findStaffsWithHealthProfessionalIfExists should return staff even if health professional does not exists`() =
        runBlocking {
            val staffIds = listOf(staff.id, otherStaff.id)
            coEvery {
                healthProfessionalDataService.find(
                    queryEq { where { this.staffId.inList(staffIds) } }
                )
            } returns listOf(healthProfessionalModel).success()
            coEvery { staffService.findByList(staffIds) } returns listOf(staff, otherStaff).success()

            val result = healthProfessionalService.findStaffsWithHealthProfessionalIfExists(staffIds)

            assertThat(result).isSuccessWithData(
                listOf(
                    StaffWithHealthProfessional(staff, healthProfessional),
                    StaffWithHealthProfessional(otherStaff, null)
                )
            )
            coVerifyOnce  { healthProfessionalDataService.find(any()) }
            coVerifyOnce  { staffService.findByList(any()) }
        }

    @Test
    fun `#findBySubSpecialtyId should return HealthProfessional when find it`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val subSpecialtyId = healthProfessional.subSpecialtyIds.first()

        coEvery {
            healthProfessionalDataService.findOne(
                queryEq {
                    where {
                        this.subSpecialtyIds.contains(subSpecialtyId)
                    }
                }
            )
        } returns healthProfessional.toModel().success()

        val result = healthProfessionalService.findBySubSpecialtyId(subSpecialtyId)

        assertThat(result).isSuccessWithData(healthProfessional)
    }

    @Test
    fun `#findBySubSpecialtyId should return NotFoundException when not find it`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val subSpecialtyId = healthProfessional.subSpecialtyIds.first()

        coEvery {
            healthProfessionalDataService.findOne(
                queryEq {
                    where {
                        this.subSpecialtyIds.contains(subSpecialtyId)
                    }
                }
            )
        } returns NotFoundException().failure()

        val result = healthProfessionalService.findBySubSpecialtyId(subSpecialtyId)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#existsByCouncil should return true if exists health professional by council`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val council = healthProfessional.council

        coEvery {
            healthProfessionalDataService.exists(
                queryEq {
                    where {
                        this.council.eq(CouncilModel(council.number, council.state))
                    }
                }
            )
        } returns true.success()

        val result = healthProfessionalService.existsByCouncil(council)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthProfessionalDataService.exists(any()) }
    }

    @Test
    fun `#getByIds should return a list of health professionals by ids`() = runBlocking {
        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where { this.id.inList(listOf(healthProfessional.id)) }
                }
            )
        } returns listOf(healthProfessionalModel.withStaff(staffModel)).success()
        coEvery { staffService.findByList(listOf(healthProfessional.staffId)) } returns listOf(staff).success()

        val result = healthProfessionalService.getByIds(listOf(healthProfessional.id))

        assertThat(result).isSuccessWithData(listOf(healthProfessional.withStaff(staff)))
        coVerifyOnce  { healthProfessionalDataService.find(any()) }
        coVerifyOnce  { staffService.findByList(any()) }
    }


    @Test
    fun `#inactivateById should inactive health professional`() = runBlocking {
        coEvery { healthProfessionalDataService.get(healthProfessional.id) } returns healthProfessionalModel.success()
        coEvery {
            healthProfessionalDataService.update(
                healthProfessionalModel.copy(status = SpecialistStatus.INACTIVE)
            )
        } returns healthProfessionalModel.success()
        coEvery { kafkaProducerService.produce(HealthProfessionalUpdatedEvent(healthProfessional.id)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                match {
                    if (it is HealthProfessionalChangedEvent) {
                        it.healthProfessional == healthProfessional && it.eventAction == NotificationEventAction.UPDATED
                    } else false
                }
            )
        } returns mockk()

        val result = healthProfessionalService.inactivateById(healthProfessional.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthProfessionalDataService.get(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#inactivateById should return false when not found health professional`() = runBlocking {
        coEvery { healthProfessionalDataService.get(healthProfessional.id) } returns NotFoundException().failure()

        val result = healthProfessionalService.inactivateById(healthProfessional.id)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthProfessionalDataService.get(any()) }
        coVerifyNone { healthProfessionalDataService.update(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#changeShowOnApp should update health professional`() = runBlocking {
        coEvery { healthProfessionalDataService.get(healthProfessional.id) } returns healthProfessionalModel.success()
        coEvery {
            healthProfessionalDataService.update(
                healthProfessionalModel.copy(showOnApp = true)
            )
        } returns healthProfessionalModel.success()
        coEvery { kafkaProducerService.produce(HealthProfessionalUpdatedEvent(healthProfessional.id)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                match {
                    if (it is HealthProfessionalChangedEvent) {
                        it.healthProfessional == healthProfessional && it.eventAction == NotificationEventAction.UPDATED
                    } else false
                }
            )
        } returns mockk()

        val result = healthProfessionalService.changeShowOnApp(healthProfessional.id, true)
        assertThat(result).isSuccessWithData(healthProfessional.copy(showOnApp = true))

        coVerifyOnce { healthProfessionalDataService.get(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#changeShowOnApp should return false when not found health professional`() = runBlocking {
        coEvery { healthProfessionalDataService.get(healthProfessional.id) } returns NotFoundException().failure()

        val result = healthProfessionalService.changeShowOnApp(healthProfessional.id, true)
        assertThat(result).isFailure()

        coVerifyOnce { healthProfessionalDataService.get(any()) }
        coVerifyNone { healthProfessionalDataService.update(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#getByIdsAndSpecialtyIds should return staff with health professional`() = runBlocking {
        val staffIds = listOf(staff.id, otherStaff.id)
        val healthProfessionalList = listOf(healthProfessionalModel, otherHealthProfessional.toModel())
        val specialtyIdList = healthProfessionalList.map { it.specialtyId!! }

        coEvery {
            healthProfessionalDataService.find(
                queryEq {
                    where {
                        this.staffId.inList(staffIds)
                            .and(this.specialtyId.inList(specialtyIdList))
                    }
                }
            )
        } returns healthProfessionalList.success()
        coEvery { staffService.findByList(staffIds) } returns listOf(staff, otherStaff).success()

        val result = healthProfessionalService.getByIdsAndSpecialtyIds(staffIds, specialtyIdList)

        assertThat(result).isSuccessWithData(
            listOf(
                StaffWithHealthProfessional(staff, healthProfessional),
                StaffWithHealthProfessional(otherStaff, otherHealthProfessional)
            )
        )
        coVerifyOnce { healthProfessionalDataService.find(any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#getByRange should return health professional`() = runBlocking {
        val range = IntRange(0, 2)

        coEvery {
            healthProfessionalDataService.find(queryEq {
                offset { range.first }
                    .limit { range.count() }
                    .orderBy { createdAt }
            })
        } returns listOf(healthProfessionalModel).success()

        val result = healthProfessionalService.getByRange(range)
        assertThat(result).isSuccessWithData(listOf(healthProfessional))

        coVerifyOnce { healthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#updateVacation should update health professional vacation`() = runBlocking {
        val startDate = LocalDateTime.now()
        val endDate = LocalDateTime.now()
        coEvery { healthProfessionalDataService.get(healthProfessional.id) } returns healthProfessionalModel.success()
        coEvery {
            healthProfessionalDataService.update(
                healthProfessionalModel.copy(
                    onVacationStart = startDate,
                    onVacationUntil = endDate
                )
            )
        } returns healthProfessionalModel.success()
        coEvery { kafkaProducerService.produce(HealthProfessionalUpdatedEvent(healthProfessionalModel.toTransport().id)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                match {
                    if (it is HealthProfessionalChangedEvent) {
                        it.healthProfessional == healthProfessional && it.eventAction == NotificationEventAction.UPDATED
                    } else false
                }
            )
        } returns mockk()

        val result = healthProfessionalService.updateVacation(healthProfessional.id, startDate, endDate)
        assertThat(result).isSuccessWithData(healthProfessional)

        coVerifyOnce { healthProfessionalDataService.get(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }
}
